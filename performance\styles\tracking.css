/* Performance Tracking Styles */

.performance-tracking {
  background-color: var(--performance-bg, rgba(30, 30, 40, 0.8));
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.performance-tracking-summary {
  display: flex;
  justify-content: space-around;
  margin-bottom: 16px;
}

.tracking-stat {
  text-align: center;
  padding: 8px;
}

.tracking-stat-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--performance-primary, #4a90e2);
}

.tracking-stat-label {
  font-size: 14px;
  color: var(--performance-text, #e0e0e0);
  margin-top: 4px;
}

.performance-tracking-progress {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.tracking-progress-bar {
  flex-grow: 1;
  height: 8px;
  background-color: var(--performance-light, rgba(255, 255, 255, 0.1));
  border-radius: 4px;
  overflow: hidden;
  margin-right: 10px;
}

.tracking-progress {
  height: 100%;
  background-color: var(--performance-primary, #4a90e2);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.tracking-progress-text {
  font-size: 14px;
  font-weight: bold;
  color: var(--performance-text, #e0e0e0);
  min-width: 40px;
  text-align: right;
}

.performance-tracking-toggle {
  text-align: center;
  margin-bottom: 16px;
}

.tracking-toggle-btn {
  background-color: var(--performance-secondary, #2c3e50);
  color: var(--performance-text, #e0e0e0);
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.tracking-toggle-btn:hover {
  background-color: var(--performance-secondary-hover, #34495e);
}

.performance-tracking-detailed {
  background-color: var(--performance-bg-light, rgba(40, 40, 50, 0.6));
  border-radius: 6px;
  padding: 16px;
  animation: fadeIn 0.3s ease;
}

.report-section {
  margin-bottom: 20px;
}

.performance-tracking-detailed h3 {
  font-size: 18px;
  margin-top: 0;
  margin-bottom: 16px;
  color: var(--performance-text, #e0e0e0);
  text-align: center;
}

.performance-tracking-detailed h4 {
  font-size: 16px;
  margin-top: 0;
  margin-bottom: 12px;
  color: var(--performance-text, #e0e0e0);
}

.report-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 16px;
}

.report-stat {
  text-align: center;
  padding: 8px;
}

.report-stat-value {
  font-size: 20px;
  font-weight: bold;
  color: var(--performance-primary, #4a90e2);
}

.report-stat-label {
  font-size: 12px;
  color: var(--performance-text, #e0e0e0);
  margin-top: 4px;
}

.report-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.report-table th,
.report-table td {
  padding: 8px;
  text-align: center;
  border-bottom: 1px solid var(--performance-border, rgba(255, 255, 255, 0.1));
}

.report-table th {
  font-weight: bold;
  color: var(--performance-text, #e0e0e0);
  background-color: var(--performance-bg-dark, rgba(20, 20, 30, 0.8));
}

.report-table td {
  color: var(--performance-text-light, #c0c0c0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .performance-tracking-summary {
    flex-wrap: wrap;
  }
  
  .tracking-stat {
    flex: 1 0 30%;
    margin-bottom: 10px;
  }
  
  .report-stats {
    flex-wrap: wrap;
  }
  
  .report-stat {
    flex: 1 0 30%;
    margin-bottom: 10px;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .performance-tracking {
    --performance-bg: rgba(30, 30, 40, 0.8);
    --performance-bg-light: rgba(40, 40, 50, 0.6);
    --performance-bg-dark: rgba(20, 20, 30, 0.8);
    --performance-text: #e0e0e0;
    --performance-text-light: #c0c0c0;
    --performance-primary: #4a90e2;
    --performance-secondary: #2c3e50;
    --performance-secondary-hover: #34495e;
    --performance-light: rgba(255, 255, 255, 0.1);
    --performance-border: rgba(255, 255, 255, 0.1);
  }
}

/* Light mode adjustments */
@media (prefers-color-scheme: light) {
  .performance-tracking {
    --performance-bg: rgba(240, 240, 245, 0.9);
    --performance-bg-light: rgba(230, 230, 235, 0.7);
    --performance-bg-dark: rgba(210, 210, 220, 0.8);
    --performance-text: #333;
    --performance-text-light: #555;
    --performance-primary: #2980b9;
    --performance-secondary: #7f8c8d;
    --performance-secondary-hover: #95a5a6;
    --performance-light: rgba(0, 0, 0, 0.1);
    --performance-border: rgba(0, 0, 0, 0.1);
  }
}
