/**
 * performance-animations.js
 * Animations for the performance-based question system
 */

const PerformanceAnimations = (function() {
  /**
   * Animate an element with a fade-in effect
   * @param {HTMLElement} element - The element to animate
   * @param {number} duration - Animation duration in milliseconds
   * @returns {Promise} - Promise that resolves when animation completes
   */
  function fadeIn(element, duration = 300) {
    return new Promise(resolve => {
      if (!element) {
        resolve();
        return;
      }

      // Set initial styles
      element.style.opacity = '0';
      element.style.display = '';
      element.style.transition = `opacity ${duration}ms ease`;

      // Force reflow
      void element.offsetWidth;

      // Start animation
      element.style.opacity = '1';

      // Resolve when animation completes
      setTimeout(() => {
        element.style.transition = '';
        resolve();
      }, duration);
    });
  }

  /**
   * Animate an element with a fade-out effect
   * @param {HTMLElement} element - The element to animate
   * @param {number} duration - Animation duration in milliseconds
   * @returns {Promise} - Promise that resolves when animation completes
   */
  function fadeOut(element, duration = 300) {
    return new Promise(resolve => {
      if (!element) {
        resolve();
        return;
      }

      // Set initial styles
      element.style.opacity = '1';
      element.style.transition = `opacity ${duration}ms ease`;

      // Force reflow
      void element.offsetWidth;

      // Start animation
      element.style.opacity = '0';

      // Resolve when animation completes
      setTimeout(() => {
        element.style.display = 'none';
        element.style.transition = '';
        resolve();
      }, duration);
    });
  }

  /**
   * Animate an element with a slide-down effect
   * @param {HTMLElement} element - The element to animate
   * @param {number} duration - Animation duration in milliseconds
   * @returns {Promise} - Promise that resolves when animation completes
   */
  function slideDown(element, duration = 300) {
    return new Promise(resolve => {
      if (!element) {
        resolve();
        return;
      }

      // Set initial styles
      element.style.height = '0';
      element.style.overflow = 'hidden';
      element.style.display = '';
      element.style.transition = `height ${duration}ms ease`;

      // Force reflow
      void element.offsetWidth;

      // Get target height
      const targetHeight = element.scrollHeight;

      // Start animation
      element.style.height = `${targetHeight}px`;

      // Resolve when animation completes
      setTimeout(() => {
        element.style.height = '';
        element.style.overflow = '';
        element.style.transition = '';
        resolve();
      }, duration);
    });
  }

  /**
   * Animate an element with a slide-up effect
   * @param {HTMLElement} element - The element to animate
   * @param {number} duration - Animation duration in milliseconds
   * @returns {Promise} - Promise that resolves when animation completes
   */
  function slideUp(element, duration = 300) {
    return new Promise(resolve => {
      if (!element) {
        resolve();
        return;
      }

      // Set initial styles
      const height = element.offsetHeight;
      element.style.height = `${height}px`;
      element.style.overflow = 'hidden';
      element.style.transition = `height ${duration}ms ease`;

      // Force reflow
      void element.offsetWidth;

      // Start animation
      element.style.height = '0';

      // Resolve when animation completes
      setTimeout(() => {
        element.style.display = 'none';
        element.style.height = '';
        element.style.overflow = '';
        element.style.transition = '';
        resolve();
      }, duration);
    });
  }

  /**
   * Create a confetti effect
   * @param {HTMLElement} container - Container element for the confetti
   * @param {number} particleCount - Number of confetti particles
   * @param {number} duration - Animation duration in milliseconds
   */
  function confetti(container, particleCount = 50, duration = 1500) {
    if (!container) return;

    // Create confetti container
    const confettiContainer = document.createElement('div');
    confettiContainer.className = 'performance-confetti-container';
    confettiContainer.style.position = 'absolute';
    confettiContainer.style.top = '0';
    confettiContainer.style.left = '0';
    confettiContainer.style.width = '100%';
    confettiContainer.style.height = '100%';
    confettiContainer.style.pointerEvents = 'none';
    confettiContainer.style.overflow = 'hidden';
    confettiContainer.style.zIndex = '1000';

    // Add to container
    container.style.position = 'relative';
    container.appendChild(confettiContainer);

    // Create confetti particles
    const colors = ['#f94144', '#f3722c', '#f8961e', '#f9c74f', '#90be6d', '#43aa8b', '#577590'];
    
    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'performance-confetti-particle';
      
      // Random styles
      const size = Math.random() * 10 + 5;
      const color = colors[Math.floor(Math.random() * colors.length)];
      const left = Math.random() * 100;
      const animationDuration = (Math.random() * 3 + 2) * 1000;
      
      // Apply styles
      particle.style.position = 'absolute';
      particle.style.width = `${size}px`;
      particle.style.height = `${size}px`;
      particle.style.backgroundColor = color;
      particle.style.borderRadius = '50%';
      particle.style.top = '-20px';
      particle.style.left = `${left}%`;
      particle.style.transform = 'translateZ(0)';
      particle.style.animation = `performance-confetti-fall ${animationDuration}ms linear forwards`;
      
      // Add to container
      confettiContainer.appendChild(particle);
    }

    // Add keyframes if not already added
    if (!document.getElementById('performance-confetti-keyframes')) {
      const style = document.createElement('style');
      style.id = 'performance-confetti-keyframes';
      style.textContent = `
        @keyframes performance-confetti-fall {
          0% {
            transform: translateY(0) rotate(0deg);
            opacity: 1;
          }
          100% {
            transform: translateY(${container.offsetHeight}px) rotate(720deg);
            opacity: 0;
          }
        }
      `;
      document.head.appendChild(style);
    }

    // Remove confetti container after animation
    setTimeout(() => {
      confettiContainer.remove();
    }, duration);
  }

  /**
   * Highlight an element
   * @param {HTMLElement} element - The element to highlight
   * @param {string} color - Highlight color
   * @param {number} duration - Animation duration in milliseconds
   * @returns {Promise} - Promise that resolves when animation completes
   */
  function highlight(element, color = '#ffeb3b', duration = 1000) {
    return new Promise(resolve => {
      if (!element) {
        resolve();
        return;
      }

      // Save original background
      const originalBackground = element.style.backgroundColor;
      const originalTransition = element.style.transition;

      // Set highlight
      element.style.backgroundColor = color;
      element.style.transition = `background-color ${duration}ms ease`;

      // Force reflow
      void element.offsetWidth;

      // Animate back to original
      setTimeout(() => {
        element.style.backgroundColor = originalBackground;
      }, 100);

      // Resolve when animation completes
      setTimeout(() => {
        element.style.transition = originalTransition;
        resolve();
      }, duration);
    });
  }

  /**
   * Shake an element
   * @param {HTMLElement} element - The element to shake
   * @param {number} duration - Animation duration in milliseconds
   * @returns {Promise} - Promise that resolves when animation completes
   */
  function shake(element, duration = 500) {
    return new Promise(resolve => {
      if (!element) {
        resolve();
        return;
      }

      // Add keyframes if not already added
      if (!document.getElementById('performance-shake-keyframes')) {
        const style = document.createElement('style');
        style.id = 'performance-shake-keyframes';
        style.textContent = `
          @keyframes performance-shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
          }
        `;
        document.head.appendChild(style);
      }

      // Save original transform
      const originalTransform = element.style.transform;

      // Apply animation
      element.style.animation = `performance-shake ${duration}ms ease`;

      // Resolve when animation completes
      setTimeout(() => {
        element.style.animation = '';
        element.style.transform = originalTransform;
        resolve();
      }, duration);
    });
  }

  // Public API
  return {
    fadeIn,
    fadeOut,
    slideDown,
    slideUp,
    confetti,
    highlight,
    shake
  };
})();

// Export for use in other modules
window.PerformanceAnimations = PerformanceAnimations;
