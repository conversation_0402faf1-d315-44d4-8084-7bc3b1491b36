/* Matching Categories Question Type Styles */

.matching-categories-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.categories-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.category-drop-area {
  border: 1px solid var(--performance-border, #dee2e6);
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: var(--performance-card-bg, #ffffff);
  box-shadow: 0 0.125rem 0.25rem var(--performance-shadow, rgba(0, 0, 0, 0.1));
}

.category-header {
  background-color: var(--performance-target-bg, #f1f3f5);
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--performance-border, #dee2e6);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.category-header i {
  font-size: 1.25rem;
  color: var(--performance-primary, #0d6efd);
}

.category-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.category-item-drop-zone {
  min-height: 8rem;
  padding: 0.75rem;
  background-color: var(--performance-drop-zone-bg, #e9ecef);
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.category-item-drop-zone.over {
  background-color: var(--performance-drop-zone-hover, #ced4da);
  border: 2px dashed var(--performance-primary, #0d6efd);
}

.items-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.75rem;
  padding: 1rem;
  background-color: var(--performance-light, #f8f9fa);
  border-radius: 0.5rem;
  border: 1px solid var(--performance-border, #dee2e6);
}

.scenario-item {
  background-color: var(--performance-item-bg, #e9ecef);
  border-radius: 0.25rem;
  padding: 0.75rem 1rem;
  cursor: grab;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  user-select: none;
  box-shadow: 0 0.125rem 0.25rem var(--performance-shadow, rgba(0, 0, 0, 0.1));
}

.scenario-item:hover {
  background-color: var(--performance-item-hover, #ced4da);
  transform: translateY(-2px);
}

.scenario-item.dragging {
  opacity: 0.5;
}

.scenario-item i {
  font-size: 1.25rem;
  color: var(--performance-primary, #0d6efd);
}

.scenario-item span {
  flex: 1;
}

.correct-answer {
  border: 2px solid var(--performance-success, #198754) !important;
  background-color: var(--performance-correct, #d1e7dd) !important;
}

.incorrect-answer {
  border: 2px solid var(--performance-danger, #dc3545) !important;
  background-color: var(--performance-incorrect, #f8d7da) !important;
}

.missing-items-label {
  margin-top: 0.75rem;
  padding: 0.5rem;
  background-color: var(--performance-warning, #fff3cd);
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .categories-container {
    grid-template-columns: 1fr;
  }
  
  .items-container {
    grid-template-columns: 1fr;
  }
}

/* Dark mode adjustments */
.dark-mode .category-drop-area {
  background-color: var(--performance-dark-card-bg, #2d3748);
  border-color: var(--performance-dark-border, #4a5568);
}

.dark-mode .category-header {
  background-color: var(--performance-dark-target-bg, #2d3748);
  border-color: var(--performance-dark-border, #4a5568);
}

.dark-mode .category-item-drop-zone {
  background-color: var(--performance-dark-drop-zone-bg, #1a202c);
}

.dark-mode .category-item-drop-zone.over {
  background-color: var(--performance-dark-drop-zone-hover, #2d3748);
}

.dark-mode .items-container {
  background-color: var(--performance-dark-light, #1a202c);
  border-color: var(--performance-dark-border, #4a5568);
}

.dark-mode .scenario-item {
  background-color: var(--performance-dark-item-bg, #2d3748);
  color: var(--performance-dark-text, #e2e8f0);
}

.dark-mode .scenario-item:hover {
  background-color: var(--performance-dark-item-hover, #4a5568);
}

.dark-mode .correct-answer {
  background-color: var(--performance-dark-correct, #1c4532) !important;
  border-color: var(--performance-dark-success, #38a169) !important;
}

.dark-mode .incorrect-answer {
  background-color: var(--performance-dark-incorrect, #742a2a) !important;
  border-color: var(--performance-dark-danger, #e53e3e) !important;
}

.dark-mode .missing-items-label {
  background-color: var(--performance-dark-warning, #744210);
  color: var(--performance-dark-text, #e2e8f0);
}
