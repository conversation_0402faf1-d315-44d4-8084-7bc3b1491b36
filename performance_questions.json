[{"id": "match_security_locations", "type": "matching", "difficulty": "medium", "category": "A", "tags": ["security controls", "physical security", "access control"], "questionText": "The security team at a manufacturing company is creating a set of security standards for employees and visitors. Select the BEST security control for each location. All of the available security controls will be used once.", "timeLimit": 180, "points": 10, "explanation": "Security outside of the building is focused on the safety of employees and visitors as they park their vehicles or are dropped off at the entrance. The parking lot and exterior building areas should be surrounded by fencing to control access and the parking lot should be well-lit at all times. The reception area is the first interaction with employees or visitors. Security guards should be available to check the authorization of anyone entering the building, and the use of an access control vestibule can help manage the flow of individuals through this checkpoint. Once inside, many areas of the building are readily available to employees and visitors. However, some areas of the building containing sensitive information may require additional authorization. To gain access to the data center from inside of the building, an individual would need to provide a valid access badge and perform a biometric check of their fingerprint, handprint, or a similar type of authentication factor. Gaining access through the door of the data center doesn't provide any access to the server data. If a technician needs console access to a server, they'll need to provide the proper username, password, and authentication token. This multi-factor authentication ensures only authorized users are able to gain access to the information contained on the server.", "hint": "Consider the security requirements and sensitivity of each location.", "matchingData": {"items": [{"id": "access_badge", "text": "Access badge", "icon": "fa-solid fa-id-card"}, {"id": "fencing", "text": "Fencing", "icon": "fa-solid fa-fence"}, {"id": "vestibule", "text": "Access control vestibule", "icon": "fa-solid fa-door-closed"}, {"id": "guard", "text": "Security guard", "icon": "fa-solid fa-user-shield"}, {"id": "token", "text": "Authentication token", "icon": "fa-solid fa-key"}, {"id": "biometrics", "text": "Biometrics", "icon": "fa-solid fa-fingerprint"}, {"id": "lighting", "text": "Lighting", "icon": "fa-solid fa-lightbulb"}], "targets": [{"id": "outside", "text": "Outside Building", "description": "Parking and Visitor drop-off", "icon": "fa-regular fa-building"}, {"id": "reception", "text": "Reception", "description": "Building lobby", "icon": "fa-solid fa-users"}, {"id": "data_center", "text": "Data Center Door", "description": "Entrance from inside building", "icon": "fa-solid fa-door-closed"}, {"id": "server_admin", "text": "Server Administration", "description": "Authentication to server console in the data center", "icon": "fa-solid fa-desktop"}], "correctMatches": {"outside": ["fencing", "lighting"], "reception": ["guard", "vestibule"], "data_center": ["access_badge", "biometrics"], "server_admin": ["token"]}, "allowMultiple": true, "requireAll": true}, "created": "2023-07-15T12:00:00Z", "lastModified": "2023-07-15T12:00:00Z", "version": 1}, {"id": "match_security_categories", "type": "matching_categories", "difficulty": "easy", "category": "A", "tags": ["security controls", "operational security", "managerial security", "physical security", "technical security"], "questionText": "Select the most appropriate security category. Some categories may be used more than once.", "timeLimit": 120, "points": 8, "explanation": "Operational controls are often implemented by people instead of systems. Security guards and awareness programs are examples of an operational control. Managerial controls are administrative controls associated with security design and implementation. A set of policies and procedures would be an example of a managerial control. Physical controls are used to limit physical access. Badge readers, fences, and guard shacks are categorized as physical controls. Technical controls are implemented using systems. Operating system controls, firewalls, and automated processes are considered technical controls.", "hint": "Consider whether the control involves people, policies, physical barriers, or technology.", "matchingCategoriesData": {"categories": [{"id": "operational", "text": "Operational"}, {"id": "managerial", "text": "Managerial"}, {"id": "physical", "text": "Physical"}, {"id": "technical", "text": "Technical"}], "items": [{"id": "guard_check", "text": "A guard checks the identification of all visitors", "icon": "fa-solid fa-id-badge"}, {"id": "vp_approval", "text": "All returns must be approved by a Vice President", "icon": "fa-solid fa-stamp"}, {"id": "generator_power", "text": "A generator is used during a power outage", "icon": "fa-solid fa-plug"}, {"id": "door_access_card", "text": "Building doors can be unlocked with an access card", "icon": "fa-solid fa-door-open"}, {"id": "log_transfer", "text": "System logs are transferred automatically to a SIEM", "icon": "fa-solid fa-file-export"}], "correctMatches": {"guard_check": "operational", "vp_approval": "managerial", "generator_power": "physical", "door_access_card": "physical", "log_transfer": "technical"}}, "created": "2023-07-15T12:00:00Z", "lastModified": "2023-07-15T12:00:00Z", "version": 1}, {"id": "match_attack_types", "type": "matching", "difficulty": "medium", "category": "A", "tags": ["attack types", "security threats", "cybersecurity"], "questionText": "Match the description with the most accurate attack type. Not all attack types will be used.", "timeLimit": 150, "points": 10, "explanation": "Understanding different attack types is crucial for implementing appropriate security controls. Vishing involves using voice communication (phone calls) to trick victims into revealing sensitive information. Injection attacks involve inserting malicious code into data inputs (like web forms) to manipulate a database or application. An On-path attack (formerly Man-in-the-Middle) intercepts communication between two parties without their knowledge. A Distributed Denial-of-Service (DDoS) attack overwhelms a target system with traffic from multiple sources, making it unavailable. A keylogger records keystrokes on a compromised computer, often to steal login credentials or other sensitive data.", "hint": "Focus on the primary method or technique used in each attack scenario.", "matchingData": {"items": [{"id": "onpath", "text": "On-path", "icon": "fa-solid fa-route"}, {"id": "keylogger", "text": "Keylogger", "icon": "fa-solid fa-keyboard"}, {"id": "rootkit", "text": "Rootkit", "icon": "fa-solid fa-bug"}, {"id": "injection", "text": "Injection", "icon": "fa-solid fa-syringe"}, {"id": "rfid", "text": "RFID cloning", "icon": "fa-solid fa-clone"}, {"id": "vishing", "text": "Vishing", "icon": "fa-solid fa-phone"}, {"id": "ddos", "text": "DDoS", "icon": "fa-solid fa-network-wired"}, {"id": "supply", "text": "Supply chain", "icon": "fa-solid fa-truck"}], "targets": [{"id": "vishing_scenario", "text": "Attacker obtains bank account number and birth date by calling the victim", "icon": "fa-solid fa-phone-volume"}, {"id": "injection_scenario", "text": "Attacker accesses a database directly from a web browser", "icon": "fa-solid fa-laptop-code"}, {"id": "onpath_scenario", "text": "Attacker intercepts all communication between a client and a web server", "icon": "fa-solid fa-arrows-left-right-to-line"}, {"id": "ddos_scenario", "text": "Multiple attackers overwhelm a web server", "icon": "fa-solid fa-server"}, {"id": "keylogger_scenario", "text": "Attacker obtains a list of all login credentials used over the last 24 hours", "icon": "fa-solid fa-keyboard"}], "correctMatches": {"vishing_scenario": ["vishing"], "injection_scenario": ["injection"], "onpath_scenario": ["onpath"], "ddos_scenario": ["ddos"], "keylogger_scenario": ["keylogger"]}, "allowMultiple": false, "requireAll": true}, "created": "2023-07-15T12:00:00Z", "lastModified": "2023-07-15T12:00:00Z", "version": 1}, {"id": "match_auth_factors", "type": "matching", "difficulty": "easy", "category": "A", "tags": ["authentication", "security factors", "access control"], "questionText": "Match the appropriate authentication factor to each description. Each authentication factor will be used once.", "timeLimit": 120, "points": 8, "explanation": "Authentication factors are important to consider when developing applications or designing network infrastructures. It's useful to know each authentication factor and some examples of how that factor can be applied during the authentication process. 'Something you know' refers to information only the user should know, like passwords or PINs. 'Something you have' refers to physical items the user possesses, like smartphones receiving OTP codes. 'Something you are' refers to biometric characteristics unique to the user, like fingerprints. 'Somewhere you are' refers to the user's location, such as being on a specific network.", "hint": "Consider what type of verification is being used in each scenario.", "matchingData": {"items": [{"id": "know", "text": "Something you know", "icon": "fa-solid fa-brain"}, {"id": "have", "text": "Something you have", "icon": "fa-solid fa-mobile-screen"}, {"id": "are", "text": "Something you are", "icon": "fa-solid fa-fingerprint"}, {"id": "where", "text": "Somewhere you are", "icon": "fa-solid fa-location-dot"}], "targets": [{"id": "otp_phone", "text": "During the login process, your phone receives a text message with a one-time passcode", "icon": "fa-solid fa-message"}, {"id": "pin_atm", "text": "You enter your PIN to make a deposit into an ATM", "icon": "fa-solid fa-money-bill"}, {"id": "fingerprint_dc", "text": "You can use your fingerprint to unlock the door to the data center", "icon": "fa-solid fa-hand"}, {"id": "vpn_login", "text": "Your login will not work unless you are connected to the VPN", "icon": "fa-solid fa-shield"}], "correctMatches": {"otp_phone": ["have"], "pin_atm": ["know"], "fingerprint_dc": ["are"], "vpn_login": ["where"]}, "allowMultiple": false, "requireAll": true}, "created": "2023-07-15T12:00:00Z", "lastModified": "2023-07-15T12:00:00Z", "version": 1}, {"id": "match_mobile_security", "type": "matching", "difficulty": "medium", "category": "B", "tags": ["mobile security", "device security", "authentication"], "questionText": "An organization is deploying a mobile app to its sales team in the field. The application will be accessed from tablets for remote team members and a browser-based front-end on desktops for corporate office users. The application contains sensitive customer information, and two forms of authentication are required to launch the application. Select the best security features for each platform. A security feature will only be used once. Not all security features will be used.", "timeLimit": 150, "points": 10, "explanation": "This question focuses on security features for end-user computing devices. As a mobile device, a tablet will need integration with an MDM (Mobile Device Manager) and encryption of all data on the device. As an additional security feature, face recognition can provide additional authentication options using the built-in camera on the tablet. Since a desktop does not often move, the security requirements are slightly different than a mobile device. Host-based firewalls and anti-virus software is common on a desktop computers.", "hint": "Consider the different security requirements for mobile vs. stationary devices.", "matchingData": {"items": [{"id": "infrared", "text": "Infrared sensors", "icon": "fa-solid fa-temperature-high"}, {"id": "osint", "text": "OSINT", "icon": "fa-solid fa-magnifying-glass"}, {"id": "mdm", "text": "MDM integration", "icon": "fa-solid fa-mobile-screen"}, {"id": "encryption", "text": "Full Device Encryption", "icon": "fa-solid fa-lock"}, {"id": "biometric", "text": "Biometric authentication", "icon": "fa-solid fa-fingerprint"}, {"id": "firewall", "text": "Host-based Firewall", "icon": "fa-solid fa-shield-halved"}, {"id": "antimalware", "text": "Anti-Malware", "icon": "fa-solid fa-virus-slash"}], "targets": [{"id": "tablet", "text": "Tablet for Field Sales", "description": "Mobile device used by sales team in the field", "icon": "fa-solid fa-tablet-screen-button"}, {"id": "desktop", "text": "Desktop with Browser-based Front-end", "description": "Office computer used by corporate staff", "icon": "fa-solid fa-desktop"}], "correctMatches": {"tablet": ["mdm", "encryption", "biometric"], "desktop": ["firewall", "antimalware"]}, "allowMultiple": true, "requireAll": true}, "created": "2023-07-15T12:00:00Z", "lastModified": "2023-07-15T12:00:00Z", "version": 1}, {"id": "match_certificate_concepts", "type": "matching", "difficulty": "hard", "category": "B", "tags": ["cryptography", "certificates", "PKI"], "questionText": "Match the certificate characteristic to the description:", "timeLimit": 120, "points": 10, "explanation": "The certificate revocation list (CRL) is a file containing a list of the revoked certificates, maintained by the CA. The certificate signing request (CSR) is sent to the CA to get a public key signed into a certificate. The certificate authority (CA) is the administrative control for PKI deployment. Online Certificate Status Protocol (OCSP) is used by browsers to check the revocation status of a certificate in real-time.", "hint": "Consider the role each component plays in the PKI ecosystem.", "matchingData": {"items": [{"id": "crl", "text": "CRL", "icon": "fa-solid fa-ban"}, {"id": "ocsp", "text": "OCSP", "icon": "fa-solid fa-check-double"}, {"id": "ca", "text": "CA", "icon": "fa-solid fa-stamp"}, {"id": "csr", "text": "CSR", "icon": "fa-solid fa-file-signature"}], "targets": [{"id": "crl_desc", "text": "Certificate Revocation List - A list of invalidated certificates", "icon": "fa-solid fa-list"}, {"id": "csr_desc", "text": "Certificate Signing Request - Send the public key to be signed", "icon": "fa-solid fa-paper-plane"}, {"id": "ca_desc", "text": "Certificate Authority - Deploy and manage certificates", "icon": "fa-solid fa-building"}, {"id": "ocsp_desc", "text": "Online Certificate Status Protocol - The browser checks for a revoked certificate", "icon": "fa-solid fa-globe"}], "correctMatches": {"crl_desc": ["crl"], "csr_desc": ["csr"], "ca_desc": ["ca"], "ocsp_desc": ["ocsp"]}, "allowMultiple": false, "requireAll": true}, "created": "2023-07-15T12:00:00Z", "lastModified": "2023-07-15T12:00:00Z", "version": 1}, {"id": "order_incident_response", "type": "ordering", "difficulty": "medium", "category": "B", "tags": ["incident response", "process", "security operations"], "questionText": "Place the incident response activities in the correct order:", "timeLimit": 90, "points": 10, "explanation": "The standard incident response process follows these phases: Preparation (planning and setup), Detection (identifying an incident), Analysis (understanding the scope and impact), Containment (preventing spread), Eradication (removing the cause), Recovery (restoring systems), and Lessons Learned (improving future responses).", "hint": "Consider the logical flow of activities from before an incident occurs to after it's resolved.", "orderingData": {"items": [{"id": "preparation", "text": "Preparation", "icon": "fa-solid fa-clipboard-list"}, {"id": "detection", "text": "Detection", "icon": "fa-solid fa-magnifying-glass"}, {"id": "analysis", "text": "Analysis", "icon": "fa-solid fa-microscope"}, {"id": "containment", "text": "Containment", "icon": "fa-solid fa-shield-virus"}, {"id": "eradication", "text": "Eradication", "icon": "fa-solid fa-virus-slash"}, {"id": "recovery", "text": "Recovery", "icon": "fa-solid fa-arrow-rotate-right"}, {"id": "lessons", "text": "Lessons learned", "icon": "fa-solid fa-book"}], "correctOrder": ["preparation", "detection", "analysis", "containment", "eradication", "recovery", "lessons"], "allowPartialCredit": true}, "created": "2023-07-15T12:00:00Z", "lastModified": "2023-07-15T12:00:00Z", "version": 1}, {"id": "match_security_tech", "type": "matching", "difficulty": "medium", "category": "B", "tags": ["security technologies", "cryptography", "data protection"], "questionText": "Match the security technology to the implementation:", "timeLimit": 120, "points": 10, "explanation": "Hashing creates a one-way function for password storage. Digital signatures ensure integrity and non-repudiation using hashing and asymmetric crypto. SPF validates email sending servers. Key escrow stores decryption keys with a trusted third party. Journaling prevents data corruption by writing changes before committing. Obfuscation makes code harder to read.", "hint": "Consider the primary purpose of each security technology and how it would be applied.", "matchingData": {"items": [{"id": "hashing", "text": "Hashing", "icon": "fa-solid fa-hashtag"}, {"id": "digsig", "text": "Digital signature", "icon": "fa-solid fa-signature"}, {"id": "spf", "text": "SPF", "icon": "fa-solid fa-envelope"}, {"id": "escrow", "text": "Key escrow", "icon": "fa-solid fa-key"}, {"id": "journal", "text": "Journaling", "icon": "fa-solid fa-book-journal-whills"}, {"id": "obfusc", "text": "Obfuscation", "icon": "fa-solid fa-mask"}], "targets": [{"id": "hashing_impl", "text": "Store a password on an authentication server", "icon": "fa-solid fa-database"}, {"id": "digsig_impl", "text": "Verify a sender's identity", "icon": "fa-solid fa-user-check"}, {"id": "spf_impl", "text": "Authenticate the server sending an email", "icon": "fa-solid fa-server"}, {"id": "escrow_impl", "text": "Store keys with a third-party", "icon": "fa-solid fa-building-columns"}, {"id": "journal_impl", "text": "Prevent data corruption when a system fails", "icon": "fa-solid fa-triangle-exclamation"}, {"id": "obfusc_impl", "text": "Modify a script to make it difficult to understand", "icon": "fa-solid fa-code"}], "correctMatches": {"hashing_impl": ["hashing"], "digsig_impl": ["digsig"], "spf_impl": ["spf"], "escrow_impl": ["escrow"], "journal_impl": ["journal"], "obfusc_impl": ["obfusc"]}, "allowMultiple": false, "requireAll": true}, "created": "2023-07-15T12:00:00Z", "lastModified": "2023-07-15T12:00:00Z", "version": 1}, {"id": "match_data_states", "type": "matching_categories", "difficulty": "hard", "category": "B", "tags": ["data security", "data states", "data protection"], "questionText": "Select the data state that best fits the description. Each data state will be used more than once.", "timeLimit": 180, "points": 10, "explanation": "Understanding the state of data is crucial for implementing appropriate security controls. Data in-transit moves across the network (e.g., trunk links, satellite uploads, network traffic inspection). Data at-rest is located on a storage device (e.g., database, backup tapes, cloud storage). Data in-use is actively being processed in memory (e.g., decryption, hashing, validation, spreadsheet calculations).", "hint": "Consider whether the data is being stored, transmitted, or actively processed.", "matchingCategoriesData": {"categories": [{"id": "transit", "text": "Data in-transit"}, {"id": "rest", "text": "Data at-rest"}, {"id": "use", "text": "Data in-use"}], "items": [{"id": "s1", "text": "All switches in a data center are connected with an 802.1Q trunk", "icon": "fa-solid fa-network-wired"}, {"id": "s2", "text": "Sales information is uploaded daily from a remote site using a satellite network", "icon": "fa-solid fa-satellite-dish"}, {"id": "s3", "text": "A company stores customer purchase information in a MySQL database", "icon": "fa-solid fa-database"}, {"id": "s4", "text": "An application decrypts credit card numbers and expiration dates to validate for approval", "icon": "fa-solid fa-credit-card"}, {"id": "s5", "text": "An authentication program performs a hash of all passwords", "icon": "fa-solid fa-key"}, {"id": "s6", "text": "An IPS identifies a SQL injection attack and removes the attack frames from the network", "icon": "fa-solid fa-shield-virus"}, {"id": "s7", "text": "An automatic teller machine validates a user's PIN before allowing a deposit", "icon": "fa-solid fa-money-bill"}, {"id": "s8", "text": "Each time a spreadsheet is updated, all of the cells containing formulas are automatically updated", "icon": "fa-solid fa-table"}, {"id": "s9", "text": "All weekly backup tapes are transported to an offsite storage facility", "icon": "fa-solid fa-tape"}, {"id": "s10", "text": "All user spreadsheets are stored on a cloud-based file sharing service", "icon": "fa-solid fa-cloud"}], "correctMatches": {"s1": "transit", "s2": "transit", "s3": "rest", "s4": "use", "s5": "use", "s6": "transit", "s7": "use", "s8": "use", "s9": "rest", "s10": "rest"}}, "created": "2023-07-15T12:00:00Z", "lastModified": "2023-07-15T12:00:00Z", "version": 1}, {"id": "match_firewall_rules", "type": "matching_categories", "difficulty": "hard", "category": "C", "tags": ["network security", "firewall", "traffic filtering"], "questionText": "Refer to the following firewall rules:\nRule # | Source IP | Destination IP | Protocol (TCP/UDP) | Port # | Allow/Block\n-------|-----------|----------------|--------------------|--------|------------\n1      | Any       | **********     | TCP                | 22     | Allow\n2      | Any       | ***********    | TCP                | 80     | Allow\n3      | Any       | ***********    | TCP                | 443    | Allow\n4      | Any       | **********     | TCP                | 3389   | Allow\n5      | Any       | Any            | UDP                | 53     | Allow\n6      | Any       | Any            | UDP                | 123    | Allow\n7      | Any       | Any            | ICMP               |        | Block\n\nCategorize the following traffic flows as ALLOWED or BLOCKED through the firewall:", "timeLimit": 180, "points": 10, "explanation": "SSH (TCP/22) to ********** matches Rule 1 (Allow). RDP (TCP/3389) to *********** doesn't match Rule 4 (wrong destination) or any other allow rule, so it's blocked. DNS (UDP/53) matches Rule 5 (Allow). Web (TCP/80 or 443) to *********** matches Rule 2 or 3 (Allow). LDAP (TCP/389 or 636) to ********** doesn't match any allow rule, so it's blocked. NTP (UDP/123) matches Rule 6 (Allow).", "hint": "For each scenario, identify the protocol, port, and destination IP, then check if there's a matching rule.", "matchingCategoriesData": {"categories": [{"id": "allowed", "text": "ALLOWED"}, {"id": "blocked", "text": "BLOCKED"}], "items": [{"id": "ssh", "text": "Use a secure terminal to connect to **********", "icon": "fa-solid fa-terminal"}, {"id": "rdp", "text": "Share the desktop on server ***********", "icon": "fa-solid fa-desktop"}, {"id": "dns", "text": "Perform a DNS query from ********** to *******", "icon": "fa-solid fa-globe"}, {"id": "web", "text": "View web pages on ***********", "icon": "fa-solid fa-browser"}, {"id": "ldap", "text": "Authenticate to an LDAP server at **********", "icon": "fa-solid fa-users"}, {"id": "ntp", "text": "Synchronize the clock on a server at **********", "icon": "fa-solid fa-clock"}], "correctMatches": {"ssh": "allowed", "rdp": "blocked", "dns": "allowed", "web": "allowed", "ldap": "blocked", "ntp": "allowed"}}, "created": "2023-07-15T12:00:00Z", "lastModified": "2023-07-15T12:00:00Z", "version": 1}, {"id": "match_network_devices", "type": "matching", "difficulty": "medium", "category": "C", "tags": ["network devices", "network security", "security appliances"], "questionText": "Match the device to the description. Some device types will not be used.", "timeLimit": 120, "points": 8, "explanation": "An IPS blocks exploits like SQL injection. A Proxy intercepts and caches web requests. A Router forwards traffic between networks/VLANs. A Load Balancer distributes traffic across servers. A WAF inspects web application input.", "hint": "Consider the primary function of each network device and how it processes traffic.", "matchingData": {"items": [{"id": "ips", "text": "IPS", "icon": "fa-solid fa-shield-virus"}, {"id": "proxy", "text": "Proxy", "icon": "fa-solid fa-exchange"}, {"id": "router", "text": "Router", "icon": "fa-solid fa-network-wired"}, {"id": "lb", "text": "Load balancer", "icon": "fa-solid fa-balance-scale"}, {"id": "waf", "text": "WAF", "icon": "fa-solid fa-filter"}], "targets": [{"id": "ips_desc", "text": "Block SQL injection over an Internet connection", "icon": "fa-solid fa-ban"}, {"id": "proxy_desc", "text": "Intercept all browser requests and cache the results", "icon": "fa-solid fa-database"}, {"id": "router_desc", "text": "Forward packets between separate VLANs", "icon": "fa-solid fa-arrows-split-up-and-left"}, {"id": "lb_desc", "text": "Configure a group of redundant web servers", "icon": "fa-solid fa-server"}, {"id": "waf_desc", "text": "Evaluate the input to a browser-based application", "icon": "fa-solid fa-code"}], "correctMatches": {"ips_desc": ["ips"], "proxy_desc": ["proxy"], "router_desc": ["router"], "lb_desc": ["lb"], "waf_desc": ["waf"]}, "allowMultiple": false, "requireAll": true}, "created": "2023-07-15T12:00:00Z", "lastModified": "2023-07-15T12:00:00Z", "version": 1}, {"id": "match_attack_characteristics", "type": "matching", "difficulty": "medium", "category": "C", "tags": ["attack types", "security threats", "cybersecurity"], "questionText": "Match the attack type to the characteristic:", "timeLimit": 120, "points": 8, "explanation": "DDoS overwhelms a service. Replay captures and retransmits data. Rootkits hide malware. Brute force tries many passwords. Phishing tricks users into revealing credentials. Injection adds malicious code to input.", "hint": "Consider the primary technique or outcome of each attack type.", "matchingData": {"items": [{"id": "ddos", "text": "DDoS", "icon": "fa-solid fa-server"}, {"id": "replay", "text": "Replay", "icon": "fa-solid fa-rotate"}, {"id": "rootkit", "text": "Rootkit", "icon": "fa-solid fa-bug"}, {"id": "bruteforce", "text": "Brute force", "icon": "fa-solid fa-hammer"}, {"id": "phishing", "text": "<PERSON><PERSON>", "icon": "fa-solid fa-fish"}, {"id": "injection", "text": "Injection", "icon": "fa-solid fa-syringe"}], "targets": [{"id": "ddos_char", "text": "A website stops responding to normal requests", "icon": "fa-solid fa-triangle-exclamation"}, {"id": "replay_char", "text": "Data is captured and retransmitted to a server", "icon": "fa-solid fa-repeat"}, {"id": "rootkit_char", "text": "The malware is designed to remain hidden on a computer system", "icon": "fa-solid fa-mask"}, {"id": "brutef_char", "text": "A list of passwords are attempted with a known username", "icon": "fa-solid fa-key"}, {"id": "phish_char", "text": "An email link redirects a user to a site that requests login credentials", "icon": "fa-solid fa-envelope"}, {"id": "inject_char", "text": "Permissions are circumvented by adding additional code as application input", "icon": "fa-solid fa-code"}], "correctMatches": {"ddos_char": ["ddos"], "replay_char": ["replay"], "rootkit_char": ["rootkit"], "brutef_char": ["bruteforce"], "phish_char": ["phishing"], "inject_char": ["injection"]}, "allowMultiple": false, "requireAll": true}, "created": "2023-07-15T12:00:00Z", "lastModified": "2023-07-15T12:00:00Z", "version": 1}, {"id": "match_crypto_tech", "type": "matching", "difficulty": "hard", "category": "C", "tags": ["cryptography", "data protection", "security technologies"], "questionText": "Match the cryptography technology to the description:", "timeLimit": 150, "points": 10, "explanation": "Key stretching makes keys stronger via hashing/processes. Steganography hides data in media. Collision is when different inputs produce the same hash. Masking hides sensitive data (e.g., ****1234). Asymmetric crypto uses different keys for encryption/decryption. Salting adds unique data to passwords before hashing.", "hint": "Consider the primary purpose and mechanism of each cryptographic technique.", "matchingData": {"items": [{"id": "stretching", "text": "Key stretching", "icon": "fa-solid fa-arrows-left-right-to-line"}, {"id": "steg", "text": "Steganography", "icon": "fa-solid fa-image"}, {"id": "collision", "text": "Collision", "icon": "fa-solid fa-car-burst"}, {"id": "masking", "text": "Masking", "icon": "fa-solid fa-eye-slash"}, {"id": "asymmetric", "text": "Asymmetric", "icon": "fa-solid fa-key"}, {"id": "salting", "text": "Salting", "icon": "fa-solid fa-salt-shaker"}], "targets": [{"id": "stretch_desc", "text": "Create a stronger key using multiple processes", "icon": "fa-solid fa-shield"}, {"id": "steg_desc", "text": "Data is hidden within another media type", "icon": "fa-solid fa-file-image"}, {"id": "coll_desc", "text": "Different inputs create the same hash", "icon": "fa-solid fa-equals"}, {"id": "mask_desc", "text": "Sensitive data is hidden from view", "icon": "fa-solid fa-mask"}, {"id": "asym_desc", "text": "A different key is used for decryption than encryption", "icon": "fa-solid fa-lock-open"}, {"id": "salt_desc", "text": "Information is added to make a unique hash", "icon": "fa-solid fa-fingerprint"}], "correctMatches": {"stretch_desc": ["stretching"], "steg_desc": ["steg"], "coll_desc": ["collision"], "mask_desc": ["masking"], "asym_desc": ["asymmetric"], "salt_desc": ["salting"]}, "allowMultiple": false, "requireAll": true}, "created": "2023-07-15T12:00:00Z", "lastModified": "2023-07-15T12:00:00Z", "version": 1}, {"id": "match_security_scenarios", "type": "matching", "difficulty": "medium", "category": "C", "tags": ["security technologies", "network security", "access control"], "questionText": "Add the most applicable security technologies to the following scenarios:", "timeLimit": 120, "points": 8, "explanation": "VPN protects data on public Wi-Fi. Sandboxing provides isolated testing environments. NGFW offers advanced filtering like blocking malicious sites. SD-WAN optimizes access to cloud services across locations. 802.1X provides port-based network access control using authentication.", "hint": "Consider the primary security need in each scenario and which technology best addresses it.", "matchingData": {"items": [{"id": "vpn", "text": "VPN", "icon": "fa-solid fa-shield-alt"}, {"id": "sandbox", "text": "Sandboxing", "icon": "fa-solid fa-box"}, {"id": "ngfw", "text": "NGFW", "icon": "fa-solid fa-fire"}, {"id": "sdwan", "text": "SD-WAN", "icon": "fa-solid fa-cloud"}, {"id": "8021x", "text": "802.1X", "icon": "fa-solid fa-user-lock"}], "targets": [{"id": "vpn_scen", "text": "A field service engineer uses their corporate laptop at coffee shops and hotels", "icon": "fa-solid fa-laptop"}, {"id": "sandbox_scen", "text": "Software developers run a series of tests before deploying an application", "icon": "fa-solid fa-code"}, {"id": "ngfw_scen", "text": "An administrator prevents employees from visiting known-malicious web sites", "icon": "fa-solid fa-ban"}, {"id": "sdwan_scen", "text": "Directly access cloud-based services from all corporate locations", "icon": "fa-solid fa-building"}, {"id": "8021x_scen", "text": "Users connecting to the network should use their corporate authentication credentials", "icon": "fa-solid fa-network-wired"}], "correctMatches": {"vpn_scen": ["vpn"], "sandbox_scen": ["sandbox"], "ngfw_scen": ["ngfw"], "sdwan_scen": ["sdwan"], "8021x_scen": ["8021x"]}, "allowMultiple": false, "requireAll": true}, "created": "2023-07-15T12:00:00Z", "lastModified": "2023-07-15T12:00:00Z", "version": 1}]