// Interactive Question Types for Quiz Application
// Includes drag-and-drop, matching, and interactive diagram questions

// Constants
const DRAG_DROP_CLASS = 'drag-drop-question';
const MATCHING_CLASS = 'matching-question';
const DIAGRAM_CLASS = 'diagram-question';

// Track current interactive question state
let interactiveState = {
    type: null,
    elements: [],
    correctState: null,
    userState: null
};

// Initialize interactive questions
function initInteractiveQuestions() {
    // This will be called when a question is displayed
    // The specific initialization depends on the question type
}

// Check if a question is interactive
function isInteractiveQuestion(question) {
    return question && (
        question.type === 'drag-drop' ||
        question.type === 'matching' ||
        question.type === 'diagram' ||
        (question.options && question.options.some(opt => typeof opt === 'object' && opt.type))
    );
}

// Get the type of interactive question
function getInteractiveType(question) {
    if (!question) return null;
    
    if (question.type) {
        return question.type;
    }
    
    if (question.options && question.options.some(opt => typeof opt === 'object' && opt.type === 'drag-drop')) {
        return 'drag-drop';
    }
    
    if (question.options && question.options.some(opt => typeof opt === 'object' && opt.type === 'matching')) {
        return 'matching';
    }
    
    if (question.options && question.options.some(opt => typeof opt === 'object' && opt.type === 'diagram')) {
        return 'diagram';
    }
    
    return null;
}

// Render an interactive question
function renderInteractiveQuestion(question, container) {
    const type = getInteractiveType(question);
    
    if (!type) {
        console.error('Not an interactive question or type not recognized');
        return false;
    }
    
    // Clear the container
    container.innerHTML = '';
    
    // Reset interactive state
    interactiveState = {
        type: type,
        elements: [],
        correctState: null,
        userState: null
    };
    
    // Render based on type
    switch (type) {
        case 'drag-drop':
            renderDragDropQuestion(question, container);
            break;
        case 'matching':
            renderMatchingQuestion(question, container);
            break;
        case 'diagram':
            renderDiagramQuestion(question, container);
            break;
        default:
            console.error('Unknown interactive question type:', type);
            return false;
    }
    
    return true;
}

// Check if the interactive question is correct
function checkInteractiveAnswer() {
    if (!interactiveState.type) return false;
    
    switch (interactiveState.type) {
        case 'drag-drop':
            return checkDragDropAnswer();
        case 'matching':
            return checkMatchingAnswer();
        case 'diagram':
            return checkDiagramAnswer();
        default:
            return false;
    }
}

// Get the user's answer for an interactive question
function getInteractiveAnswer() {
    if (!interactiveState.type) return null;
    
    switch (interactiveState.type) {
        case 'drag-drop':
            return getDragDropAnswer();
        case 'matching':
            return getMatchingAnswer();
        case 'diagram':
            return getDiagramAnswer();
        default:
            return null;
    }
}

// Show the correct answer for an interactive question
function showInteractiveCorrectAnswer() {
    if (!interactiveState.type) return;
    
    switch (interactiveState.type) {
        case 'drag-drop':
            showDragDropCorrectAnswer();
            break;
        case 'matching':
            showMatchingCorrectAnswer();
            break;
        case 'diagram':
            showDiagramCorrectAnswer();
            break;
    }
}

// === Drag and Drop Questions ===

// Render a drag and drop question
function renderDragDropQuestion(question, container) {
    container.classList.add(DRAG_DROP_CLASS);
    
    // Extract drag-drop data
    const dragDropData = question.options.find(opt => typeof opt === 'object' && opt.type === 'drag-drop');
    
    if (!dragDropData || !dragDropData.items || !dragDropData.targets) {
        console.error('Invalid drag-drop question data');
        return;
    }
    
    // Create items container
    const itemsContainer = document.createElement('div');
    itemsContainer.className = 'drag-items-container';
    
    // Create targets container
    const targetsContainer = document.createElement('div');
    targetsContainer.className = 'drag-targets-container';
    
    // Store correct state
    interactiveState.correctState = {};
    
    // Create draggable items
    const shuffledItems = shuffleArray([...dragDropData.items]);
    shuffledItems.forEach((item, index) => {
        const itemElement = document.createElement('div');
        itemElement.className = 'drag-item';
        itemElement.textContent = item.text || item;
        itemElement.setAttribute('draggable', 'true');
        itemElement.dataset.itemId = item.id || `item-${index}`;
        
        // Add drag event listeners
        itemElement.addEventListener('dragstart', handleDragStart);
        
        itemsContainer.appendChild(itemElement);
        
        // Store in elements array
        interactiveState.elements.push(itemElement);
    });
    
    // Create drop targets
    dragDropData.targets.forEach((target, index) => {
        const targetElement = document.createElement('div');
        targetElement.className = 'drag-target';
        
        // Add target label if provided
        if (target.label) {
            const labelElement = document.createElement('div');
            labelElement.className = 'drag-target-label';
            labelElement.textContent = target.label;
            targetElement.appendChild(labelElement);
        }
        
        // Add drop zone
        const dropZone = document.createElement('div');
        dropZone.className = 'drag-drop-zone';
        dropZone.textContent = 'Drop here';
        dropZone.dataset.targetId = target.id || `target-${index}`;
        
        // Add drop event listeners
        dropZone.addEventListener('dragover', handleDragOver);
        dropZone.addEventListener('drop', handleDrop);
        dropZone.addEventListener('dragenter', handleDragEnter);
        dropZone.addEventListener('dragleave', handleDragLeave);
        
        targetElement.appendChild(dropZone);
        targetsContainer.appendChild(targetElement);
        
        // Store in elements array
        interactiveState.elements.push(dropZone);
        
        // Store correct answers
        if (target.correctItem) {
            interactiveState.correctState[dropZone.dataset.targetId] = 
                typeof target.correctItem === 'object' ? target.correctItem.id : target.correctItem;
        }
    });
    
    // Add containers to main container
    container.appendChild(itemsContainer);
    container.appendChild(targetsContainer);
    
    // Initialize user state
    interactiveState.userState = {};
}

// Handle drag start event
function handleDragStart(e) {
    e.dataTransfer.setData('text/plain', e.target.dataset.itemId);
    e.target.classList.add('dragging');
}

// Handle drag over event
function handleDragOver(e) {
    e.preventDefault();
}

// Handle drag enter event
function handleDragEnter(e) {
    e.preventDefault();
    e.target.classList.add('drag-over');
}

// Handle drag leave event
function handleDragLeave(e) {
    e.target.classList.remove('drag-over');
}

// Handle drop event
function handleDrop(e) {
    e.preventDefault();
    e.target.classList.remove('drag-over');
    
    const itemId = e.dataTransfer.getData('text/plain');
    const targetId = e.target.dataset.targetId;
    
    // Find the dragged item
    const draggedItem = document.querySelector(`.drag-item[data-item-id="${itemId}"]`);
    
    if (!draggedItem) return;
    
    // Remove the item from its previous location
    if (draggedItem.parentNode.classList.contains('drag-drop-zone')) {
        // It was in another drop zone, remove it from there
        draggedItem.parentNode.textContent = 'Drop here';
        
        // Remove from user state
        for (const key in interactiveState.userState) {
            if (interactiveState.userState[key] === itemId) {
                delete interactiveState.userState[key];
            }
        }
    }
    
    // Clear the drop zone
    e.target.textContent = '';
    
    // Add the item to the drop zone
    e.target.appendChild(draggedItem);
    draggedItem.classList.remove('dragging');
    
    // Update user state
    interactiveState.userState[targetId] = itemId;
}

// Check if drag-drop answer is correct
function checkDragDropAnswer() {
    if (!interactiveState.correctState || !interactiveState.userState) return false;
    
    // Check if all targets have the correct items
    for (const targetId in interactiveState.correctState) {
        const correctItemId = interactiveState.correctState[targetId];
        const userItemId = interactiveState.userState[targetId];
        
        if (correctItemId !== userItemId) {
            return false;
        }
    }
    
    return true;
}

// Get the user's drag-drop answer
function getDragDropAnswer() {
    return interactiveState.userState;
}

// Show the correct drag-drop answer
function showDragDropCorrectAnswer() {
    if (!interactiveState.correctState) return;
    
    // Highlight correct and incorrect answers
    for (const targetId in interactiveState.correctState) {
        const dropZone = document.querySelector(`.drag-drop-zone[data-target-id="${targetId}"]`);
        if (!dropZone) continue;
        
        const correctItemId = interactiveState.correctState[targetId];
        const userItemId = interactiveState.userState[targetId];
        
        if (correctItemId === userItemId) {
            // Correct answer
            dropZone.classList.add('correct-answer');
        } else {
            // Incorrect answer
            dropZone.classList.add('incorrect-answer');
            
            // Show correct answer
            const correctItem = document.querySelector(`.drag-item[data-item-id="${correctItemId}"]`);
            if (correctItem) {
                const correctLabel = document.createElement('div');
                correctLabel.className = 'correct-answer-label';
                correctLabel.textContent = `Correct: ${correctItem.textContent}`;
                dropZone.appendChild(correctLabel);
            }
        }
    }
}

// === Matching Questions ===
// (Implementation similar to drag-drop but with lines connecting matches)

// Render a matching question
function renderMatchingQuestion(question, container) {
    // Implementation will be similar to drag-drop but with different UI
    container.classList.add(MATCHING_CLASS);
    
    // Extract matching data
    const matchingData = question.options.find(opt => typeof opt === 'object' && opt.type === 'matching');
    
    if (!matchingData || !matchingData.items || !matchingData.matches) {
        console.error('Invalid matching question data');
        return;
    }
    
    // Create matching container
    const matchingContainer = document.createElement('div');
    matchingContainer.className = 'matching-container';
    
    // Create items column
    const itemsColumn = document.createElement('div');
    itemsColumn.className = 'matching-column items-column';
    
    // Create matches column
    const matchesColumn = document.createElement('div');
    matchesColumn.className = 'matching-column matches-column';
    
    // Store correct state
    interactiveState.correctState = {};
    
    // Create items
    matchingData.items.forEach((item, index) => {
        const itemElement = document.createElement('div');
        itemElement.className = 'matching-item';
        itemElement.textContent = item.text || item;
        itemElement.dataset.itemId = item.id || `item-${index}`;
        
        itemsColumn.appendChild(itemElement);
        
        // Store in elements array
        interactiveState.elements.push(itemElement);
    });
    
    // Create matches (shuffled)
    const shuffledMatches = shuffleArray([...matchingData.matches]);
    shuffledMatches.forEach((match, index) => {
        const matchElement = document.createElement('div');
        matchElement.className = 'matching-match';
        matchElement.textContent = match.text || match;
        matchElement.dataset.matchId = match.id || `match-${index}`;
        
        matchesColumn.appendChild(matchElement);
        
        // Store in elements array
        interactiveState.elements.push(matchElement);
    });
    
    // Add columns to container
    matchingContainer.appendChild(itemsColumn);
    matchingContainer.appendChild(matchesColumn);
    container.appendChild(matchingContainer);
    
    // Add connection lines container
    const linesContainer = document.createElement('div');
    linesContainer.className = 'matching-lines-container';
    container.appendChild(linesContainer);
    
    // Add matching controls
    const controlsContainer = document.createElement('div');
    controlsContainer.className = 'matching-controls';
    
    const instructionsElement = document.createElement('div');
    instructionsElement.className = 'matching-instructions';
    instructionsElement.textContent = 'Click an item on the left, then click its match on the right.';
    controlsContainer.appendChild(instructionsElement);
    
    const resetButton = document.createElement('button');
    resetButton.className = 'matching-reset-button';
    resetButton.textContent = 'Reset Matches';
    resetButton.addEventListener('click', resetMatching);
    controlsContainer.appendChild(resetButton);
    
    container.appendChild(controlsContainer);
    
    // Initialize user state
    interactiveState.userState = {};
    
    // Add click event listeners to items and matches
    document.querySelectorAll('.matching-item, .matching-match').forEach(element => {
        element.addEventListener('click', handleMatchingClick);
    });
    
    // Store correct matches
    if (matchingData.correctMatches) {
        for (const itemId in matchingData.correctMatches) {
            interactiveState.correctState[itemId] = matchingData.correctMatches[itemId];
        }
    }
}

// Export functions
window.interactiveQuestions = {
    initInteractiveQuestions,
    isInteractiveQuestion,
    renderInteractiveQuestion,
    checkInteractiveAnswer,
    getInteractiveAnswer,
    showInteractiveCorrectAnswer
};
