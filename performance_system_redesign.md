# Performance-Based Question System Redesign

## 1. Data Structure

### 1.1 Unified Question Model

```javascript
{
  "id": "unique_question_id",
  "type": "performance_type", // matching, ordering, simulation, etc.
  "difficulty": "medium", // easy, medium, hard, adaptive
  "category": "category_code", // A, B, C, P (Performance)
  "tags": ["tag1", "tag2"], // For filtering and categorization
  "questionText": "Question prompt text",
  "timeLimit": 120, // Optional time limit in seconds
  "points": 10, // Base points value
  "explanation": "Detailed explanation of the answer",
  "hint": "Optional hint for the user",
  
  // Type-specific data (examples)
  "matchingData": {}, // For matching questions
  "orderingData": {}, // For ordering questions
  "simulationData": {}, // For simulation questions
  
  // Metadata
  "created": "2023-06-15T12:00:00Z",
  "lastModified": "2023-06-15T12:00:00Z",
  "version": 1
}
```

### 1.2 Type-Specific Data Structures

#### Matching Questions
```javascript
"matchingData": {
  "items": [
    { "id": "item1", "text": "Item 1", "icon": "fa-solid fa-icon" },
    { "id": "item2", "text": "Item 2", "icon": "fa-solid fa-icon" }
  ],
  "targets": [
    { "id": "target1", "text": "Target 1", "description": "Description" },
    { "id": "target2", "text": "Target 2", "description": "Description" }
  ],
  "correctMatches": {
    "target1": ["item1"],
    "target2": ["item2"]
  },
  "allowMultiple": true, // Whether multiple items can be assigned to one target
  "requireAll": true // Whether all items must be used
}
```

#### Ordering Questions
```javascript
"orderingData": {
  "items": [
    { "id": "item1", "text": "Step 1", "icon": "fa-solid fa-icon" },
    { "id": "item2", "text": "Step 2", "icon": "fa-solid fa-icon" }
  ],
  "correctOrder": ["item1", "item2"],
  "allowPartialCredit": true // Whether partial credit is given for partially correct ordering
}
```

#### Simulation Questions (New Type)
```javascript
"simulationData": {
  "scenario": "Detailed scenario description",
  "environment": {
    "type": "network", // network, system, application
    "initialState": {}, // Initial state of the simulation
    "tools": ["tool1", "tool2"] // Available tools
  },
  "tasks": [
    { "id": "task1", "description": "Task 1", "points": 5 },
    { "id": "task2", "description": "Task 2", "points": 5 }
  ],
  "successCriteria": {}, // Criteria for successful completion
  "timeLimit": 300 // Time limit in seconds
}
```

## 2. User Interface Redesign

### 2.1 Core Components

1. **Question Container**
   - Unified container for all question types
   - Responsive design for different screen sizes
   - Consistent styling and interaction patterns

2. **Interactive Elements**
   - Drag-and-drop interface with visual feedback
   - Touch-friendly for mobile devices
   - Keyboard navigation for accessibility

3. **Feedback System**
   - Real-time validation feedback
   - Progressive hints
   - Detailed explanations after submission

### 2.2 Question Type-Specific UI

1. **Matching Questions**
   - Two-column layout with items and targets
   - Visual indicators for matches
   - Support for one-to-many and many-to-one relationships

2. **Ordering Questions**
   - Vertical list with drag handles
   - Numbered positions
   - Animation for reordering

3. **Simulation Questions**
   - Interactive workspace
   - Tool palette
   - Real-time state visualization

### 2.3 Progress Tracking UI

1. **Progress Bar**
   - Visual indicator of completion percentage
   - Color-coded for performance level
   - Synced with 7-day timer

2. **Performance Dashboard**
   - Detailed statistics by question type
   - Skill mastery visualization
   - Improvement trends over time

## 3. Scoring and Assessment

### 3.1 Scoring Model

1. **Base Points**
   - Each question has a base point value
   - Points adjusted by difficulty level
   - Time-based bonus points

2. **Partial Credit**
   - Granular scoring for partially correct answers
   - Weighted scoring based on importance of elements
   - Progressive scoring for multi-step questions

3. **Performance Metrics**
   - Accuracy: Percentage of correct answers
   - Efficiency: Time taken vs. expected time
   - Consistency: Performance variation across attempts

### 3.2 Adaptive Difficulty

1. **Difficulty Levels**
   - Easy: Basic concepts, more guidance
   - Medium: Standard complexity
   - Hard: Advanced concepts, less guidance
   - Expert: Complex scenarios, no guidance

2. **Dynamic Adjustment**
   - Adjust difficulty based on user performance
   - Personalized question selection
   - Spaced repetition for challenging concepts

## 4. Technical Implementation

### 4.1 Architecture

1. **Module Structure**
   - `performance-core.js`: Core functionality
   - `performance-ui.js`: UI components
   - `performance-types/`: Type-specific modules
   - `performance-analytics.js`: Performance tracking

2. **State Management**
   - Centralized state for question data
   - Event-based communication between modules
   - Persistent storage with localStorage

3. **Rendering Engine**
   - Component-based rendering
   - Virtual DOM for efficient updates
   - Template system for question types

### 4.2 Interaction Handling

1. **Drag and Drop**
   - HTML5 Drag and Drop API
   - Touch events for mobile
   - Keyboard navigation for accessibility

2. **Validation**
   - Real-time validation
   - Debounced feedback
   - Progressive error messages

3. **Animation**
   - CSS transitions for smooth interactions
   - Feedback animations
   - Progress indicators

## 5. Performance Analytics

### 5.1 Data Collection

1. **User Interactions**
   - Answer attempts
   - Time spent on questions
   - Hint usage
   - Error patterns

2. **Session Data**
   - Question sequence
   - Performance by category
   - Performance by difficulty
   - Time distribution

### 5.2 Analysis and Visualization

1. **Performance Metrics**
   - Accuracy by question type
   - Time efficiency
   - Improvement over time
   - Skill mastery levels

2. **Visualizations**
   - Performance radar charts
   - Progress over time
   - Strength/weakness analysis
   - Comparison to peers (optional)

## 6. Integration with Existing System

### 6.1 Data Migration

1. **Question Conversion**
   - Convert existing questions to new format
   - Preserve question IDs for history tracking
   - Add new metadata fields

2. **User Data Migration**
   - Map existing performance data to new structure
   - Preserve historical performance data
   - Update statistics calculation

### 6.2 UI Integration

1. **Consistent Experience**
   - Match existing UI patterns
   - Consistent navigation
   - Unified feedback system

2. **Progressive Enhancement**
   - Fallback for older browsers
   - Graceful degradation of advanced features
   - Accessibility compliance
