# Performance-Based Question System Implementation Plan

## 1. File Structure

```
/performance/
  ├── core/
  │   ├── performance-core.js       # Core functionality
  │   ├── performance-state.js      # State management
  │   └── performance-utils.js      # Utility functions
  ├── ui/
  │   ├── performance-ui.js         # UI components
  │   ├── performance-renderer.js   # Rendering engine
  │   └── performance-animations.js # Animations and transitions
  ├── types/
  │   ├── matching.js               # Matching question type
  │   ├── ordering.js               # Ordering question type
  │   └── simulation.js             # Simulation question type
  ├── analytics/
  │   ├── performance-tracking.js   # Data collection
  │   └── performance-metrics.js    # Analysis and metrics
  └── data/
      ├── question-schema.json      # JSON schema for validation
      └── sample-questions.json     # Sample questions
```

## 2. Core Module Implementation

### 2.1 performance-core.js

```javascript
// performance-core.js - Core functionality for performance-based questions

// Module pattern for encapsulation
const PerformanceCore = (function() {
  // Private variables
  let _questions = [];
  let _currentQuestion = null;
  let _userAnswers = {};
  let _settings = {
    showHints: true,
    allowPartialCredit: true,
    adaptiveDifficulty: false
  };

  // Initialize the module
  function init(options = {}) {
    _settings = { ..._settings, ...options };
    _loadQuestions();
    return this;
  }

  // Load questions from source
  async function _loadQuestions() {
    try {
      const response = await fetch('performance/data/questions.json');
      if (!response.ok) throw new Error('Failed to load questions');
      _questions = await response.json();
      PerformanceState.setQuestions(_questions);
    } catch (error) {
      console.error('Error loading questions:', error);
      _questions = [];
    }
  }

  // Get a question by ID
  function getQuestion(id) {
    return _questions.find(q => q.id === id) || null;
  }

  // Get questions filtered by criteria
  function getQuestions(filters = {}) {
    return _questions.filter(q => {
      for (const [key, value] of Object.entries(filters)) {
        if (Array.isArray(value)) {
          if (!value.includes(q[key])) return false;
        } else if (q[key] !== value) {
          return false;
        }
      }
      return true;
    });
  }

  // Set current question
  function setCurrentQuestion(questionId) {
    _currentQuestion = getQuestion(questionId);
    PerformanceState.setCurrentQuestion(_currentQuestion);
    return _currentQuestion;
  }

  // Submit an answer for the current question
  function submitAnswer(answer) {
    if (!_currentQuestion) return null;
    
    const questionType = _currentQuestion.type;
    const result = PerformanceTypes[questionType].validateAnswer(_currentQuestion, answer);
    
    // Save answer and result
    _userAnswers[_currentQuestion.id] = {
      answer,
      result,
      timestamp: new Date().toISOString()
    };
    
    // Update state
    PerformanceState.setUserAnswer(_currentQuestion.id, answer, result);
    
    // Track analytics
    PerformanceAnalytics.trackAnswer(_currentQuestion, answer, result);
    
    return result;
  }

  // Public API
  return {
    init,
    getQuestion,
    getQuestions,
    setCurrentQuestion,
    submitAnswer
  };
})();

// Export for use in other modules
window.PerformanceCore = PerformanceCore;
```

### 2.2 performance-state.js

```javascript
// performance-state.js - State management for performance-based questions

const PerformanceState = (function() {
  // State object
  const state = {
    questions: [],
    currentQuestion: null,
    userAnswers: {},
    userProgress: {
      completed: [],
      correct: [],
      incorrect: []
    },
    settings: {
      showHints: true,
      allowPartialCredit: true,
      adaptiveDifficulty: false
    }
  };

  // Event listeners
  const listeners = {
    'state:change': [],
    'question:change': [],
    'answer:submit': []
  };

  // Set questions
  function setQuestions(questions) {
    state.questions = questions;
    _notifyListeners('state:change', { questions });
  }

  // Set current question
  function setCurrentQuestion(question) {
    state.currentQuestion = question;
    _notifyListeners('question:change', { question });
  }

  // Set user answer
  function setUserAnswer(questionId, answer, result) {
    state.userAnswers[questionId] = { answer, result, timestamp: new Date().toISOString() };
    
    // Update progress
    if (!state.userProgress.completed.includes(questionId)) {
      state.userProgress.completed.push(questionId);
    }
    
    if (result.correct) {
      if (!state.userProgress.correct.includes(questionId)) {
        state.userProgress.correct.push(questionId);
      }
    } else {
      if (!state.userProgress.incorrect.includes(questionId)) {
        state.userProgress.incorrect.push(questionId);
      }
    }
    
    _notifyListeners('answer:submit', { questionId, answer, result });
  }

  // Get state
  function getState() {
    return { ...state };
  }

  // Subscribe to events
  function subscribe(event, callback) {
    if (!listeners[event]) listeners[event] = [];
    listeners[event].push(callback);
    return () => {
      listeners[event] = listeners[event].filter(cb => cb !== callback);
    };
  }

  // Notify listeners
  function _notifyListeners(event, data) {
    if (listeners[event]) {
      listeners[event].forEach(callback => callback(data));
    }
  }

  // Public API
  return {
    getState,
    setQuestions,
    setCurrentQuestion,
    setUserAnswer,
    subscribe
  };
})();

// Export for use in other modules
window.PerformanceState = PerformanceState;
```

## 3. Question Type Implementation

### 3.1 matching.js

```javascript
// matching.js - Matching question type implementation

const MatchingType = (function() {
  // Render a matching question
  function render(question, container) {
    const { matchingData } = question;
    
    // Create container elements
    const matchingContainer = document.createElement('div');
    matchingContainer.className = 'performance-matching';
    
    // Create items container
    const itemsContainer = document.createElement('div');
    itemsContainer.className = 'matching-items';
    
    // Create targets container
    const targetsContainer = document.createElement('div');
    targetsContainer.className = 'matching-targets';
    
    // Render items (shuffled)
    const shuffledItems = [...matchingData.items].sort(() => Math.random() - 0.5);
    shuffledItems.forEach(item => {
      const itemElement = document.createElement('div');
      itemElement.className = 'matching-item';
      itemElement.dataset.itemId = item.id;
      itemElement.draggable = true;
      
      // Add icon if available
      if (item.icon) {
        const iconElement = document.createElement('i');
        iconElement.className = item.icon;
        itemElement.appendChild(iconElement);
      }
      
      // Add text
      const textElement = document.createElement('span');
      textElement.textContent = item.text;
      itemElement.appendChild(textElement);
      
      // Add drag events
      itemElement.addEventListener('dragstart', handleDragStart);
      
      itemsContainer.appendChild(itemElement);
    });
    
    // Render targets
    matchingData.targets.forEach(target => {
      const targetElement = document.createElement('div');
      targetElement.className = 'matching-target';
      targetElement.dataset.targetId = target.id;
      
      // Add header
      const headerElement = document.createElement('div');
      headerElement.className = 'matching-target-header';
      headerElement.textContent = target.text;
      targetElement.appendChild(headerElement);
      
      // Add drop zone
      const dropZone = document.createElement('div');
      dropZone.className = 'matching-drop-zone';
      dropZone.addEventListener('dragover', handleDragOver);
      dropZone.addEventListener('drop', handleDrop);
      targetElement.appendChild(dropZone);
      
      targetsContainer.appendChild(targetElement);
    });
    
    // Assemble the question
    matchingContainer.appendChild(itemsContainer);
    matchingContainer.appendChild(targetsContainer);
    container.appendChild(matchingContainer);
  }
  
  // Validate a matching answer
  function validateAnswer(question, answer) {
    const { matchingData } = question;
    const correctMatches = matchingData.correctMatches;
    
    let totalCorrect = 0;
    let totalTargets = Object.keys(correctMatches).length;
    
    // Check each target
    for (const [targetId, correctItems] of Object.entries(correctMatches)) {
      const userItems = answer[targetId] || [];
      
      // Sort arrays for comparison
      const sortedCorrect = [...correctItems].sort();
      const sortedUser = [...userItems].sort();
      
      // Check if arrays match
      const isCorrect = sortedCorrect.length === sortedUser.length && 
                        sortedCorrect.every((item, i) => item === sortedUser[i]);
      
      if (isCorrect) totalCorrect++;
    }
    
    // Calculate score
    const percentCorrect = totalTargets > 0 ? (totalCorrect / totalTargets) * 100 : 0;
    const isFullyCorrect = totalCorrect === totalTargets;
    
    return {
      correct: isFullyCorrect,
      score: percentCorrect,
      details: {
        totalTargets,
        correctTargets: totalCorrect
      }
    };
  }
  
  // Event handlers
  function handleDragStart(event) {
    event.dataTransfer.setData('text/plain', event.target.dataset.itemId);
    event.target.classList.add('dragging');
  }
  
  function handleDragOver(event) {
    event.preventDefault();
    event.target.classList.add('drag-over');
  }
  
  function handleDrop(event) {
    event.preventDefault();
    const itemId = event.dataTransfer.getData('text/plain');
    const dropZone = event.target.closest('.matching-drop-zone');
    
    if (dropZone) {
      // Get the item element
      const itemElement = document.querySelector(`.matching-item[data-item-id="${itemId}"]`);
      
      if (itemElement) {
        // Move the item to the drop zone
        dropZone.appendChild(itemElement);
        
        // Update the answer in the state
        const targetId = dropZone.parentElement.dataset.targetId;
        const currentAnswer = PerformanceState.getState().userAnswers[PerformanceState.getState().currentQuestion.id]?.answer || {};
        
        // Initialize the target array if it doesn't exist
        if (!currentAnswer[targetId]) currentAnswer[targetId] = [];
        
        // Add the item to the target
        if (!currentAnswer[targetId].includes(itemId)) {
          currentAnswer[targetId].push(itemId);
        }
        
        // Update the state
        PerformanceState.setUserAnswer(
          PerformanceState.getState().currentQuestion.id,
          currentAnswer,
          null // No validation yet
        );
      }
    }
    
    event.target.classList.remove('drag-over');
  }
  
  // Public API
  return {
    render,
    validateAnswer
  };
})();

// Register the type
if (!window.PerformanceTypes) window.PerformanceTypes = {};
window.PerformanceTypes.matching = MatchingType;
```

## 4. UI Implementation

### 4.1 performance-ui.js

```javascript
// performance-ui.js - UI components for performance-based questions

const PerformanceUI = (function() {
  // Cache DOM elements
  let _container = null;
  let _questionText = null;
  let _questionContent = null;
  let _feedbackContainer = null;
  let _actionButtons = null;
  
  // Initialize the UI
  function init(containerId) {
    _container = document.getElementById(containerId);
    if (!_container) {
      console.error(`Container element with ID "${containerId}" not found`);
      return;
    }
    
    // Create UI structure
    _createUIStructure();
    
    // Subscribe to state changes
    PerformanceState.subscribe('question:change', handleQuestionChange);
    PerformanceState.subscribe('answer:submit', handleAnswerSubmit);
    
    return this;
  }
  
  // Create the UI structure
  function _createUIStructure() {
    _container.innerHTML = `
      <div class="performance-question">
        <div class="question-header">
          <div class="question-meta">
            <span class="question-category"></span>
            <span class="question-difficulty"></span>
          </div>
          <div class="question-text"></div>
        </div>
        <div class="question-content"></div>
        <div class="question-feedback"></div>
        <div class="question-actions">
          <button class="check-answer-btn">Check Answer</button>
          <button class="next-question-btn" disabled>Next Question</button>
        </div>
      </div>
    `;
    
    // Cache elements
    _questionText = _container.querySelector('.question-text');
    _questionContent = _container.querySelector('.question-content');
    _feedbackContainer = _container.querySelector('.question-feedback');
    _actionButtons = _container.querySelector('.question-actions');
    
    // Add event listeners
    _actionButtons.querySelector('.check-answer-btn').addEventListener('click', handleCheckAnswer);
    _actionButtons.querySelector('.next-question-btn').addEventListener('click', handleNextQuestion);
  }
  
  // Handle question change
  function handleQuestionChange(data) {
    const { question } = data;
    
    if (!question) {
      _container.classList.add('hidden');
      return;
    }
    
    // Update question text
    _questionText.textContent = question.questionText;
    
    // Update meta information
    _container.querySelector('.question-category').textContent = question.category;
    _container.querySelector('.question-difficulty').textContent = question.difficulty;
    
    // Clear previous content and feedback
    _questionContent.innerHTML = '';
    _feedbackContainer.innerHTML = '';
    
    // Reset buttons
    _actionButtons.querySelector('.check-answer-btn').disabled = false;
    _actionButtons.querySelector('.next-question-btn').disabled = true;
    
    // Render question based on type
    const questionType = question.type;
    if (PerformanceTypes[questionType]) {
      PerformanceTypes[questionType].render(question, _questionContent);
    } else {
      console.error(`Unknown question type: ${questionType}`);
      _questionContent.innerHTML = '<p>Error: Unknown question type</p>';
    }
    
    // Show the container
    _container.classList.remove('hidden');
  }
  
  // Handle check answer button click
  function handleCheckAnswer() {
    const state = PerformanceState.getState();
    const currentQuestion = state.currentQuestion;
    
    if (!currentQuestion) return;
    
    // Get current answer from state
    const currentAnswer = state.userAnswers[currentQuestion.id]?.answer || {};
    
    // Submit the answer
    const result = PerformanceCore.submitAnswer(currentAnswer);
    
    // Show feedback
    showFeedback(currentQuestion, currentAnswer, result);
    
    // Update buttons
    _actionButtons.querySelector('.check-answer-btn').disabled = true;
    _actionButtons.querySelector('.next-question-btn').disabled = false;
  }
  
  // Handle next question button click
  function handleNextQuestion() {
    // This would be implemented based on your navigation logic
    console.log('Next question requested');
  }
  
  // Handle answer submission
  function handleAnswerSubmit(data) {
    const { questionId, answer, result } = data;
    
    // If result is provided (automatic validation), show feedback
    if (result) {
      const question = PerformanceCore.getQuestion(questionId);
      showFeedback(question, answer, result);
    }
  }
  
  // Show feedback for an answer
  function showFeedback(question, answer, result) {
    _feedbackContainer.innerHTML = '';
    
    // Create feedback element
    const feedbackElement = document.createElement('div');
    feedbackElement.className = `feedback ${result.correct ? 'correct' : 'incorrect'}`;
    
    // Add feedback header
    const headerElement = document.createElement('h3');
    headerElement.textContent = result.correct ? 'Correct!' : 'Incorrect';
    feedbackElement.appendChild(headerElement);
    
    // Add score
    const scoreElement = document.createElement('p');
    scoreElement.textContent = `Score: ${Math.round(result.score)}%`;
    feedbackElement.appendChild(scoreElement);
    
    // Add explanation if available
    if (question.explanation) {
      const explanationElement = document.createElement('div');
      explanationElement.className = 'explanation';
      explanationElement.innerHTML = `<h4>Explanation:</h4><p>${question.explanation}</p>`;
      feedbackElement.appendChild(explanationElement);
    }
    
    // Add to container
    _feedbackContainer.appendChild(feedbackElement);
  }
  
  // Public API
  return {
    init
  };
})();

// Export for use in other modules
window.PerformanceUI = PerformanceUI;
```

## 5. Integration with Main Application

### 5.1 Integration Code

```javascript
// Initialize performance system when document is ready
document.addEventListener('DOMContentLoaded', function() {
  // Initialize core module
  PerformanceCore.init({
    showHints: true,
    allowPartialCredit: true,
    adaptiveDifficulty: false
  });
  
  // Initialize UI
  PerformanceUI.init('performanceQuestionContainer');
  
  // Initialize analytics
  PerformanceAnalytics.init();
  
  // Load initial question (example)
  const questions = PerformanceCore.getQuestions({ type: 'matching' });
  if (questions.length > 0) {
    PerformanceCore.setCurrentQuestion(questions[0].id);
  }
});
```

### 5.2 HTML Integration

```html
<!-- Add to index.html -->
<div id="performanceQuestionContainer" class="performance-container hidden"></div>

<!-- Add script references -->
<script src="performance/core/performance-core.js"></script>
<script src="performance/core/performance-state.js"></script>
<script src="performance/core/performance-utils.js"></script>
<script src="performance/ui/performance-ui.js"></script>
<script src="performance/types/matching.js"></script>
<script src="performance/types/ordering.js"></script>
<script src="performance/analytics/performance-tracking.js"></script>
```
