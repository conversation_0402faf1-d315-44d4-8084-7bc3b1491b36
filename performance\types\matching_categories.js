/**
 * matching_categories.js
 * Matching categories question type implementation
 */

const MatchingCategoriesType = (function() {
  /**
   * Render a matching categories question
   * @param {Object} question - Question object
   * @param {HTMLElement} container - Container element
   */
  function render(question, container) {
    if (!question || !question.matchingCategoriesData || !container) {
      console.error('Invalid question or container for matching_categories:', question);
      container.innerHTML = '<p>Error: Invalid question data</p>';
      return;
    }

    const { matchingCategoriesData } = question;
    
    // Create main container
    const mainContainer = document.createElement('div');
    mainContainer.className = 'matching-categories-container';
    
    // Create categories container
    const categoriesContainer = document.createElement('div');
    categoriesContainer.className = 'categories-container';
    
    // Create items container
    const itemsContainer = document.createElement('div');
    itemsContainer.className = 'items-container';
    
    // Render categories
    matchingCategoriesData.categories.forEach(category => {
      const categoryElement = document.createElement('div');
      categoryElement.className = 'category-drop-area';
      categoryElement.dataset.categoryId = category.id;
      
      const categoryHeader = document.createElement('div');
      categoryHeader.className = 'category-header';
      
      // Add icon if available
      if (category.icon) {
        const iconElement = document.createElement('i');
        iconElement.className = category.icon;
        categoryHeader.appendChild(iconElement);
      }
      
      const categoryTitle = document.createElement('h3');
      categoryTitle.textContent = category.text;
      categoryHeader.appendChild(categoryTitle);
      
      categoryElement.appendChild(categoryHeader);
      
      // Create drop zone for items
      const dropZone = document.createElement('div');
      dropZone.className = 'category-item-drop-zone';
      dropZone.dataset.locationId = category.id;
      
      // Add event listeners for drag and drop
      dropZone.addEventListener('dragover', handleDragOver);
      dropZone.addEventListener('dragenter', handleDragEnter);
      dropZone.addEventListener('dragleave', handleDragLeave);
      dropZone.addEventListener('drop', handleDrop);
      
      categoryElement.appendChild(dropZone);
      categoriesContainer.appendChild(categoryElement);
    });
    
    // Render items (shuffled)
    const shuffledItems = PerformanceUtils.shuffleArray([...matchingCategoriesData.items]);
    shuffledItems.forEach(item => {
      const itemElement = document.createElement('div');
      itemElement.className = 'draggable-control scenario-item';
      itemElement.dataset.control = item.id;
      itemElement.draggable = true;
      
      // Add icon if available
      if (item.icon) {
        const iconElement = document.createElement('i');
        iconElement.className = item.icon;
        itemElement.appendChild(iconElement);
      }
      
      const itemText = document.createElement('span');
      itemText.textContent = item.text;
      itemElement.appendChild(itemText);
      
      // Add event listeners for drag and drop
      itemElement.addEventListener('dragstart', handleDragStart);
      itemElement.addEventListener('dragend', handleDragEnd);
      
      itemsContainer.appendChild(itemElement);
    });
    
    // Add containers to main container
    mainContainer.appendChild(categoriesContainer);
    mainContainer.appendChild(itemsContainer);
    
    // Add main container to the provided container
    container.innerHTML = '';
    container.appendChild(mainContainer);
  }
  
  /**
   * Get the current answer from the UI
   * @param {HTMLElement} container - Container element
   * @returns {Object} - Current answer
   */
  function getCurrentAnswer(container) {
    if (!container) return {};
    
    const userAnswers = {}; // scenarioId: categoryName
    const allCategoryItemDropZones = container.querySelectorAll('.categories-container .category-item-drop-zone');
    
    allCategoryItemDropZones.forEach(zone => {
      const categoryName = zone.dataset.locationId; // Category name stored here
      const droppedScenarios = zone.querySelectorAll('.draggable-control.scenario-item');
      
      droppedScenarios.forEach(scenario => {
        const scenarioId = scenario.dataset.control; // Scenario ID stored in control dataset
        userAnswers[scenarioId] = categoryName; // Assign category to this scenario
      });
    });
    
    return userAnswers;
  }
  
  /**
   * Validate a matching categories answer
   * @param {Object} question - Question object
   * @param {Object} answer - User's answer
   * @returns {Object} - Validation result
   */
  function validateAnswer(question, answer) {
    if (!question || !question.matchingCategoriesData || !question.matchingCategoriesData.correctMatches) {
      return { correct: false, score: 0, details: { error: 'Invalid question' } };
    }
    
    if (!answer || typeof answer !== 'object') {
      return { correct: false, score: 0, details: { error: 'Invalid answer' } };
    }
    
    const { correctMatches } = question.matchingCategoriesData;
    const scenarioIds = Object.keys(correctMatches);
    
    let totalScenarios = scenarioIds.length;
    let correctScenarios = 0;
    
    // Check each scenario
    const details = {};
    
    scenarioIds.forEach(scenarioId => {
      const correctCategory = correctMatches[scenarioId];
      const userCategory = answer[scenarioId];
      
      const isCorrect = correctCategory === userCategory;
      
      if (isCorrect) {
        correctScenarios++;
      }
      
      // Store details for this scenario
      details[scenarioId] = {
        correctCategory,
        userCategory,
        isCorrect
      };
    });
    
    // Calculate score
    const score = (correctScenarios / totalScenarios) * 100;
    
    // Determine if answer is fully correct
    const isFullyCorrect = correctScenarios === totalScenarios;
    
    return {
      correct: isFullyCorrect,
      score: score,
      details: {
        totalScenarios,
        correctScenarios,
        scenarioDetails: details
      }
    };
  }
  
  /**
   * Show the correct answer in the UI
   * @param {Object} question - Question object
   * @param {HTMLElement} container - Container element
   */
  function showCorrectAnswer(question, container) {
    if (!question || !question.matchingCategoriesData || !container) return;
    
    const { correctMatches } = question.matchingCategoriesData;
    
    // Get all drop zones
    const dropZones = container.querySelectorAll('.category-item-drop-zone');
    
    // Check each drop zone
    dropZones.forEach(zone => {
      const categoryId = zone.dataset.locationId;
      if (!categoryId) return;
      
      // Find all scenarios that should be in this category
      const correctScenarios = Object.entries(correctMatches)
        .filter(([_, category]) => category === categoryId)
        .map(([scenarioId, _]) => scenarioId);
      
      // Get current scenarios in the drop zone
      const currentScenarios = Array.from(zone.querySelectorAll('.scenario-item'))
        .map(item => item.dataset.control);
      
      // Mark each scenario as correct or incorrect
      currentScenarios.forEach(scenarioId => {
        const scenarioElement = zone.querySelector(`.scenario-item[data-control="${scenarioId}"]`);
        if (!scenarioElement) return;
        
        if (correctMatches[scenarioId] === categoryId) {
          scenarioElement.classList.add('correct-answer');
        } else {
          scenarioElement.classList.add('incorrect-answer');
        }
      });
      
      // Show missing correct scenarios
      const missingScenarios = correctScenarios.filter(id => !currentScenarios.includes(id));
      
      if (missingScenarios.length > 0) {
        const missingLabel = document.createElement('div');
        missingLabel.className = 'missing-items-label';
        missingLabel.innerHTML = '<strong>Missing items:</strong> ' + 
          missingScenarios.map(id => {
            const item = question.matchingCategoriesData.items.find(i => i.id === id);
            return item ? item.text : id;
          }).join(', ');
        
        zone.appendChild(missingLabel);
      }
    });
  }
  
  /**
   * Handle drag start event
   * @param {Event} event - Drag event
   */
  function handleDragStart(event) {
    event.dataTransfer.setData('text/plain', event.currentTarget.dataset.control);
    event.currentTarget.classList.add('dragging');
  }
  
  /**
   * Handle drag end event
   * @param {Event} event - Drag event
   */
  function handleDragEnd(event) {
    event.currentTarget.classList.remove('dragging');
  }
  
  /**
   * Handle drag over event
   * @param {Event} event - Drag event
   */
  function handleDragOver(event) {
    event.preventDefault();
  }
  
  /**
   * Handle drag enter event
   * @param {Event} event - Drag event
   */
  function handleDragEnter(event) {
    event.preventDefault();
    event.currentTarget.classList.add('over');
  }
  
  /**
   * Handle drag leave event
   * @param {Event} event - Drag event
   */
  function handleDragLeave(event) {
    event.currentTarget.classList.remove('over');
  }
  
  /**
   * Handle drop event
   * @param {Event} event - Drag event
   */
  function handleDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('over');
    
    const itemId = event.dataTransfer.getData('text/plain');
    const item = document.querySelector(`.scenario-item[data-control="${itemId}"]`);
    
    if (item) {
      // Move the item to the drop zone
      event.currentTarget.appendChild(item);
    }
  }
  
  // Public API
  return {
    render,
    validateAnswer,
    getCurrentAnswer,
    showCorrectAnswer
  };
})();

// Register the type
if (!window.PerformanceTypes) window.PerformanceTypes = {};
window.PerformanceTypes.matching_categories = MatchingCategoriesType;
