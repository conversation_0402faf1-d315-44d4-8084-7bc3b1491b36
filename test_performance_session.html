<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Session Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Performance Session Test</h1>
    
    <div class="test-section">
        <h2>Session Controls</h2>
        <label for="questionCount">Number of Questions:</label>
        <input type="number" id="questionCount" value="3" min="1" max="10">
        <br><br>
        <button onclick="startSession()">Start Session</button>
        <button onclick="nextQuestion()">Next Question</button>
        <button onclick="endSession()">End Session</button>
        <button onclick="getProgress()">Get Progress</button>
    </div>
    
    <div class="test-section">
        <h2>Progress</h2>
        <div id="progressInfo">No session active</div>
        <div class="progress-bar">
            <div id="progressBar" class="progress" style="width: 0%"></div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log" class="log"></div>
    </div>

    <!-- Include performance system scripts -->
    <script src="performance/core/performance-utils.js"></script>
    <script src="performance/core/performance-state.js"></script>
    <script src="performance/core/performance-core.js"></script>
    
    <script>
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function updateProgressDisplay() {
            const progress = PerformanceCore.getSessionProgress();
            const progressInfo = document.getElementById('progressInfo');
            const progressBar = document.getElementById('progressBar');
            
            if (progress.isActive) {
                progressInfo.textContent = `Question ${progress.currentIndex + 1}/${progress.totalQuestions} (${Math.round(progress.progress)}%)`;
                progressBar.style.width = `${progress.progress}%`;
            } else {
                progressInfo.textContent = 'No session active';
                progressBar.style.width = '0%';
            }
        }
        
        function startSession() {
            const questionCount = parseInt(document.getElementById('questionCount').value) || 3;
            log(`Starting session with ${questionCount} questions...`);
            
            // Initialize the performance system first
            PerformanceCore.init();
            
            // Load some test questions
            const testQuestions = [
                { id: 'q1', type: 'matching', questionText: 'Test Question 1', category: 'A', difficulty: 'easy' },
                { id: 'q2', type: 'matching', questionText: 'Test Question 2', category: 'A', difficulty: 'medium' },
                { id: 'q3', type: 'matching', questionText: 'Test Question 3', category: 'B', difficulty: 'hard' },
                { id: 'q4', type: 'matching', questionText: 'Test Question 4', category: 'B', difficulty: 'easy' },
                { id: 'q5', type: 'matching', questionText: 'Test Question 5', category: 'C', difficulty: 'medium' }
            ];
            
            PerformanceState.setQuestions(testQuestions);
            
            // Start session
            const success = PerformanceCore.startSession(questionCount);
            
            if (success) {
                log(`Session started successfully!`);
                updateProgressDisplay();
            } else {
                log('Failed to start session');
            }
        }
        
        function nextQuestion() {
            log('Moving to next question...');
            const hasNext = PerformanceCore.nextQuestion();
            
            if (hasNext) {
                log('Moved to next question');
                updateProgressDisplay();
            } else {
                log('No more questions - session ended');
                updateProgressDisplay();
            }
        }
        
        function endSession() {
            log('Ending session...');
            PerformanceCore.endSession();
            updateProgressDisplay();
        }
        
        function getProgress() {
            const progress = PerformanceCore.getSessionProgress();
            log(`Progress: ${JSON.stringify(progress, null, 2)}`);
            updateProgressDisplay();
        }
        
        // Set up event listeners
        document.addEventListener('DOMContentLoaded', function() {
            if (window.PerformanceState) {
                PerformanceState.subscribe('session:start', (data) => {
                    log(`Session started event: ${JSON.stringify(data)}`);
                    updateProgressDisplay();
                });
                
                PerformanceState.subscribe('session:progress', (data) => {
                    log(`Session progress event: ${JSON.stringify(data)}`);
                    updateProgressDisplay();
                });
                
                PerformanceState.subscribe('session:end', (data) => {
                    log(`Session ended event: ${JSON.stringify(data)}`);
                    updateProgressDisplay();
                });
                
                PerformanceState.subscribe('question:change', (data) => {
                    log(`Question changed: ${data.question ? data.question.id : 'null'}`);
                });
            }
            
            log('Test page loaded');
        });
    </script>
</body>
</html>
