/**
 * performance-core.js
 * Core functionality for the performance-based question system
 */

const PerformanceCore = (function() {
  // Private variables
  let _initialized = false;
  let _timerInterval = null;

  // Default settings
  const _defaultSettings = {
    showHints: true,
    allowPartialCredit: true,
    adaptiveDifficulty: false,
    showExplanations: true,
    timeLimit: 0 // 0 means no time limit
  };

  /**
   * Initialize the performance system
   * @param {Object} options - Configuration options
   * @returns {Object} - The PerformanceCore instance
   */
  function init(options = {}) {
    if (_initialized) {
      console.warn('PerformanceCore already initialized');
      return this;
    }

    // Apply settings
    const settings = { ..._defaultSettings, ...options };
    PerformanceState.updateSettings(settings);

    // Subscribe to state changes
    PerformanceState.subscribe('question:change', _handleQuestionChange);
    PerformanceState.subscribe('timer:update', _handleTimerUpdate);

    // Set initialized flag
    _initialized = true;
    console.log('PerformanceCore initialized with settings:', settings);

    return this;
  }

  /**
   * Load questions from a source
   * @param {string|Array} source - URL to fetch questions from or array of questions
   * @returns {Promise} - Promise that resolves when questions are loaded
   */
  async function loadQuestions(source) {
    PerformanceState.setUIState({ isLoading: true, error: null });

    try {
      let questions;

      if (typeof source === 'string') {
        // Fetch questions from URL
        const response = await fetch(source);
        if (!response.ok) {
          throw new Error(`Failed to load questions: ${response.status} ${response.statusText}`);
        }
        questions = await response.json();
      } else if (Array.isArray(source)) {
        // Use provided array
        questions = source;
      } else {
        throw new Error('Invalid source: must be a URL or an array');
      }

      // Validate questions
      const validQuestions = questions.filter(q => {
        const isValid = PerformanceUtils.validateQuestion(q);
        if (!isValid) {
          console.warn('Invalid question:', q);
        }
        return isValid;
      });

      if (validQuestions.length === 0) {
        throw new Error('No valid questions found');
      }

      // Store questions in state
      PerformanceState.setQuestions(validQuestions);
      PerformanceState.setUIState({ isLoading: false });

      console.log(`Loaded ${validQuestions.length} questions`);
      return validQuestions;
    } catch (error) {
      PerformanceState.setUIState({ isLoading: false, error: error.message });
      console.error('Error loading questions:', error);
      throw error;
    }
  }

  /**
   * Get a question by ID
   * @param {string} id - Question ID
   * @returns {Object|null} - Question object or null if not found
   */
  function getQuestion(id) {
    const state = PerformanceState.getState();
    return state.questions.find(q => q.id === id) || null;
  }

  /**
   * Get questions filtered by criteria
   * @param {Object} filters - Filter criteria
   * @returns {Array} - Filtered questions
   */
  function getQuestions(filters = {}) {
    const state = PerformanceState.getState();

    return state.questions.filter(q => {
      for (const [key, value] of Object.entries(filters)) {
        if (Array.isArray(value)) {
          if (!value.includes(q[key])) return false;
        } else if (q[key] !== value) {
          return false;
        }
      }
      return true;
    });
  }

  /**
   * Set the current question
   * @param {string|Object} questionIdOrObject - Question ID or question object
   * @returns {Object|null} - The current question or null if not found
   */
  function setCurrentQuestion(questionIdOrObject) {
    // Stop any existing timer
    _stopTimer();

    // Set the current question in state
    PerformanceState.setCurrentQuestion(questionIdOrObject);

    // Return the current question
    const state = PerformanceState.getState();
    return state.currentQuestion;
  }

  /**
   * Submit an answer for the current question
   * @param {any} answer - The user's answer
   * @returns {Object|null} - Validation result or null if no current question
   */
  function submitAnswer(answer) {
    const state = PerformanceState.getState();
    const currentQuestion = state.currentQuestion;

    if (!currentQuestion) {
      console.error('No current question');
      return null;
    }

    // Stop the timer
    _stopTimer();

    // Get the question type
    const questionType = currentQuestion.type;

    // Validate the answer using the appropriate type handler
    let result = null;

    if (window.PerformanceTypes && window.PerformanceTypes[questionType]) {
      // Use the type-specific validator
      result = window.PerformanceTypes[questionType].validateAnswer(currentQuestion, answer);
    } else {
      console.error(`No validator found for question type: ${questionType}`);
      result = { correct: false, score: 0, details: { error: 'Unsupported question type' } };
    }

    // Save the answer and result
    PerformanceState.setUserAnswer(currentQuestion.id, answer, result);

    // Update question tracking data
    _updateQuestionTracking(currentQuestion, result);

    return result;
  }

  /**
   * Update tracking data for a question
   * @param {Object} question - The question object
   * @param {Object} result - The validation result
   * @private
   */
  function _updateQuestionTracking(question, result) {
    if (!question || !question.tracking) return;

    // Update tracking data
    question.tracking.attempts += 1;
    question.tracking.lastAttempt = new Date().toISOString();

    // Update completion status
    if (result.correct === true) {
      question.tracking.completionStatus = 'completed';
    } else if (result.score > 0) {
      question.tracking.completionStatus = 'partially_completed';
    } else {
      question.tracking.completionStatus = 'attempted';
    }

    // Update best score
    if (result.score > question.tracking.bestScore) {
      question.tracking.bestScore = result.score;
    }

    // Notify listeners of the update
    if (window.PerformanceState) {
      PerformanceState._notifyListeners('tracking:update', {
        questionId: question.id,
        tracking: question.tracking
      });
    }
  }

  /**
   * Get the user's progress
   * @returns {Object} - User progress data
   */
  function getUserProgress() {
    const state = PerformanceState.getState();
    return {
      ...state.userProgress,
      totalQuestions: state.questions.length,
      completionPercentage: state.questions.length > 0
        ? (state.userProgress.completed.length / state.questions.length) * 100
        : 0
    };
  }

  /**
   * Start a new performance session
   * @param {number} questionsCount - Number of questions for the session
   * @returns {boolean} - True if session started successfully
   */
  function startSession(questionsCount = 10) {
    if (!_initialized) {
      console.warn('PerformanceCore not initialized');
      return false;
    }

    return PerformanceState.startSession(questionsCount);
  }

  /**
   * Move to the next question in the session
   * @returns {boolean} - True if moved to next question, false if session ended
   */
  function nextQuestion() {
    if (!_initialized) {
      console.warn('PerformanceCore not initialized');
      return false;
    }

    return PerformanceState.nextQuestion();
  }

  /**
   * End the current session
   */
  function endSession() {
    if (!_initialized) {
      console.warn('PerformanceCore not initialized');
      return;
    }

    PerformanceState.endSession();
  }

  /**
   * Get current session progress
   * @returns {Object} - Session progress information
   */
  function getSessionProgress() {
    if (!_initialized) {
      console.warn('PerformanceCore not initialized');
      return { isActive: false, currentIndex: 0, totalQuestions: 0, progress: 0 };
    }

    return PerformanceState.getSessionProgress();
  }

  /**
   * Reset the system
   */
  function reset() {
    _stopTimer();
    PerformanceState.resetState();
  }

  /**
   * Handle question change event
   * @param {Object} data - Event data
   * @private
   */
  function _handleQuestionChange(data) {
    const { question } = data;

    if (question && question.timeLimit) {
      _startTimer(question.timeLimit);
    }
  }

  /**
   * Handle timer update event
   * @param {Object} data - Event data
   * @private
   */
  function _handleTimerUpdate(data) {
    const { timer } = data;

    if (timer.remaining <= 0 && timer.active) {
      _stopTimer();

      // Auto-submit the current answer if time runs out
      const state = PerformanceState.getState();
      if (state.currentQuestion) {
        // Get the current answer from the state (if any)
        const currentAnswer = state.userAnswers[state.currentQuestion.id]?.answer || null;

        // Submit the answer
        submitAnswer(currentAnswer || 'Time Expired');
      }
    }
  }

  /**
   * Start the timer
   * @param {number} seconds - Time in seconds
   * @private
   */
  function _startTimer(seconds) {
    // Stop any existing timer
    _stopTimer();

    // Set initial timer state
    PerformanceState.updateTimer(seconds);

    // Start the interval
    _timerInterval = setInterval(() => {
      const state = PerformanceState.getState();
      if (state.timer.active && state.timer.remaining > 0) {
        PerformanceState.updateTimer(state.timer.remaining - 1);
      } else {
        _stopTimer();
      }
    }, 1000);
  }

  /**
   * Stop the timer
   * @private
   */
  function _stopTimer() {
    if (_timerInterval) {
      clearInterval(_timerInterval);
      _timerInterval = null;
    }
  }

  // Public API
  return {
    init,
    loadQuestions,
    getQuestion,
    getQuestions,
    setCurrentQuestion,
    submitAnswer,
    getUserProgress,
    startSession,
    nextQuestion,
    endSession,
    getSessionProgress,
    reset
  };
})();

// Export for use in other modules
window.PerformanceCore = PerformanceCore;
