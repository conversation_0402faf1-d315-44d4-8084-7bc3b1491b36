/**
 * performance-tracking-ui.js
 * UI components for displaying performance tracking information
 */

const PerformanceTrackingUI = (function() {
  // Private variables
  let _initialized = false;
  let _trackingContainer = null;
  let _summaryContainer = null;
  let _detailedReportContainer = null;
  
  /**
   * Initialize the tracking UI
   * @param {string} containerId - ID of the container element
   * @param {Object} options - Configuration options
   * @returns {Object} - The PerformanceTrackingUI instance
   */
  function init(containerId, options = {}) {
    if (_initialized) {
      console.warn('PerformanceTrackingUI already initialized');
      return this;
    }
    
    // Get container
    _trackingContainer = document.getElementById(containerId);
    if (!_trackingContainer) {
      console.error(`Container element with ID "${containerId}" not found`);
      return this;
    }
    
    // Create UI structure
    _createUIStructure();
    
    // Subscribe to events
    if (window.PerformanceState) {
      PerformanceState.subscribe('progress:update', _handleProgressUpdate);
      PerformanceState.subscribe('state:change', _handleStateChange);
    }
    
    // Set initialized flag
    _initialized = true;
    console.log('PerformanceTrackingUI initialized');
    
    // Initial update
    updateTrackingDisplay();
    
    return this;
  }
  
  /**
   * Create the UI structure
   * @private
   */
  function _createUIStructure() {
    _trackingContainer.innerHTML = `
      <div class="performance-tracking">
        <div class="performance-tracking-summary" id="performanceTrackingSummary">
          <div class="tracking-stat">
            <div class="tracking-stat-value" id="completedQuestionsValue">0</div>
            <div class="tracking-stat-label">Completed</div>
          </div>
          <div class="tracking-stat">
            <div class="tracking-stat-value" id="remainingQuestionsValue">0</div>
            <div class="tracking-stat-label">Remaining</div>
          </div>
          <div class="tracking-stat">
            <div class="tracking-stat-value" id="correctQuestionsValue">0</div>
            <div class="tracking-stat-label">Correct</div>
          </div>
        </div>
        <div class="performance-tracking-progress">
          <div class="tracking-progress-bar">
            <div id="trackingProgressBar" class="tracking-progress"></div>
          </div>
          <div class="tracking-progress-text" id="trackingProgressText">0%</div>
        </div>
        <div class="performance-tracking-toggle">
          <button id="showDetailedReportBtn" class="tracking-toggle-btn">
            <i class="fa-solid fa-chart-line"></i> Show Detailed Report
          </button>
        </div>
        <div class="performance-tracking-detailed hidden" id="performanceTrackingDetailed">
          <!-- Detailed report will be inserted here -->
        </div>
      </div>
    `;
    
    // Cache elements
    _summaryContainer = document.getElementById('performanceTrackingSummary');
    _detailedReportContainer = document.getElementById('performanceTrackingDetailed');
    
    // Add event listeners
    const toggleBtn = document.getElementById('showDetailedReportBtn');
    if (toggleBtn) {
      toggleBtn.addEventListener('click', _toggleDetailedReport);
    }
  }
  
  /**
   * Update the tracking display
   */
  function updateTrackingDisplay() {
    if (!_initialized) {
      console.warn('PerformanceTrackingUI not initialized');
      return;
    }
    
    // Get state and metrics
    const state = window.PerformanceState ? PerformanceState.getState() : null;
    const metrics = window.PerformanceMetrics ? PerformanceMetrics.calculateMetrics() : null;
    
    if (!state || !metrics) {
      console.warn('State or metrics not available');
      return;
    }
    
    // Update summary
    const completedElement = document.getElementById('completedQuestionsValue');
    const remainingElement = document.getElementById('remainingQuestionsValue');
    const correctElement = document.getElementById('correctQuestionsValue');
    const progressBarElement = document.getElementById('trackingProgressBar');
    const progressTextElement = document.getElementById('trackingProgressText');
    
    if (completedElement && remainingElement && correctElement && progressBarElement && progressTextElement) {
      const totalQuestions = state.questions.length;
      const completedQuestions = state.userProgress.completed.length;
      const remainingQuestions = totalQuestions - completedQuestions;
      const correctQuestions = state.userProgress.correct.length;
      const progressPercentage = totalQuestions > 0 ? (completedQuestions / totalQuestions) * 100 : 0;
      
      completedElement.textContent = completedQuestions;
      remainingElement.textContent = remainingQuestions;
      correctElement.textContent = correctQuestions;
      progressBarElement.style.width = `${progressPercentage}%`;
      progressTextElement.textContent = `${Math.round(progressPercentage)}%`;
    }
    
    // Update detailed report if visible
    if (!_detailedReportContainer.classList.contains('hidden')) {
      _updateDetailedReport(metrics);
    }
  }
  
  /**
   * Toggle detailed report visibility
   * @private
   */
  function _toggleDetailedReport() {
    if (!_initialized || !_detailedReportContainer) return;
    
    const isHidden = _detailedReportContainer.classList.contains('hidden');
    const toggleBtn = document.getElementById('showDetailedReportBtn');
    
    if (isHidden) {
      _detailedReportContainer.classList.remove('hidden');
      if (toggleBtn) toggleBtn.innerHTML = '<i class="fa-solid fa-chart-line"></i> Hide Detailed Report';
      
      // Update report content
      const metrics = window.PerformanceMetrics ? PerformanceMetrics.calculateMetrics() : null;
      if (metrics) {
        _updateDetailedReport(metrics);
      }
    } else {
      _detailedReportContainer.classList.add('hidden');
      if (toggleBtn) toggleBtn.innerHTML = '<i class="fa-solid fa-chart-line"></i> Show Detailed Report';
    }
  }
  
  /**
   * Update detailed report content
   * @param {Object} metrics - Performance metrics
   * @private
   */
  function _updateDetailedReport(metrics) {
    if (!_detailedReportContainer) return;
    
    // Create report content
    let reportHTML = `
      <h3>Performance Report</h3>
      <div class="report-section">
        <h4>Overall Performance</h4>
        <div class="report-stats">
          <div class="report-stat">
            <div class="report-stat-value">${metrics.overall.totalQuestions}</div>
            <div class="report-stat-label">Total Questions</div>
          </div>
          <div class="report-stat">
            <div class="report-stat-value">${metrics.overall.correctQuestions}</div>
            <div class="report-stat-label">Correct</div>
          </div>
          <div class="report-stat">
            <div class="report-stat-value">${Math.round(metrics.overall.accuracy)}%</div>
            <div class="report-stat-label">Accuracy</div>
          </div>
        </div>
      </div>
    `;
    
    // Add performance by type if available
    if (metrics.byType && Object.keys(metrics.byType).length > 0) {
      reportHTML += `
        <div class="report-section">
          <h4>Performance by Question Type</h4>
          <table class="report-table">
            <thead>
              <tr>
                <th>Type</th>
                <th>Questions</th>
                <th>Correct</th>
                <th>Accuracy</th>
              </tr>
            </thead>
            <tbody>
      `;
      
      Object.entries(metrics.byType).forEach(([type, data]) => {
        reportHTML += `
          <tr>
            <td>${type.charAt(0).toUpperCase() + type.slice(1)}</td>
            <td>${data.totalQuestions}</td>
            <td>${data.correctQuestions}</td>
            <td>${Math.round(data.accuracy)}%</td>
          </tr>
        `;
      });
      
      reportHTML += `
            </tbody>
          </table>
        </div>
      `;
    }
    
    // Update container
    _detailedReportContainer.innerHTML = reportHTML;
  }
  
  /**
   * Handle progress update event
   * @param {Object} data - Event data
   * @private
   */
  function _handleProgressUpdate(data) {
    updateTrackingDisplay();
  }
  
  /**
   * Handle state change event
   * @param {Object} data - Event data
   * @private
   */
  function _handleStateChange(data) {
    updateTrackingDisplay();
  }
  
  // Public API
  return {
    init,
    updateTrackingDisplay
  };
})();

// Export for use in other modules
window.PerformanceTrackingUI = PerformanceTrackingUI;
