[{"id": "A2", "type": "matching", "questionText": "The security team at a manufacturing company is creating a set of security standards for employees and visitors. Select the BEST security control for each location. All of the available security controls will be used once.", "availableControls": ["Access badge", "Fencing", "Access control vestibule", "Security guard", "Authentication token", "Biometrics", "Lighting"], "locations": [{"id": "outside", "name": "Outside Building", "description": "Parking and Visitor drop-off", "icon": "fa-regular fa-building", "explanation": "Security outside of the building is focused on the safety of employees and visitors as they park their vehicles or are dropped off at the entrance. The parking lot and exterior building areas should be surrounded by fencing to control access and the parking lot should be well-lit at all times."}, {"id": "reception", "name": "Reception", "description": "Building lobby", "icon": "fa-solid fa-users", "explanation": "The reception area is the first interaction with employees or visitors. Security guards should be available to check the authorization of anyone entering the building, and the use of an access control vestibule can help manage the flow of individuals through this checkpoint."}, {"id": "data_center", "name": "Data Center Door", "description": "Entrance from inside building", "icon": "fa-solid fa-door-closed", "explanation": "Once inside, many areas of the building are readily available to employees and visitors. However, some areas of the building containing sensitive information may require additional authorization. To gain access to the data center from inside of the building, an individual would need to provide a valid access badge and perform a biometric check of their fingerprint, handprint, or a similar type of authentication factor."}, {"id": "server_admin", "name": "Server Administration", "description": "Authentication to server console in the data center", "icon": "fa-solid fa-desktop", "explanation": "Gaining access through the door of the data center doesn't provide any access to the server data. If a technician needs console access to a server, they'll need to provide the proper username, password, and authentication token. This multi-factor authentication ensures only authorized users are able to gain access to the information contained on the server."}], "correctMatches": {"outside": ["Fencing", "Lighting"], "reception": ["Security guard", "Access control vestibule"], "data_center": ["Access badge", "Biometrics"], "server_admin": ["Authentication token"]}, "difficulty": "medium", "category": "security_controls"}, {"id": "A3", "type": "matching_categories", "questionText": "Select the most appropriate security category. Some categories may be used more than once.", "categories": ["Operational", "Managerial", "Physical", "Technical"], "scenarios": [{"id": "guard_check", "text": "A guard checks the identification of all visitors"}, {"id": "vp_approval", "text": "All returns must be approved by a Vice President"}, {"id": "generator_power", "text": "A generator is used during a power outage"}, {"id": "door_access_card", "text": "Building doors can be unlocked with an access card"}, {"id": "log_transfer", "text": "System logs are transferred automatically to a SIEM"}], "correctMatches": {"guard_check": "Operational", "vp_approval": "Managerial", "generator_power": "Physical", "door_access_card": "Physical", "log_transfer": "Technical"}, "explanations": {"Operational": "Operational controls are often implemented by people instead of systems. Security guards and awareness programs are examples of an operational control.", "Managerial": "Managerial controls are administrative controls associated with security design and implementation. A set of policies and procedures would be an example of a managerial control.", "Physical": "Physical controls are used to limit physical access. Badge readers, fences, and guard shacks are categorized as physical controls.", "Technical": "Technical controls are implemented using systems. Operating system controls, firewalls, and automated processes are considered technical controls."}, "difficulty": "easy", "category": "security_controls"}, {"id": "A1", "type": "matching", "questionText": "Match the description with the most accurate attack type. Not all attack types will be used.", "availableControls": ["On-path", "Keylogger", "Rootkit", "Injection", "RFID cloning", "Vishing", "DDoS", "Supply chain"], "locations": [{"id": "vishing_scenario", "icon": "fa-solid fa-phone-volume", "name": "Attacker obtains bank account number and birth date by calling the victim", "explanation": "Vishing involves using voice communication (phone calls) to trick victims into revealing sensitive information."}, {"id": "injection_scenario", "icon": "fa-solid fa-laptop-code", "name": "Attacker accesses a database directly from a web browser", "explanation": "Injection attacks involve inserting malicious code into data inputs (like web forms) to manipulate a database or application."}, {"id": "onpath_scenario", "icon": "fa-solid fa-arrows-left-right-to-line", "name": "Attacker intercepts all communication between a client and a web server", "explanation": "An On-path attack (formerly Man-in-the-Middle) intercepts communication between two parties without their knowledge."}, {"id": "ddos_scenario", "icon": "fa-solid fa-server", "name": "Multiple attackers overwhelm a web server", "explanation": "A Distributed Denial-of-Service (DDoS) attack overwhelms a target system with traffic from multiple sources, making it unavailable."}, {"id": "keylogger_scenario", "icon": "fa-solid fa-keyboard", "name": "Attacker obtains a list of all login credentials used over the last 24 hours", "explanation": "A keylogger records keystrokes on a compromised computer, often to steal login credentials or other sensitive data."}], "correctMatches": {"vishing_scenario": ["Vishing"], "injection_scenario": ["Injection"], "onpath_scenario": ["On-path"], "ddos_scenario": ["DDoS"], "keylogger_scenario": ["Keylogger"]}, "difficulty": "medium", "category": "attack_types"}, {"id": "A4", "type": "matching", "questionText": "Match the appropriate authentication factor to each description. Each authentication factor will be used once.", "availableControls": ["Something you know", "Something you have", "Something you are", "Somewhere you are"], "locations": [{"id": "otp_phone", "name": "During the login process, your phone receives a text message with a one-time passcode"}, {"id": "pin_atm", "name": "You enter your PIN to make a deposit into an ATM"}, {"id": "fingerprint_dc", "name": "You can use your fingerprint to unlock the door to the data center"}, {"id": "vpn_login", "name": "Your login will not work unless you are connected to the VPN"}], "correctMatches": {"otp_phone": ["Something you have"], "pin_atm": ["Something you know"], "fingerprint_dc": ["Something you are"], "vpn_login": ["Somewhere you are"]}, "explanation": "Authentication factors are important to consider when developing applications or designing network infrastructures. It's useful to know each authentication factor and some examples of how that factor can be applied during the authentication process.", "difficulty": "easy", "category": "authentication"}, {"id": "B2", "type": "matching", "questionText": "An organization is deploying a mobile app to its sales team in the field. The application will be accessed from tablets for remote team members and a browser-based front-end on desktops for corporate office users. The application contains sensitive customer information, and two forms of authentication are required to launch the application. Select the best security features for each platform. A security feature will only be used once. Not all security features will be used.", "availableControls": ["Infrared sensors", "OSINT", "MDM integration", "Full Device Encryption", "Biometric authentication", "Host-based Firewall", "Anti-Malware"], "locations": [{"id": "tablet", "name": "Tablet for Field Sales", "icon": "fa-solid fa-tablet-screen-button"}, {"id": "desktop", "name": "Desktop with Browser-based Front-end", "icon": "fa-solid fa-desktop"}], "correctMatches": {"tablet": ["MDM integration", "Full Device Encryption", "Biometric authentication"], "desktop": ["Host-based Firewall", "Anti-Malware"]}, "explanation": "This question focuses on security features for end-user computing devices. As a mobile device, a tablet will need integration with an MDM (Mobile Device Manager) and encryption of all data on the device. As an additional security feature, face recognition can provide additional authentication options using the built-in camera on the tablet. Since a desktop does not often move, the security requirements are slightly different than a mobile device. Host-based firewalls and anti-virus software is common on a desktop computers.", "difficulty": "medium", "category": "device_security"}, {"id": "B1", "type": "matching", "questionText": "Match the certificate characteristic to the description:", "availableControls": ["CRL", "OCSP", "CA", "CSR"], "locations": [{"id": "crl_desc", "name": "Certificate Revocation List - A list of invalidated certificates"}, {"id": "csr_desc", "name": "Certificate Signing Request - Send the public key to be signed"}, {"id": "ca_desc", "name": "Certificate Authority - Deploy and manage certificates"}, {"id": "ocsp_desc", "name": "Online Certificate Status Protocol - The browser checks for a revoked certificate"}], "correctMatches": {"crl_desc": ["CRL"], "csr_desc": ["CSR"], "ca_desc": ["CA"], "ocsp_desc": ["OCSP"]}, "explanation": "The certificate revocation list (CRL) is a file containing a list of the revoked certificates, maintained by the CA. The certificate signing request (CSR) is sent to the CA to get a public key signed into a certificate. The certificate authority (CA) is the administrative control for PKI deployment. Online Certificate Status Protocol (OCSP) is used by browsers to check the revocation status of a certificate in real-time.", "difficulty": "hard", "category": "cryptography"}, {"id": "B3", "type": "ordering", "questionText": "Place the incident response activities in the correct order:", "items": ["Preparation", "Detection", "Analysis", "Containment", "Eradication", "Recovery", "Lessons learned"], "correctOrder": ["Preparation", "Detection", "Analysis", "Containment", "Eradication", "Recovery", "Lessons learned"], "explanation": "The standard incident response process follows these phases: Preparation (planning and setup), Detection (identifying an incident), Analysis (understanding the scope and impact), Containment (preventing spread), Eradication (removing the cause), Recovery (restoring systems), and Lessons Learned (improving future responses).", "difficulty": "medium", "category": "incident_response"}, {"id": "B4", "type": "matching", "questionText": "Match the security technology to the implementation:", "availableControls": ["Hashing", "Digital signature", "SPF", "Key escrow", "Journaling", "Obfuscation"], "locations": [{"id": "hashing_impl", "name": "Store a password on an authentication server"}, {"id": "digsig_impl", "name": "Verify a sender's identity"}, {"id": "spf_impl", "name": "Authenticate the server sending an email"}, {"id": "escrow_impl", "name": "Store keys with a third-party"}, {"id": "journal_impl", "name": "Prevent data corruption when a system fails"}, {"id": "obfusc_impl", "name": "Modify a script to make it difficult to understand"}], "correctMatches": {"hashing_impl": ["Hashing"], "digsig_impl": ["Digital signature"], "spf_impl": ["SPF"], "escrow_impl": ["Key escrow"], "journal_impl": ["Journaling"], "obfusc_impl": ["Obfuscation"]}, "explanation": "Hashing creates a one-way function for password storage. Digital signatures ensure integrity and non-repudiation using hashing and asymmetric crypto. SPF validates email sending servers. Key escrow stores decryption keys with a trusted third party. Journaling prevents data corruption by writing changes before committing. Obfuscation makes code harder to read.", "difficulty": "medium", "category": "security_technologies"}, {"id": "B5", "type": "matching_categories", "questionText": "Select the data state that best fits the description. Each data state will be used more than once.", "categories": ["Data in-transit", "Data at-rest", "Data in-use"], "scenarios": [{"id": "s1", "text": "All switches in a data center are connected with an 802.1Q trunk"}, {"id": "s2", "text": "Sales information is uploaded daily from a remote site using a satellite network"}, {"id": "s3", "text": "A company stores customer purchase information in a MySQL database"}, {"id": "s4", "text": "An application decrypts credit card numbers and expiration dates to validate for approval"}, {"id": "s5", "text": "An authentication program performs a hash of all passwords"}, {"id": "s6", "text": "An IPS identifies a SQL injection attack and removes the attack frames from the network"}, {"id": "s7", "text": "An automatic teller machine validates a user's PIN before allowing a deposit"}, {"id": "s8", "text": "Each time a spreadsheet is updated, all of the cells containing formulas are automatically updated"}, {"id": "s9", "text": "All weekly backup tapes are transported to an offsite storage facility"}, {"id": "s10", "text": "All user spreadsheets are stored on a cloud-based file sharing service"}], "correctMatches": {"s1": "Data in-transit", "s2": "Data in-transit", "s3": "Data at-rest", "s4": "Data in-use", "s5": "Data in-use", "s6": "Data in-transit", "s7": "Data in-use", "s8": "Data in-use", "s9": "Data at-rest", "s10": "Data at-rest"}, "explanations": {"Data in-transit": "Data in-transit moves across the network (e.g., trunk links, satellite uploads, network traffic inspection).", "Data at-rest": "Data at-rest is located on a storage device (e.g., database, backup tapes, cloud storage).", "Data in-use": "Data in-use is actively being processed in memory (e.g., decryption, hashing, validation, spreadsheet calculations)."}, "difficulty": "hard", "category": "data_security"}, {"id": "C1", "type": "matching_categories", "questionText": "Refer to the following firewall rules:\nRule # | Source IP | Destination IP | Protocol (TCP/UDP) | Port # | Allow/Block\n-------|-----------|----------------|--------------------|--------|------------\n1      | Any       | **********     | TCP                | 22     | Allow\n2      | Any       | ***********    | TCP                | 80     | Allow\n3      | Any       | ***********    | TCP                | 443    | Allow\n4      | Any       | **********     | TCP                | 3389   | Allow\n5      | Any       | Any            | UDP                | 53     | Allow\n6      | Any       | Any            | UDP                | 123    | Allow\n7      | Any       | Any            | ICMP               |        | Block\n\nCategorize the following traffic flows as ALLOWED or BLOCKED through the firewall:", "categories": ["ALLOWED", "BLOCKED"], "scenarios": [{"id": "ssh", "text": "Use a secure terminal to connect to **********"}, {"id": "rdp", "text": "Share the desktop on server ***********"}, {"id": "dns", "text": "Perform a DNS query from ********** to 9.9.9.9"}, {"id": "web", "text": "View web pages on ***********"}, {"id": "ldap", "text": "Authenticate to an LDAP server at **********"}, {"id": "ntp", "text": "Synchronize the clock on a server at 10.1.10.17"}], "correctMatches": {"ssh": "ALLOWED", "rdp": "BLOCKED", "dns": "ALLOWED", "web": "ALLOWED", "ldap": "BLOCKED", "ntp": "ALLOWED"}, "explanation": "SSH (TCP/22) to ********** matches Rule 1 (Allow). RDP (TCP/3389) to *********** doesn't match Rule 4 (wrong destination) or any other allow rule, so it's blocked. DNS (UDP/53) matches Rule 5 (Allow). Web (TCP/80 or 443) to *********** matches Rule 2 or 3 (Allow). LDAP (TCP/389 or 636) to ********** doesn't match any allow rule, so it's blocked. NTP (UDP/123) matches Rule 6 (Allow).", "difficulty": "hard", "category": "network_security"}, {"id": "C2", "type": "matching", "questionText": "Match the device to the description. Some device types will not be used.", "availableControls": ["IPS", "Proxy", "Router", "Load balancer", "WAF"], "locations": [{"id": "ips_desc", "name": "Block SQL injection over an Internet connection"}, {"id": "proxy_desc", "name": "Intercept all browser requests and cache the results"}, {"id": "router_desc", "name": "Forward packets between separate VLANs"}, {"id": "lb_desc", "name": "Configure a group of redundant web servers"}, {"id": "waf_desc", "name": "Evaluate the input to a browser-based application"}], "correctMatches": {"ips_desc": ["IPS"], "proxy_desc": ["Proxy"], "router_desc": ["Router"], "lb_desc": ["Load balancer"], "waf_desc": ["WAF"]}, "explanation": "An IPS blocks exploits like SQL injection. A Proxy intercepts and caches web requests. A Router forwards traffic between networks/VLANs. A Load Balancer distributes traffic across servers. A WAF inspects web application input.", "difficulty": "medium", "category": "network_devices"}, {"id": "C3", "type": "matching", "questionText": "Match the attack type to the characteristic:", "availableControls": ["DDoS", "Replay", "Rootkit", "Brute force", "<PERSON><PERSON>", "Injection"], "locations": [{"id": "ddos_char", "name": "A website stops responding to normal requests"}, {"id": "replay_char", "name": "Data is captured and retransmitted to a server"}, {"id": "rootkit_char", "name": "The malware is designed to remain hidden on a computer system"}, {"id": "brutef_char", "name": "A list of passwords are attempted with a known username"}, {"id": "phish_char", "name": "An email link redirects a user to a site that requests login credentials"}, {"id": "inject_char", "name": "Permissions are circumvented by adding additional code as application input"}], "correctMatches": {"ddos_char": ["DDoS"], "replay_char": ["Replay"], "rootkit_char": ["Rootkit"], "brutef_char": ["Brute force"], "phish_char": ["<PERSON><PERSON>"], "inject_char": ["Injection"]}, "explanation": "DDoS overwhelms a service. Replay captures and retransmits data. Rootkits hide malware. Brute force tries many passwords. Phishing tricks users into revealing credentials. Injection adds malicious code to input.", "difficulty": "medium", "category": "attack_types"}, {"id": "C4", "type": "matching", "questionText": "Match the cryptography technology to the description:", "availableControls": ["Key stretching", "Steganography", "Collision", "Masking", "Asymmetric", "Salting"], "locations": [{"id": "stretch_desc", "name": "Create a stronger key using multiple processes"}, {"id": "steg_desc", "name": "Data is hidden within another media type"}, {"id": "coll_desc", "name": "Different inputs create the same hash"}, {"id": "mask_desc", "name": "Sensitive data is hidden from view"}, {"id": "asym_desc", "name": "A different key is used for decryption than encryption"}, {"id": "salt_desc", "name": "Information is added to make a unique hash"}], "correctMatches": {"stretch_desc": ["Key stretching"], "steg_desc": ["Steganography"], "coll_desc": ["Collision"], "mask_desc": ["Masking"], "asym_desc": ["Asymmetric"], "salt_desc": ["Salting"]}, "explanation": "Key stretching makes keys stronger via hashing/processes. Steganography hides data in media. Collision is when different inputs produce the same hash. Masking hides sensitive data (e.g., ****1234). Asymmetric crypto uses different keys for encryption/decryption. Salting adds unique data to passwords before hashing.", "difficulty": "hard", "category": "cryptography"}, {"id": "C5", "type": "matching", "questionText": "Add the most applicable security technologies to the following scenarios:", "availableControls": ["VPN", "Sandboxing", "NGFW", "SD-WAN", "802.1X"], "locations": [{"id": "vpn_scen", "name": "A field service engineer uses their corporate laptop at coffee shops and hotels"}, {"id": "sandbox_scen", "name": "Software developers run a series of tests before deploying an application"}, {"id": "ngfw_scen", "name": "An administrator prevents employees from visiting known-malicious web sites"}, {"id": "sdwan_scen", "name": "Directly access cloud-based services from all corporate locations"}, {"id": "8021x_scen", "name": "Users connecting to the network should use their corporate authentication credentials"}], "correctMatches": {"vpn_scen": ["VPN"], "sandbox_scen": ["Sandboxing"], "ngfw_scen": ["NGFW"], "sdwan_scen": ["SD-WAN"], "8021x_scen": ["802.1X"]}, "explanation": "VPN protects data on public Wi-Fi. Sandboxing provides isolated testing environments. NGFW offers advanced filtering like blocking malicious sites. SD-WAN optimizes access to cloud services across locations. 802.1X provides port-based network access control using authentication.", "difficulty": "medium", "category": "security_technologies"}]