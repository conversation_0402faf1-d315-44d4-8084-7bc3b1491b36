[{"question": "A6. A company has hired a third-party to gather information about the \ncompany’s servers and data. This third-party will not have direct access to \nthe company's internal network, but they can gather information from any \nother source. Which of the following would BEST describe this approach?", "options": ["Vulnerability scanning", "Passive reconnaissance", "Supply chain analysis", "Regulatory audit"], "answer": "Passive reconnaissance", "explanation": "B. Passive reconnaissance\nPassive reconnaissance focuses on gathering as much information from \nopen sources such as social media, corporate websites, and business \norganizations.\nThe incorrect answers:\nA. Vulnerability scanning\nSome active reconnaissance tests will query systems directly to see if a \nvulnerability currently exists.\nC. Supply chain analysis\nA supply chain analysis will examine the security associated with a \nsupplier, and the analysis will not provide any information regarding a \ncompany's own servers and data.\nD. Regulatory audit\nA regulatory audit is a detailed security analysis based on existing laws or \nprivate guidelines. A regulatory audit commonly requires access to internal \nsystems and data.\nMore information:\nSY0-701, Objective 5.5 - Penetration Tests https://professormesser.link/*********"}, {"question": "A7. A company's email server has received an email from a third-party, but the \norigination server does not match the list of authorized devices. Which of \nthe following would determine the disposition of this message?", "options": ["SPF", "NAC", "DMARC", "DKIM"], "answer": "DMARC", "explanation": "C. DMARC\nDMARC (Domain-based Message Authentication Reporting and \nConformance) specifies the disposition of spam emails. The legitimate \nowner of the originating email domain can choose to have these messages \naccepted, sent to a spam folder, or rejected.\nThe incorrect answers:\nA. SPF\nSPF (Sender Policy Framework) is a list of all authorized mail servers for \na specific domain. All legitimate emails would be sent from one of the \nservers listed in the SPF configuration.\nB. NAC\nNAC (Network Access Control) is a way to limit network access to only \nauthorized users. NAC is not commonly used to manage the transfer of \nemail messages.\nD. DKIM\nDKIM (Domain Keys Identified Mail) provides a way to validate all \ndigitally signed messages from a specific email server. DKIM does not \ndetermine how the receiving server categorizes these digitally signed \nmessages.\nMore information:\nSY0-701, Objective 4.5 - Email Security https://professormesser.link/*********"}, {"question": "A8. Which of these threat actors would be MOST likely to attack systems for \ndirect financial gain?", "options": ["Organized crime", "<PERSON><PERSON><PERSON><PERSON>", "Nation state", "Shadow IT"], "answer": "Organized crime", "explanation": "A. Organized crime\nAn organized crime actor is motivated by money, and their hacking \nobjectives are usually based around objectives that can be easily exchanged \nfor financial capital.\nThe incorrect answers:\n<PERSON><PERSON>tivist\nA hacktivist is focused on a political agenda and not commonly on a \nfinancial gain.\nC. Nation state\nNation states are already well funded, and their primary objective is not \nusually based on revenue or income.\nD. Shadow IT\nShadow IT describes part of the organization that works around the \nexisting IT department to build their own applications and infrastructure.\nMore information:\nSY0-701, Objective 2.1 - Threat Actors https://professormesser.link/*********"}, {"question": "A9. A security administrator has examined a server recently compromised by \nan attacker, and has determined the system was exploited due to a known \noperating system vulnerability. Which of the following would BEST \ndescribe this finding?", "options": ["Root cause analysis", "E-discovery", "Risk appetite", "Data subject"], "answer": "Root cause analysis", "explanation": "A. Root cause analysis\nThe goal of a root cause analysis is to explain the ultimate cause of an \nincident. Once the cause is known, it becomes easier to protect against \nsimilar attacks in the future.\nThe incorrect answers:\nB. E-discovery\nE-discovery relates to the collection, preparation, review, interpretation, \nand production of electronic documents. E-discovery itself is not involved \nwith the research and determination of an attack's root cause.\nC. Risk appetite\nA risk appetite describes the amount of risk an organization is willing to \ntake before taking any action to reduce that risk. Risk appetite is not part \nof a root cause analysis.\nD. Data subject\nA data subject describes any information relating to an identified or \nidentifiable natural person, especially when describing or managing private \ninformation about the subject.\nMore information:\nSY0-701, Objective 4.8 - Incident Planning https://professormesser.link/701040802"}, {"question": "A10.  A city is building an ambulance service network for emergency medical \ndispatching. Which of the following should have the highest priority?", "options": ["Integration costs", "Patch availability", "System availability", "Power usage"], "answer": "System availability", "explanation": "C. System availability\nRequests to emergency services are often critical in nature, and it's \nimportant for a dispatching system to always be available when a call is \nmade.\nThe incorrect answers:\nA. Integration costs\nWhen lives are on the line, the cost is not commonly the most important \naspect of a system integration.\nB. Patch availability\nAlthough it's important to always keep systems patched, it's more \nimportant that a life saving service be available to those who might need it.\nD. Power usage\nPower usage is not usually the most important consideration when \nbuilding a critical healthcare and emergency service infrastructure.\nMore information:\nSY0-701, Objective 3.1 - Infrastructure Considerations https://professormesser.link/701030104"}, {"question": "A11. A system administrator receives a text alert when access rights are \nchanged on a database containing private customer information. Which \nof the following would describe this alert?", "options": ["Maintenance window", "Attestation and acknowledgment", "Automation", "External audit"], "answer": "Automation", "explanation": "C. Automation\nAutomation ensures that compliance checks can be performed on a \nregular basis without the need for human intervention. This can be \nespecially useful to provide alerts when a configuration change causes an \norganization to be out of compliance.\nThe incorrect answers:\nA. Maintenance window\nA maintenance window describes the scheduling associated with the \nchange control process. Systems and services generally have limited \navailability during a maintenance window.\nB. Attestation and acknowledgment\nWith compliance, the process of attestation and acknowledgment is the \nfinal verification of the formal compliance documentation. An alert from \nan automated process would not qualify as attestation.\nD. External audit\nAn external audit can be a valuable tool for verifying the compliance \nprocess, but an automated alert from a monitoring system would not be \npart of an external audit.\nMore information:\nSY0-701, Objective 5.4 - Compliance https://professormesser.link/*********"}, {"question": "A12. A security administrator is concerned about the potential for data \nexfiltration using external storage drives. Which of the following would \nbe the BEST way to prevent this method of data exfiltration?", "options": ["Create an operating system security policy to block the use of removable media", "Monitor removable media usage in host-based firewall logs", "Only allow applications that do not use removable media", "Define a removable media block rule in the UTM"], "answer": "Create an operating system security policy to block the use of removable media", "explanation": "<PERSON><PERSON> <PERSON>reate an operating system security policy to prevent\nthe use of removable media\nRemovable media uses hot-pluggable interfaces such as USB to connect \nstorage drives. A security policy in the operating system can prevent any \nfiles from being written to a removable drive.\nThe incorrect answers:\nB. Monitor removable media usage in host-based firewall logs\nA host-based firewall monitors traffic flows and does not commonly log \nhardware or USB drive access.\nC. Only allow applications that do not use removable media\nFile storage access options are not associated with applications, so it’s not \npossible to allow based on external storage drive usage.\nD. Define a removable media block rule in the UTM\nA UTM (Unified Threat Manager) watches traffic flows across the \nnetwork and does not commonly manage the storage options on individual \ncomputers.\nMore information:\nSY0-701, Objective 2.2 - Common Threat Vectors https://professormesser.link/*********"}, {"question": "A13. A company creates a standard set of government reports each calendar \nquarter. Which of the following would describe this type of data?", "options": ["Data in use", "Obfuscated", "Trade secrets", "Regulated"], "answer": "Regulated", "explanation": "D. Regulated\nReports and information created for governmental use are regulated by \nlaws regarding the disclosure of certain types of data.\nThe incorrect answers:\nA. Data in use\nData in use describes information actively processing in the memory of a \nsystem, such as system RAM, CPU registers, or CPU cache. Government \nreports are static documents and are not actively being processed.\nB. Obfuscated\nObfuscation describes the modification of data to make something \nunderstandable into something very difficult to understand. Information \ncontained in a government report is relatively easy to understand and \nwould not be considered obfuscated data.\nC. Trade secrets\nTrade secrets are the private details a company uses as part of their normal \nbusiness processes, and these trade secrets are not shared with any other \norganization or business.\nMore information:\nSY0-701, Objective 3.3 - Data Types and Classifications https://professormesser.link/*********"}, {"question": "A14. An insurance company has created a set of policies to handle data \nbreaches. The security team has been given this set of requirements based \non these policies:\n• Access records from all devices must be saved and archived\n• Any data access outside of normal working hours  \nmust be immediately reported\n• Data access must only occur inside of the country\n• Access logs and audit reports must be created from a single database\nWhich of the following should be implemented by the security team to \nmeet these requirements? (trei)", "multiple answers": 3, "options": ["Restrict login access by IP address and GPS location", "Require government-issued identification during the onboarding process", "Add additional password complexity for accounts that access data", "Conduct monthly permission auditing", "Consolidate all logs on a SIEM", "Archive the encryption keys of all disabled accounts", "Enable time-of-day restrictions on the authentication server"], "answer": ["Restrict login access by IP address and GPS location", "Consolidate all logs on a SIEM", "Enable time-of-day restrictions on the authentication server"], "explanation": "The Answer: A. Restrict login access by IP address and GPS location,\n  E. Consolidate all logs on a SIEM, and\n  G. Enable time-of-day restrictions on\n  the authentication server\n  Adding location-based policies will prevent direct data access from outside\n  of the country. Saving log information from all devices and creating audit\n  reports from a single database can be implemented through the use of a\n  SIEM (Security Information and Event Manager). Adding a check for the\n  time-of-day will report any access that occurs during non-working hours.\n  52 Practice Exam A - Answers\n  The incorrect answers:\n  B. Require government-issued identification during the  onboarding process\n  Requiring proper identification is always a good idea, but it’s not one of\n  the listed requirements.\n  C. Add additional password complexity for accounts that access data\n  Additional password complexity is another good best practice, but it’s not\n  part of the provided requirements.\n  D. Conduct monthly permission auditing\n  No requirements for ongoing auditing were included in the requirements,  but ongoing auditing is always an important consideration.\n  F. Archive the encryption keys of all disabled accounts\n  If an account is disabled, there may still be encrypted data that needs to be  recovered later. Archiving the encryption keys will allow access to that data\n  after the account is no longer in use.\n More information:\n  SY0-701, Objective 4.6 - Access Controls\n  https://professormesser.link/*********"}, {"question": "A15. A security engineer, is viewing this record from the firewall logs:\nUTC 04/05/2023 03:09:15809 AV Gateway Alert\n  ************** 80 -> *********** 60818\n  Gateway Anti-Virus Alert:\n  XPACK.A_7854 (Trojan) blocked\nWhich of the following can be observed from this log information?", "options": ["The victim's IP address is **************", "A download was blocked from a web server", "A botnet DDoS attack was blocked", "The Trojan was blocked, but the file was not"], "answer": "A download was blocked from a web server", "explanation": "B. A download was blocked from a web server\nA traffic flow from a web server port number (80) to a device port (60818) \nindicates that this traffic flow originated on port 80 of the web server. A \nfile download is one of the most common ways to deliver a Trojan, and \nthis log entry shows that the file containing the XPACK.A_7854 Trojan \nwas blocked.\nThe incorrect answers:\nA. The victim’s IP address is **************\nThe format for this log entry uses an arrow to differentiate between the \nattacker and the victim. The attacker IP address is **************, and the \nvictim’s IP address is ***********.\nC. A botnet DDoS attack was blocked\nA botnet attack would not commonly include a Trojan horse as part of a \ndistributed denial of service (DDoS) attack.\nD. The Trojan was blocked, but the file was not\nA Trojan horse attack involves malware that is disguised as legitimate \nsoftware. The Trojan malware and the file are the same entity, so there isn’t \na way to decouple the malware from the file.\nMore information:\nSY0-701, Objective 4.9 - Log Files https://professormesser.link/*********"}, {"question": "A16. A user connects to a third-party website and receives this message:  \n \nYour connection is not private.  \nNET::ERR_CERT_INVALID  \n \nWhich of the following attacks would be the MOST likely reason  \nfor this message?", "options": ["Brute force", "DoS", "On-path", "Deauthentication"], "answer": "On-path", "explanation": "C. On-path\nAn on-path attack is often associated with a third-party who is actively \nintercepting network traffic. This entity in the middle would not be able \nto provide a valid SSL certificate for a third-party website, and this error \nwould appear in the browser as a warning.\nThe incorrect answers:\nA. Brute force\nA brute force attack is commonly associated with password hacks. Brute \nforce attacks would not cause the certificate on a website to be invalid.\nB. DoS\nA DoS (Denial of Service) attack would prevent communication to a \nserver and most likely provide a timeout error. This error is not related to a \nservice availability issue.\nD. Deauthentication\nDeauthentication attacks are commonly associated with wireless networks, \nand they usually cause disconnects and lack of connectivity. The error \nmessage in this example does not appear to be associated with a network \noutage or disconnection.\nMore information:\nSY0-701, Objective 2.4 - On-Path Attacks https://professormesser.link/701020409"}, {"question": "A17. Which of the following would be the BEST way to provide a website \nlogin using existing credentials from a third-party site?", "options": ["Federation", "802.1X", "EAP", "SSO"], "answer": "Federation", "explanation": "A. Federation\nFederation would allow members of one organization to authenticate \nusing the credentials of another organization.\nThe incorrect answers:\nB. 802.1X\n802.1X is a useful authentication protocol, but it needs additional \nfunctionality to authenticate across multiple user databases.\nC. EAP\nEAP (Extensible Authentication Protocol) is an authentication  \nframework commonly associated with network access control. EAP by \nitself does not provide the federation needed to authenticate users to a \nthird-party access database.\nD. SSO\nSSO (Single Sign-On) describes the process of enabling a single \nauthentication to grant access to many different network services. \nObtaining login credentials from a third-party access database does not \ndescribe the process used by SSO.\nMore information:\nSY0-701, Objective 4.6 - Identity and Access Management https://professormesser.link/*********"}, {"question": "A18. A system administrator is working on a contract that will specify a \nminimum required uptime for a set of Internet-facing firewalls. The \nadministrator needs to know how often the firewall hardware is expected \nto fail between repairs. Which of the following would BEST describe this \ninformation?", "options": ["MTBF", "RTO", "MTTR", "RPO"], "answer": "MTBF", "explanation": "A. MTBF\nThe MTBF (Mean Time Between Failures) is a prediction of how often a \nrepairable system will fail.\nThe incorrect answers:\nB. RTO\nRTO (Recovery Time Objectives) define a timeframe needed to restore a \nparticular service level.\nC. MTTR\nMTTR (Mean Time to Restore) is the amount of time it takes to repair a \ncomponent.\nD. RPO\nRPO (Recovery Point Objective) describes the minimum data or \noperational state required to categorize a system as recovered.\nMore information:\nSY0-701, Objective 5.2 - Business Impact Analysis https://professormesser.link/*********"}, {"question": "A19.  An attacker calls into a company’s help desk and pretends to be the \ndirector of the company’s manufacturing department. The attacker \nstates that they have forgotten their password and they need to have the \npassword reset quickly for an important meeting. What kind of attack \nwould BES<PERSON> describe this phone call?", "options": ["Social engineering", "Supply chain", "Watering hole", "On-path"], "answer": "Social engineering", "explanation": "A. Social engineering\nThis social engineering attack uses impersonation to take advantage of \nauthority and urgency principles in an effort to convince someone else to \ncircumvent normal security controls.\nThe incorrect answers:\nB. Supply chain\nA supply chain attack focuses on the equipment or raw materials used to \ndeliver products or services to an organization or user. A call to the help \ndesk would not be categorized as part of the supply chain.\nC. Watering hole\nA watering hole attack uses a third-party site to perform attacks outside of \na user's local (and usually more secure) network.\nD. On-path\nAn on-path attack commonly occurs without any knowledge to the parties \ninvolved, and there’s usually no additional notification that an attack is \nunderway. In this question, the attacker contacted the help desk engineer \ndirectly.\nMore information:\nSY0-701, Objective 2.2 - Impersonation https://professormesser.link/701020203"}, {"question": "A20. Two companies have been working together for a number of months, \nand they would now like to qualify their partnership with a broad formal \nagreement between both organizations. Which of the following would \ndescribe this agreement?", "options": ["SLA", "SOW", "MOA", "NDA"], "answer": "MOA", "explanation": "C. MOA\nAn MOA (Memorandum of Agreement) is a formal document where \nboth sides agree to a broad set of goals and objectives associated with the \npartnership.\nThe incorrect answers:\nA. SLA\nAn SLA (Service Level Agreement) is commonly provided as a formal \ncontract between two parties that documents the minimum terms for \nservices provided. The SLA often provides very specific requirements and \nexpectations between both parties.\nB. SOW\nAn SOW (Statement of Work) is a detailed list of items to be completed \nas part of overall project deliverables. For example, a list of expected job \ntasks associated with a firewall installation would be documented in an \nSOW.\nD. NDA\nAn NDA (Non-Disclosure Agreement) is a confidentiality agreement \nbetween parties. This question did not mention any requirement for \nprivacy or confidentiality.\nMore information:\nSY0-701, Objective 5.3 - Agreement Types https://professormesser.link/*********"}, {"question": "A21.  Which of the following would explain why a company would \nautomatically add a digital signature to each outgoing email message?", "options": ["Confidentiality", "Integrity", "Authentication", "Availability"], "answer": "Integrity", "explanation": "B. Integrity\nIntegrity refers to the trustworthiness of data. A digital signature allows \nthe recipient to confirm that none of the data has been changed since the \ndigital signature was created.\nThe incorrect answers:\nA. Confidentiality\nConfidentiality describes the privacy of data. Encrypting traffic sent over \na VPN or encrypting files stored on a flash drive would be an example of \ndata confidentiality.\nC. Authentication\nAuthentication refers to the process of verifying the identity of an \nindividual or system. A username and password is a common method \nof authentication, but digital signatures are not commonly used as an \nauthentication method.\nD. Availability\nAvailability describes the ability of an authorized user to access data. \nA digital signature does not provide any features associated with the \navailability of the data.\nMore information:\nSY0-701, Objective 1.2 - The CIA Triad https://professormesser.link/*********"}, {"question": "A22. The embedded OS in a company’s time clock appliance is configured to \nreset the file system and reboot when a file system error occurs. On one \nof the time clocks, this file system error occurs during the startup process \nand causes the system to constantly reboot. Which of the following \nBEST describes this issue?", "options": ["Memory injection", "Resource consumption", "Race condition", "Malicious update"], "answer": "Race condition", "explanation": "C. Race condition\nA race condition occurs when two processes occur at similar times, and \nusually with unexpected results. The file system problem can often be fixed \nbefore a reboot, but the reboot is occurring before the fix can be applied. \nThis has created a race condition that results in constant reboots.\nThe incorrect answers:\nA. Memory injection\nA memory injection is commonly used by malicious software to add code \nto the memory of an existing process. The issue in this question was related \nto a file system error and was not part of a malicious data injection.\nB. Resource consumption\nIf the time clock was running out of storage space or memory, it would \nmost likely be unusable. In this example, the issue isn’t based on a lack of \nresources.\nD. Malicious update\nA malicious update occurs when a software patch installs unwanted or \nunauthorized code. Many attackers will use software patches to install \ntheir own malicious code during a software update.\nMore information:\nSY0-701, Objective 2.3 - Race Conditions https://professormesser.link/*********"}, {"question": "A23. A recent audit has found that existing password policies do not include \nany restrictions on password attempts, and users are not required to \nperiodically change their passwords. Which of the following would \ncorrect these policy issues? (doua)\n ", "multiple answers": 2, "options": ["Password complexity", "Password expiration", "Account lockout", "Password reuse", "Password managers"], "answer": ["Password expiration", "Account lockout"], "explanation": "The Answer: B. Password expiration and C. Account lockout\nPassword expiration requires users to change their passwords on a regular \nbasis. Account lockout prevents an attacker from using a brute force \nattack to guess a password.\nThe incorrect answers:\nA. Password complexity\nPassword complexity requires users to create passwords that include a \nmixture of uppercase and lowercase letters, numbers, and special \ncharacters. Password complexity does not address the lack of password \nexpiration or account lockout.\nD. Password reuse\nPassword reuse is the practice of using the same password for multiple \naccounts. Password reuse does not address the lack of password expiration \nor account lockout.\nE. Password managers\nPassword managers store and manage passwords for multiple accounts. \nPassword managers do not address the lack of password expiration or \naccount lockout.\nMore information:\nSY0-701, Objective 4.6 - Identity and Access Management https://professormesser.link/*********"}, {"question": "A24. What kind of security control is associated with a login banner?", "options": ["Preventative", "Detective", "Corrective", "Deterrent", "Compensating", "Directive"], "answer": "Deterrent", "explanation": "The Answer: B. Deterrent \n A deterrent control does not directly stop an attack, but it may discourage an action.\nThe incorrect answers: \nA. Preventive A preventive control physically limits access to a device or area.\nC. Corrective \nA corrective control can actively work to mitigate any damage.\n D. Detective A detective control may not prevent access, but it can identify and record any intrusion attempts.\nE. Compensating A compensating security control doesn’t prevent an attack, but it does restore from an attack using other means.\n F. Directive A directive control is relatively weak control which relies on security compliance from the end users.\n More information: SY0-701, Objective 1.1 - Security Controls https://professormesser.link/*********"}, {"question": "A25.  An internal audit has discovered four servers that have not been updated \nin over a year, and it will take two weeks to test and deploy the latest \npatches. Which of the following would be the best way to quickly \nrespond to this situation in the meantime?", "options": ["Purchase cybersecurity insurance", "Implement an exception for all data center services", "Move the servers to a protected segment", "Hire a third-party to perform an extensive audit"], "answer": "Move the servers to a protected segment", "explanation": "C. Move the servers to a protected segment\nSegmenting the servers to their own protected network would allow \nfor additional security controls while still maintaining the uptime and \navailability of the systems.\nThe incorrect answers:\nA. Purchase cybersecurity insurance\nCybersecurity insurance can help plan for financial issues during a \nsignificant attack, but it wouldn't provide any assistance for mitigating \npotential vulnerabilities during this two week period.\nB. Implement an exception for all data center services\nSecurity exceptions should be rare, and they should be very specific \nto a small number of systems. It would be risky to create a broad \nsecurity exception for systems which are not in-scope for the identified \nvulnerability.\nD. Hire a third-party to perform an extensive audit\nAudits take time, and hiring a third-party to perform an audit takes even \nlonger. By the time a third-party audit was underway, the problematic \nsystems would have already been tested and patched.\nMore information:\nSY0-701, Objective 4.3 - Vulnerability Remediation https://professormesser.link/701040305"}, {"question": "A26. A business manager is documenting a set of steps for processing orders \nif the primary Internet connection fails. Which of these would BEST \ndescribe these steps?", "options": ["Platform diversity", "Continuity of operations", "Cold site recovery", "Tabletop exercise"], "answer": "Continuity of operations", "explanation": "B. Continuity of operations\nIt's always useful to have an alternative set of processes to handle any type \nof outage or issue. Continuity of operations planning ensures that the \nbusiness will continue to operate when these issues occur.\nThe incorrect answers:\nA. Platform diversity\nUsing different operating systems and platforms can help mitigate issues \nassociated with a single OS, but it wouldn't provide any mitigation if the \nprimary Internet connection was no longer available.\nC. Cold site recovery\nA cold site takes time to build, and the time and expense associated with \na disaster recovery switchover would be extensive. By the time a cold site \nwas enabled, the primary Internet connection may already be restored and \nmany alternative recovery options could have potentially been deployed.\nD. Tabletop exercise\nA tabletop exercise usually consists of a meeting where members of a \nrecovery team or disaster recovery talk through a disaster scenario.\nMore information:\nSY0-701, Objective 3.4 - Resiliency https://professormesser.link/*********"}, {"question": "A27. A company would like to examine the credentials of each individual \nentering the data center building. Which of the following would BEST \nfacilitate this requirement?", "options": ["Access control vestibule", "Video surveillance", "Pressure sensors", "<PERSON><PERSON><PERSON>"], "answer": "Access control vestibule", "explanation": "A. Access control vestibule\nAn access control vestibule is a room designed to restrict the flow of \nindividuals through an area. These are commonly used in high security \nareas where each person needs to be evaluated and approved before access \ncan be provided.\nThe incorrect answers:\nB. Video surveillance\nAlthough video surveillance can assist with monitoring access to a \nbuilding or room, it doesn't provide a way to validate the credentials of \neach visitor.\nC. Pressure sensors\nPressure sensors are commonly used on doors or windows to detect \nmovement in those devices. However, pressure sensors would not be used \nto check visitor credentials.\nD. Bollards\nBollards and barricades are often used on the exterior of a facility to \nprevent access to motorized vehicles and channel people through a specific \naccess location.\nMore information:\nSY0-701, Objective 1.2 - Physical Security https://professormesser.link/*********"}, {"question": "A28. A company stores some employee information in encrypted form, but \nother public details are stored as plaintext. Which of the following would \nBEST describe this encryption strategy?", "options": ["Full-disk", "Record", "Asymmetric", "Key escrow"], "answer": "Record", "explanation": "B. Record\nRecord-level encryption is commonly used with databases to encrypt \nindividual columns within the database. This would store some \ninformation in the database as plaintext and other information as \nencrypted data.\nThe incorrect answers:\nA. Full-disk\nFull-disk encryption ensures that all data on a storage drive is protected. \nFull-disk encryption protects all data on the drive, and none of the \ninformation would remain as the original plaintext.\nC. Asymmetric\nAsymmetric encryption uses a public and private key pair to encrypt data. \nAsymmetric encryption does not store some information as plaintext and \nother information as encrypted data.\nD. Key escrow\nKey escrow describes the storage and management of decryption keys by \na third-party. Key escrow does not determine which data is selected for \nencryption or the method of encryption.\nMore information:\nSY0-701, Objective 1.4 - Encrypting Data https://professormesser.link/*********"}, {"question": "A29. A company would like to minimize database corruption if power is lost to \na server. Which of the following would be the BEST strategy to follow?", "options": ["Encryption", "Off-site backups", "Journaling", "Replication"], "answer": "Journaling", "explanation": "C. Journaling\nJournaling writes data to a temporary journal before writing the \ninformation to the database. If power is lost, the system can recover the \nlast transaction from the journal when power is restored.\nThe incorrect answers:\nA. Encryption\nEncryption would provide confidentiality of the data, but it would not \nprovide any additional integrity features if power was lost.\nB. Off-site backups\nOff-site backups can be used to recover a corrupted database, but this does \nnot minimize or prevent database corruption from occurring.\nD. Replication\nReplication is used to create a duplicate copy of data. Although this \nprocess does provide a backup, it doesn't add any additional integrity and \ncould still potentially corrupt data if power is lost.\nMore information:\nSY0-701, Objective 3.4 - Backups https://professormesser.link/*********"}, {"question": "A30. A company is creating a security policy for corporate mobile devices:\n• All mobile devices must be automatically locked after a predefined  \ntime period. \n• The location of each device needs to be traceable.\n• All of the user’s information should be completely separate from \ncompany data.\nWhich of the following would be the BEST way to establish these \nsecurity policy rules?", "options": ["Segmentation", "Biometrics", "COPE", "MDM"], "answer": "MDM", "explanation": "D. MDM\nAn MDM (Mobile Device Manager) provides a centralized management \nsystem for all mobile devices. From this central console, security \nadministrators can set policies for many different types of mobile devices.\nThe incorrect answers:\nA. Segmentation\nSegmentation describes the separation of user data from company data, \nbut the implementation all policies is managed by the MDM.\nB. Biometrics\nBiometrics can be used as another layer of device security, but you need \nmore than biometrics to implement the required security policies in this \nquestion.\nC. COPE\nA device that is COPE (Corporately Owned and Personally Enabled) is \ncommonly purchased by the corporation and allows the use of the mobile \ndevice for both business and personal use. The use of a COPE device does \nnot provide any policy management of the device.\nMore information:\nSY0-701, Objective 4.1 - Securing Wireless and Mobile https://professormesser.link/*********"}, {"question": "A31. A security engineer runs a monthly vulnerability scan. The scan doesn’t \nlist any vulnerabilities for Windows servers, but a significant vulnerability \nwas announced last week and none of the servers are patched yet. Which \nof the following best describes this result?", "options": ["Exploit", "Compensating controls", "Zero-day attack", "False negative"], "answer": "False negative", "explanation": "D. False negative\nA false negative is a result that fails to detect an issue when one  \nactually exists.\nThe incorrect answers:\nA. Exploit\nAn exploit is an attack against a vulnerability. Vulnerability scans do not \ncommonly attempt to exploit the vulnerabilities that they identify.\nB. Compensating controls\nCompensating controls are used to mitigate a vulnerability when an \noptimal security response may not be available. For example, if a company \ncan't deploy a patch for a vulnerability, they can revoke or limit application \naccess until a patch is provided.\nC. Zero-day attack\nA zero-day attack focuses on previously unknown vulnerabilities. In this \nexample, the vulnerability scan isn’t an attack, and the vulnerabilities are \nalready known and patches are available.\nMore information:\nSY0-701, Objective 4.3 - Analyzing Vulnerabilities https://professormesser.link/*********"}, {"question": "A32.  An IT help desk is using automation to improve the response time for \nsecurity events. Which of the following use cases would apply to this \nprocess?", "options": ["Escalation", "Guard rails", "Continuous integration", "Resource provisioning"], "answer": "Escalation", "explanation": "A. Escalation\nAutomation can recognize security events and escalate a security-related \nticket to the incident response team without any additional human \ninteraction.\nThe incorrect answers:\nB. Guard rails\nGuard rails are used by application developers to provide a set of \nautomated validations to user input and behavior. Guard rails are not used \nby the help desk team.\nC. Continuous integration\nContinuous integration and testing provides an automated method \nof constantly developing, testing, and deploying code. The continuous \nintegration process is not used by the help desk.\nD. Resource provisioning\nResource provisioning can be automated during the on-boarding and \noff-boarding process to quickly create or remove rights and permissions. \nResource provisioning is not commonly part of the automation associated \nwith security event notification.\nMore information:\nSY0-701, Objective 4.7 - Scripting and Automation https://professormesser.link/*********"}, {"question": "A33. A network administrator would like each user to authenticate with \ntheir corporate username and password when connecting to the \ncompany's wireless network. Which of the following should the network \nadministrator configure on the wireless access points?", "options": ["WPA3", "802.1X", "PSK", "MFA"], "answer": "802.1X", "explanation": "B. 802.1X\n802.1X uses a centralized authentication server, and this allows all users to \nuse their corporate credentials during the login process.\nThe incorrect answers:\nA. WPA3\nWPA3 (Wi-Fi Protected Access 3) is an encryption protocol for 802.11 \nwireless networking. The WPA3 encryption itself does not include the \ncentralized authentication process described in this question.\nC. PSK\nPSK (Pre-Shared Key) is a wireless configuration option that allows \neveryone on the network to use the same access key or password when \nconnecting to the wireless network. This question requires each person to \nuse unique authentication credentials.\nD. MFA\nMFA (Multifactor Authentication) describes the use of multiple types \nof authentication checks. A username and password is a single factor \n(something you know), and the use of MFA does not itself require unique \nusername and password credentials for each user.\nMore information:\nSY0-701, Objective 3.2 - Port Security https://professormesser.link/*********"}, {"question": "A34. A company's VPN service performs a posture assessment during the \nlogin process. Which of the following mitigation techniques would this \ndescribe?", "options": ["Encryption", "Decommissioning", "Least privilege", "Configuration enforcement"], "answer": "Configuration enforcement", "explanation": "D. Configuration enforcement\nA posture assessment evaluates the configuration of a system to ensure \nall configurations and applications are up to date and secure as possible. \nIf a configuration does not meet these standards, the user is commonly \nprovided with options for resolving the issue before proceeding.\nThe incorrect answers:\nA. Encryption\nEncryption is an important part of a VPN (Virtual Private Network), but \nthe encryption of network data is not related to the posture assessment \nprocess.\nB. Decommissioning\nIt's important to properly manage data during any decommissioning \nprocess, but the decommissioning isn't part of the VPN login process.\nC. Least privilege\nLeast privilege describes the minimum rights and permissions that would \nallow an individual to perform their job function. Least privilege is not \npart of a posture assessment.\nMore information:\nSY0-701, Objective 2.5 - Mitigation Techniques https://professormesser.link/*********"}, {"question": "A35.  A user has assigned individual rights and permissions to a file on their \nnetwork drive. The user adds three additional individuals to have read-\nonly access to the file. Which of the following would describe this access \ncontrol model?", "options": ["Discretionary", "Mandatory", "Attribute-based", "Role-based"], "answer": "Discretionary", "explanation": "A. Discretionary\nDiscretionary access control is used in many operating systems, and this \nmodel allows the owner of the resource to control who has access.\nThe incorrect answers:\nB. Mandatory\nMandatory access control allows access based on the security level assigned \nto an object. Only users with the object’s assigned security level or higher \nmay access the resource.\nC. Attribute-based\nAttribute-based access control combines many different parameters to \ndetermine if a user has access to a resource.\nD. Role-based\nRole-based access control assigns rights and permissions based on the role \nof a user. These roles are usually assigned by group.\nMore information:\nSY0-701, Objective 4.6 - Access Controls https://professormesser.link/*********"}, {"question": "A36. A remote user has received a text message with a link to login and \nconfirm their upcoming work schedule. Which of the following would \nBEST describe this attack?", "options": ["Brute force", "Watering hole", "Typosquatting", "Smishing"], "answer": "Smishing", "explanation": "D. Smishing\nSmishing, or SMS (Short Message Service) phishing, is a social \nengineering attack that asks for sensitive information using SMS or  \ntext messages.\nThe incorrect answers:\nA. Brute force\nA brute force attack tries multiple password combinations in an effort to \nidentify the correct authentication details.\nB. Watering hole\nA watering hole attack will infect a third-party site visited by the victim. \nWatering hole attacks are not commonly associated with received text \nmessages.\nC. Typosquatting\nTyposquatting uses a misspelling of a domain name to convince victims \nthey are visiting a legitimate website. The information provided in this \nquestion does not provide any specific domain names or links.\nMore information:\nSY0-701, Objective 2.2 - Phishing https://professormesser.link/*********"}, {"question": "A37. A company is formalizing the design and deployment process used by \ntheir application programmers. Which of the following policies would \napply?", "options": ["Business continuity", "Acceptable use policy", "Incident response", "Development lifecycle"], "answer": "Development lifecycle", "explanation": "D. Development lifecycle\nA formal software development lifecycle defines the specific policies \nassociated with the design, development, testing, deployment, and \nmaintenance of the application development process.\nThe incorrect answers:\nA. Business continuity\nBusiness continuity plans define the procedures used when the primary \nbusiness systems are unavailable. The business continuity process is not \ncommonly associated with the application development process.\nB. Acceptable use policy\nAn acceptable use policy formally defines the proper use of company assets \nand technology devices.\nC. Incident response\nIncident response policies define the procedures to follow when a security \nincident is identified. Incident response is not part of the application \ndevelopment process\nMore information:\nSY0-701, Objective 5.1 - Security Policies https://professormesser.link/*********"}, {"question": "A38.  A security administrator has copied a suspected malware executable from \na user's computer and is running the program in a sandbox. Which of the \nfollowing would describe this part of the incident response process?", "options": ["Eradication", "Preparation", "Recovery", "Containment"], "answer": "Containment", "explanation": "D. Containment\nThe isolation and containment process prevents malware from spreading \nand allows the administrator to analyze the operation of the malware \nwithout putting any other devices at risk.\nThe incorrect answers:\nA. Eradication\nThe eradication phase is associated with completely removing malware \nfrom a system. This process usually involves removing all data from a \nsystem and installing or re-imaging with a known-good operating system.\nB. Preparation\nThe preparation process occurs before a security incident is discovered, \nand it can include the documentation of communication methods, the \ncompiling of mitigation software, or gathering network and application \ndocumentation.\nC. Recovery\nThe recovery phase is associated with the recovery of a system after \na security incident. Running malware in a sandbox is not part of the \nrecovery process.\nMore information:\nSY0-701, Objective 4.8 - Incident Response https://professormesser.link/*********"}, {"question": "A39. A server administrator at a bank has noticed a decrease in the number \nof visitors to the bank's website. Additional research shows that users are \nbeing directed to a different IP address than the bank's web server. Which \nof the following would MOST likely describe this attack?", "options": ["Deauthentication", "DDoS", "Buffer overflow", "DNS poisoning"], "answer": "DNS poisoning", "explanation": "D. DNS poisoning\nA DNS poisoning can modify a DNS server to modify the IP address \nprovided during the name resolution process. If an attacker modifies the \nDNS information, they can direct client computers to any destination IP \naddress.\nThe incorrect answers:\nA. Deauthentication\nDeauthentication attacks are commonly associated with wireless networks. \nThe deauthentication attack is used to remove devices from the wireless \nnetwork, and it does not commonly redirect clients to a different website.\nB. DDoS\nA DDoS (Distributed Denial of Service) is used by attackers to \ncause services to be unavailable. In this example, the bank's website is \noperational but clients are not resolving the correct IP address.\nC. Buffer overflow\nBuffer overflows are associated with application attacks and can cause \napplications to crash or act in unexpected ways. A buffer overflow would \nnot commonly redirect clients to a different website IP address.\nMore information:\nSY0-701, Objective 2.4 - DNS Attacks https://professormesser.link/*********"}, {"question": "A40. Which of the following considerations are MOST commonly associated \nwith a hybrid cloud model?", "options": ["Microservice outages", "IoT support", "Network protection mismatches", "Containerization backups"], "answer": "Network protection mismatches", "explanation": "C. Network protection mismatches\nA hybrid cloud includes more than one private or public cloud. This adds \nadditional complexity to the overall infrastructure, and it's common to \ninadvertently apply different authentication options and user permissions \nacross multiple cloud providers.\nThe incorrect answers:\nA. Microservice outages\nMicroservices are used to create a scalable and resilient application \ninstance. However, the availability of a microservice is not specific to a \nhybrid cloud model.\nB. IoT support\nIoT (Internet of Things) support is available in many cloud infrastructure \nmodels, and this would not be specific to a hybrid cloud.\nD. Containerization backups\nContainerization provides an efficient method of deploying application \ninstances, but the use and backup of these containers is not specific to a \nhybrid cloud infrastructure.\nMore information:\nSY0-701, Objective 3.1 - Cloud Infrastructures https://professormesser.link/*********"}, {"question": "A41.  A company hires a large number of seasonal employees, and their \nsystem access should normally be disabled when the employee leaves \nthe company. The security administrator would like to verify that their \nsystems cannot be accessed by any of the former employees. Which of the \nfollowing would be the BEST way to provide this verification?", "options": ["Confirm that no unauthorized accounts have administrator access", "Validate the account lockout policy", "Validate the offboarding processes and procedures", "Create a report that shows all authentications for a 24-hour period"], "answer": "Validate the offboarding processes and procedures", "explanation": "<PERSON>. Validate the offboarding processes and procedures\nThe disabling of an employee account is commonly part of the offboarding \nprocess. One way to validate an offboarding policy is to perform an audit \nof all accounts and compare active accounts with active employees.\nThe incorrect answers:\n<PERSON><PERSON> Confirm that no unauthorized accounts have administrator access\nIt’s always a good idea to periodically audit administrator accounts, but \nthis audit won’t provide any validation that all former employee accounts \nhave been disabled.\nB. Validate the account lockout policy\nAccount lockouts occur when a number of invalid authentication attempts \nhave been made to a valid account. Disabled accounts would not be locked \nout because they are not currently valid accounts.\nD. Create a report that shows all authentications for a 24-hour period\nA list of all authentications would be quite large, and it would not be \nobvious to see which authentications were made with valid accounts and \nwhich authentications were made with former employee accounts.\nMore information:\nSY0-701, Objective 5.1 - Security Procedures https://professormesser.link/*********"}, {"question": "A42. Which of the following is used to describe how cautious an organization \nmight be to taking a specific risk?", "options": ["Risk appetite", "Risk register", "Risk transfer", "Risk reporting"], "answer": "Risk appetite", "explanation": "A. Risk appetite\nA risk appetite is a broad description of how much risk-taking is deemed \nacceptable. An organization's risk appetite posture might be conservative, \nor they might be more expansionary and willing to take additional risks.\nThe incorrect answers:\nB. Risk register\nA risk register identifies and documents the risks associated with each \nstep of a project plan. A risk register is not designed to describe an \norganization's level of caution associated with each risk.\nC. Risk transfer\nSome organizations will transfer their risk to a third-party. For example, \nmany organizations will purchase cybersecurity insurance to minimize the \nfinancial impact of a cybersecurity event.\nD. Risk reporting\nRisk reporting is the formal process of identifying risk and documenting \nall details associated with the risk. These reports are commonly designed \nfor the decision making process by the senior management of an \norganization.\nMore information:\nSY0-701, Objective 5.2 - Risk Analysis https://professormesser.link/*********"}, {"question": "A43. A technician is applying a series of patches to fifty web servers during a \nscheduled maintenance window. After patching and rebooting the first \nserver, the web service fails with a critical error. Which of the following \nshould the technician do NEXT?", "options": ["Contact the stakeholders regarding the outage", "Follow the steps listed in the backout plan", "Test the upgrade process in the lab", "Evaluate the impact analysis associated with the change"], "answer": "Follow the steps listed in the backout plan", "explanation": "B. Follow the steps listed in the backout plan\nThe backout plan associated with the change control process provides \ninformation on reverting to the previous configuration if an unrecoverable \nerror is found during the change.\nThe incorrect answers:\nA. Contact the stakeholders regarding the outage\nThe stakeholders don't commonly require a detailed notification of every \nstep during the maintenance window. The final disposition of the change \ncan be communicated to the stakeholders after the maintenance window \nhas concluded.\nC. Test the upgrade process in the lab\nThe testing phase of the change control process takes place prior to the \nmaintenance window. Once the maintenance window has started, it's too \nlate to perform any additional tests in the lab.\nD. Evaluate the impact analysis associated with the change\nAn impact analysis determines the risk for making the proposed change. \nThis analysis is created prior to the change control approval, and it would \nnot be very useful when troubleshooting during the maintenance window.\nMore information:\nSY0-701, Objective 1.3 - Change Management Process https://professormesser.link/*********"}, {"question": "A44.  An attacker has discovered a way to disable a server by sending specially \ncrafted packets from many remote devices to the operating system. When \nthe packet is received, the system crashes and must be rebooted to restore \nnormal operations. Which of the following would BEST describe this \nattack?", "options": ["Privilege escalation", "SQL injection", "Replay attack", "DDoS"], "answer": "DDoS", "explanation": "D. DDoS\nA DDoS (Distributed Denial of Service) is an attack that overwhelms or \ndisables a service to prevent the service from operating normally. Packets \nfrom multiple devices that disable a server would be an example of a \nDDoS attack.\nThe incorrect answers:\nA. Privilege escalation\nA privilege escalation attack allows a user to exceed their normal rights \nand permissions. In this example, user permission escalations were not \nrequired to perform this attack.\nB. SQL injection\nA SQL (Structured Query Language) injection is used to circumvent an \napplication and communicate directly to the application's database. In this \nquestion, there was no mention of application vulnerabilities or specific \nSQL statements.\nC. Replay attack\nA replay attack captures information and then replays that information \nas the method of attack. In this question, no mention was made of a prior \ndata capture.\nMore information:\nSY0-701, Objective 2.4 - Denial of Service https://professormesser.link/*********"}, {"question": "A45.  A data breach has occurred in a large insurance company. A security \nadministrator is building new servers and security systems to get all of \nthe financial systems back online. Which part of the incident response \nprocess would BEST describe these actions?", "options": ["Lessons learned", "Containment", "Recovery", "Analysis"], "answer": "Recovery", "explanation": "C. Recovery\nThe recovery after a breach can be a phased approach that may take \nmonths to complete.\nThe incorrect answers:\n<PERSON><PERSON> learned\nOnce the event is over, it’s useful to revisit the process to learn and \nimprove for next time.\nB. Containment\nDuring an incident, it’s useful to separate infected systems from the rest of \nthe network.\nD. Analysis\nThe analysis phase can include the analysis of log files and alerts. These \ndata source can help warn of a potential attack or evidence an attack is \nunderway.\nMore information:\nSY0-701, Objective 4.8 - Incident Response https://professormesser.link/*********"}, {"question": "A46. A network team has installed new access points to support an application \nlaunch. In less than 24 hours, the wireless network was attacked and \nprivate company information was accessed. Which of the following would \nbe the MOST likely reason for this breach?", "options": ["Race condition", "Jailbreaking", "Impersonation", "Misconfiguration"], "answer": "Misconfiguration", "explanation": "D. Misconfiguration\nThere are many different configuration options when installing an access \npoint, and it's likely one of those options allowed an attacker to gain access \nto the internal network.\nThe incorrect answers:\nA. Race condition\nA race condition occurs when two different application processes are \nexecuting simultaneously. If the two processes are not aware of each other, \nthe application may have unexpected results. In this example, there's no \nevidence the access points were experiencing a race condition.\nB. Jailbreaking\nJailbreaking replaces the firmware on a mobile device to gain access to \nfeatures not normally available in the operating system. Jailbreaking is not \ncommonly associated with wireless access points.\nC. Impersonation\nImpersonation is an attacker pretending to be someone or something they \nare not. In this example, there's no evidence that impersonation was used \nto breach the wireless network.\nMore information:\nSY0-701, Objective 2.3 - Misconfiguration Vulnerabilities https://professormesser.link/*********"}, {"question": "A47.  An organization has identified a significant vulnerability in an Internet-\nfacing firewall. The firewall company has stated the firewall is no \nlonger available for sale and there are no plans to create a patch for this \nvulnerability. Which of the following would BEST describe this issue?", "options": ["End-of-life", "Improper input handling", "Improper key management", "Incompatible OS"], "answer": "End-of-life", "explanation": "A. End-of-life\nBecause the firewall is no longer available for sale, the firewall company \nhas decided to stop supporting and updating the device. A product no \nlonger supported by the manufacturer is consider to be end-of-life.\nThe incorrect answers:\nB. Improper input handling\nA best practice for application security is to provide the proper handling \nof invalid or unnecessary input. A missing patch for the firewall firmware \nwould not be related to input handling.\nC. Improper key management\nCryptographic keys can be used for many security purposes, but managing \nthose keys isn’t part of the patching process from a vendor.\nD. Incompatible OS\nThe operating system in the firewall would normally be supported by the \nmanufacturer, and the operating systems are not commonly modified on a \npurpose-built device such as a firewall.\nMore information:\nSY0-701, Objective 2.3 - Hardware vulnerabilities https://professormesser.link/*********"}, {"question": "A48. A company has decided to perform a disaster recovery exercise during an \nannual meeting with the IT directors and senior directors. A simulated \ndisaster will be presented, and the participants will discuss the logistics \nand processes required to resolve the disaster. Which of the following \nwould BEST describe this exercise?", "options": ["Capacity planning", "Business impact analysis", "Continuity of operations", "Tabletop exercise"], "answer": "Tabletop exercise", "explanation": "D. Tabletop exercise\nA tabletop exercise allows a disaster recovery team to evaluate and plan \ndisaster recovery processes without performing a full-scale drill.\nThe incorrect answers:\nA. Capacity planning\nCapacity planning is used to determine how many resources would \nbe required for a particular task. A formal tabletop exercise would not \ncommonly include a capacity planning analysis.\nB. Business impact analysis\nA business impact analysis is usually created during the disaster recovery \nplanning process. Once the disaster has occurred, it becomes much more \ndifficult to complete an accurate impact analysis.\nC. Continuity of operations\nIf an outage occurs, it's common to have a backup plan to provide \ncontinuity of operations. This plan can be used for any significant outage \nand is not specific to disaster recovery testing.\nMore information:\nSY0-701, Objective 3.4 - Recovery Testing https://professormesser.link/701030403"}, {"question": "A49.  A security administrator needs to block users from visiting websites \nhosting malicious software. Which of the following would be the BEST \nway to control this access?", "options": ["Honeynet", "Data masking", "DNS filtering", "Data loss prevention"], "answer": "DNS filtering", "explanation": "C. DNS filtering\nDNS filtering uses a database of known malicious websites to resolve an \nincorrect or null IP address. If a user attempts to visit a known malicious \nsite, the DNS resolution will fail and the user will not be able to visit the \nwebsite.\nThe incorrect answers:\nA. Honeynet\nA honeynet is a non-production network created to attract attackers. A \nhoneynet is not used to block traffic to known malicious Internet sites.\nB. Data masking\nData masking provides a way to hide data by substitution, shuffling, \nencryption, and other methods. Data masking does not provide a method \nof blocking communication to malicious websites.\nD. Data loss prevention\nData Loss Prevention (DLP) systems can identify and block private \ninformation from being transferred between systems. DLP does not \nprovide any direct method of blocking network traffic to known malware \nrepositories.\nMore information:\nSY0-701, Objective 4.5 - Web Filtering https://professormesser.link/*********"}, {"question": "A50.  A system administrator has been called to a system with a malware \ninfection. As part of the incident response process, the administrator has \nimaged the operating system to a known-good version. Which of these \nincident response steps is the administrator following?", "options": ["Lessons learned", "Recovery", "Detection", "Containment"], "answer": "Recovery", "explanation": "B. Recovery\nThe recovery phase describes the process of returning the system and data \nto the state prior to the malware infection. With a malware infection, this \noften requires deleting all data and reinstalling a known-good operating \nsystem.\nThe incorrect answers:\n<PERSON><PERSON> learned\nA post-incident meeting can help the incident response participants \ndiscuss the phases of the incident that went well and which processes can \nbe improved for future events.\nC. Detection\nThe detection of the malware is an early phase in the incident response \nprocess. If the administrator is imaging a system, the malware was \npreviously detected and any critical documents were already recovered.\nD. Containment\nThe containment phase isolates the system from any other devices to \nprevent the spread of any malicious software. The containment phase \ngenerally occurs immediately after\nMore information:\nSY0-701, Objective 4.8 - Incident Response https://professormesser.link/*********"}, {"question": "A51.  A company has placed a SCADA system on a segmented network with \nlimited access from the rest of the corporate network. Which of the \nfollowing would describe this process?", "options": ["Load balancing", "Least privilege", "Data retention", "Hardening"], "answer": "Hardening", "explanation": "<PERSON>. Hardening\nThe hardening process for an industrial SCADA (Supervisory Control and \nData Acquisition) system might include network segmentation, additional \nfirewall controls, and the implementation of access control lists.\nThe incorrect answers:\nA. Load balancing\nA load balancer is used to distribute transactions across multiple systems. \nA single system was the only device referenced in this question, so a load \nbalancing option would not be available.\nB. Least privilege\nLeast privilege defines the minimum rights and permissions for \ncompleting a specific task. In this example, there was no mention of \nspecific tasks or their necessary permissions.\nC. Data retention\nData retention is important for long-term storage of important \ninformation. In this example, the mandated storage of data was not a \nconsideration.\nMore information:\nSY0-701, Objective 4.1 - Hardening Targets https://professormesser.link/701040102"}, {"question": "A52.  An administrator is viewing the following security log:  \n \nDec 30 08:40:03 web01 Failed password for root\n  from ************* port 26244 ssh2\n  Dec 30 08:40:05 web01 Failed password for root\n  from ************* port 26244 ssh2\n  Dec 30 08:40:09 web01 445 more authentication\n  failures; rhost=************* user=root\n\nWhich of the following would describe this attack?", "options": ["Spraying", "Downgrade", "Brute force", "DDoS"], "answer": "Brute force", "explanation": "C. Brute force\nA brute force attack discovers password by attempting a large combination \nof letters, numbers, and special characters until a match is found. In this \nexample, the notification of over four hundred attempts would qualify as a \nbrute force attack.\nThe incorrect answers:\nA. Spraying\nA spraying attack is similar to a brute force attack, but spraying limits the \nnumber of attempts to prevent alerts or an account lockout. A spraying \nattack often uses accounts passwords stolen from other sites or a short list \nof the most common passwords.\nB. Downgrade\nA downgrade attack is often used to force an insecure encryption \nalgorithm or the disabling of encryption entirely. In this example, no \nevidence of a downgrade attack is contained in the security log.\nD. DDoS\nA DDoS (Distributed Denial of Service) would involve many different \ndevices to cause a system outage. In this example, a single IP address was \nlogged and there was no evidence of a service outage.\nMore information:\nSY0-701, Objective 4.9 - Log data https://professormesser.link/*********"}, {"question": "A53. During a morning login process, a user's laptop was moved to a private \nVLAN and a series of updates were automatically installed. Which of the \nfollowing would describe this process?", "options": ["Account lockout", "Configuration enforcement", "Decommissioning", "Sideloading"], "answer": "Configuration enforcement", "explanation": "B. Configuration enforcement\nMany organizations will perform a posture assessment during the login \nprocess to verify the proper security controls are in place. If the device does \nnot pass the assessment, the system can be quarantined and any missing \nsecurity updates can then be installed.\nThe incorrect answers:\nA. Account lockout\nIn this example, there were no errors or notifications regarding the account \nor authentication status.\nC. Decommissioning\nThe decommissioning process is often used to permanently remove devices \nfrom the network. In this example, the laptop mitigation would allow the \ndevice to return to the network once the updates were complete.\nD. Sideloading\nSideloading describes the installation of software on a mobile device \nthrough the use of third-party operating systems or websites.\nMore information:\nSY0-701, Objective 2.5 - Mitigation Techniques https://professormesser.link/*********"}, {"question": "A54. Which of the following describes two-factor authentication?", "options": ["A printer uses a password and a PIN", "The door to a building requires a fingerprint scan", "An application requires a pseudo-random code", "A Windows Domain requires a password and smart card"], "answer": "A Windows Domain requires a password and smart card", "explanation": "D. A Windows Domain requires a password and smart card\nThe multiple factors of authentication for this Windows Domain are a \npassword (something you know), and a smart card (something you have).\nThe incorrect answers:\nA. A printer uses a password and a PIN\nA password and a PIN (Personal Identification Number) are both \nsomething you know, so only one authentication factor is used.\nB. The door to a building requires a fingerprint scan\nA biometric scan (something you are) is a single factor of authentication.\nC. An application requires a pseudo-random code\nPseudo-random authentication codes are often provided using a hardware \ndongle or mobile app. This single factor of authentication is something you \nhave.\nMore information:\nSY0-701, Objective 4.6 - Multi-factor Authentication https://professormesser.link/*********"}, {"question": "A55.  A company is deploying a new application to all employees in the field. \nSome of the problems associated with this roll out include:\n• The company does not have a way to manage the devices in the field\n• Team members have many different kinds of mobile devices\n• The same device needs to be used for both corporate and private use\nWhich of the following deployment models would address these \nconcerns?", "options": ["CYOD", "SSO", "COPE", "BYOD"], "answer": "COPE", "explanation": "C. COPE\nA COPE (Corporate-owned, Personally Enabled) device would solve the \nissue of device standardization and would allow the device to be used for \nboth corporate access and personal use.\nThe incorrect answers:\nA. CYOD\nCYOD (Choose Your Own Device) allows the user to pick the make and \nmodel of their device. This would not solve the issue of different kinds of \nmobile devices used in the field.\nB. SSO\nSSO (Single Sign-On) is used to authenticate once when accessing \nmultiple resources. SSO would not resolve any of the listed issues.\nD. BYOD\nWith BYOD (Bring Your Own Device), the employee uses their personal \ndevice at work. This would not address the issue of mobile device \nmanagement or standardization of mobile devices.\nMore information:\nSY0-701, Objective 4.1 - Securing Wireless and Mobile https://professormesser.link/*********"}, {"question": "A56.  An organization is installing a UPS for their new data center. Which of \nthe following would BEST describe this control type?", "options": ["Compensating", "Directive", "Deterrent", "Detective"], "answer": "Compensating", "explanation": "A. Compensating\nA compensating security control doesn’t prevent an attack, but it does \nrestore from an attack using other means. In this example, the UPS \n(Uninterruptible Power Supply) does not stop a power outage, but it does \nprovide alternative power if an outage occurs.\nThe incorrect answers:\nB. Directive\nA directive control provides security controls using instructions and \nguidance. A UPS is not categorized as a directive control.\nC. Deterrent\nA deterrent control discourages an intrusion attempt. A UPS is used after \npower has been lost, so it would not be categorized as a deterrent.\nD. Detective\nA detective control may not prevent access, but it can identify and record \nintrusion attempts.\nMore information:\nSY0-701, Objective 1.1 - Security Controls https://professormesser.link/*********"}, {"question": "A57. A manufacturing company would like to track the progress of parts used \non an assembly line. Which of the following technologies would be the \nBEST choice for this task?", "options": ["Secure enclave", "Blockchain", "Hashing", "Asymmetric encryption"], "answer": "Blockchain", "explanation": "B. Blockchain\nThe ledger functionality of a blockchain can be used to track or verify \ncomponents, digital media, votes, and other physical or digital objects.\nThe incorrect answers:\nA. Secure enclave\nA secure enclave is a protected area for secret information, and the secure \nenclave is often implemented as a hardware processor in a device.\nC. Hashing\nCryptographic hashes are commonly used to provide integrity \nverifications, but they don't necessarily include any method of tracking \ncomponents on an assembly line.\nD. Asymmetric encryption\nAsymmetric encryption uses different keys for encryption and decryption. \nAsymmetric encryption does not provide any method for tracking objects \non an assembly line.\nMore information:\nSY0-701, Objective 1.4 - Blockchain Technology https://professormesser.link/*********"}, {"question": "A58. A company's website has been compromised and the website content has \nbeen replaced with a political message. Which of the following threat \nactors would be the MOST likely culprit?", "options": ["Insider", "Organized crime", "Shadow IT", "<PERSON><PERSON><PERSON><PERSON>"], "answer": "<PERSON><PERSON><PERSON><PERSON>", "explanation": "<PERSON><PERSON> Hacktivist\nA hacktivist is motivated by a particular philosophy, and their goal \nis to spread their message by defacing web sites and releasing private \ndocuments.\nThe incorrect answers:\nA. Insider\nAn insider has access to many company services, but the motivations of \nan insider threat would not commonly result in the posting of political \ninformation.\nB. Organized crime\nOrganized crime actors are motivated by money. It would be unusual for \nan organized crime hack to include the posting of political messages.\nC. Shadow IT\nA shadow IT group is mostly interested in building their own systems and \napplications, and they would not commonly deface a website in an attempt \nto spread a specific political message.\nMore information:\nSY0-701, Objective 2.1 - Threat Actors https://professormesser.link/*********"}, {"question": "A59. A Linux administrator is downloading an updated version of her Linux \ndistribution. The download site shows a link to the ISO and a SHA256 \nhash value. Which of these would describe the use of this hash value?", "options": ["Verifies that the file was not corrupted during the file transfer", "Provides a key for decrypting the ISO after download", "Authenticates the site as an official ISO distribution site", "Confirms that the file does not contain any malware"], "answer": "Verifies that the file was not corrupted during the file transfer", "explanation": "<PERSON><PERSON> Verifies that the file was not corrupted during\nthe file transfer\nOnce the file is downloaded, the administrator can calculate the file’s \nSHA256 hash and confirm that it matches the value on the website.\nThe incorrect answers:\n<PERSON><PERSON> Provides a key for decrypting the ISO after download\nISO files containing public information are usually distributed without  \nany encryption, and a hash value would not commonly be used as a \ndecryption key.\nC. Authenticates the site as an official ISO distribution site\nAlthough it’s important to download files from known good sites, \nproviding a hash value on a site would not provide any information about \nthe site’s authentication.\nD. Confirms that the file does not contain any malware\nA hash value doesn’t inherently provide any protection against malware.\nMore information:\nSY0-701, Objective 3.3 - Protecting Data https://professormesser.link/*********"}, {"question": "A60.  A company's security policy requires that login access should only \nbe available if a person is physically within the same building as the \nserver. Which of the following would be the BEST way to provide this \nrequirement?", "options": ["USB security key", "Biometric scanner", "PIN", "SMS"], "answer": "Biometric scanner", "explanation": "B. Biometric scanner\nA biometric scanner would require a person to be physically present to \nverify the authentication.\nThe incorrect answers:\nA. USB security key\nA security key can be used to store a certificate on a USB (Universal \nSerial Bus) drive. The security key is commonly used as an authentication \nmethod for a user or application, and it doesn't provide any information \nabout the location of the security key.\nC. PIN\nAlthough a PIN (Personal Identification Number) can be used as an \nauthentication factor, the use of the PIN does not guarantee that a person \nis physically present.\nD. SMS\nSMS (Short Message Service), or text messages, are commonly used as \nauthentication factors. However, the use of a mobile device to receive the \nSMS message does not guarantee that the owner of the mobile device is \nphysically present.\nMore information:\nSY0-701, Objective 3.3 - Protecting Data https://professormesser.link/*********"}, {"question": "A61.  A development team has installed a new application and database to a \ncloud service. After running a vulnerability scanner on the application \ninstance, a security administrator finds the database is available for \nanyone to query without providing any authentication. Which of these \nvulnerabilities is MOST associated with this issue?", "options": ["Legacy software", "Open permissions", "Race condition", "Malicious update"], "answer": "Open permissions", "explanation": "B. Open permissions\nJust like local systems, proper permissions and security controls are \nrequired when applications are installed to a cloud-based system. If \npermissions are not properly configured, the application data may be \naccessible by anyone on the Internet.\nThe incorrect answers:\nA. Legacy software\nLegacy software often describes an older application with limited support \noptions. The application and database in this example is a new installation \nand would not normally be categorized as legacy.\nC. Race condition\nIf two processes occur simultaneously without coordination between \nthe processes, unexpected results could occur. In this example, a single \nvulnerability scan has identified the issue and other processes do not \nappear to be involved.\nD. Malicious update\nA malicious update involves the installation of unwanted software during \na normal update process. In this example, an update was not performed \nand the resulting public access would not generally be part of a malicious \nupdate.\nMore information:\nSY0-701, Objective 3.3 - Protecting Data https://professormesser.link/*********"}, {"question": "A62. Employees of an organization have received an email with a link offering \na cash bonus for completing an internal training course. Which of the \nfollowing would BEST describe this email?", "options": ["Watering hole attack", "Cross-site scripting", "Zero-day", "Phishing campaign"], "answer": "Phishing campaign", "explanation": "<PERSON><PERSON>shing campaign\nA phishing campaign is an internal process used to test the security habits \nof the user community. An email with a link from a server not under the \ncontrol of the company could be an email sent by the IT department as \npart of a phishing campaign.\nThe incorrect answers:\nA. Watering hole attack\nA watering hole attack is used as an alternative to attacking a victim's \ndevice directly. With a watering hole attack, an attacker will compromise a \nsite used by the victim and will simply wait for the victim to visit.\nB. Cross-site scripting\nCross-site scripting takes advantage of the trust already existing in a \nweb browser. In this example, there's no evidence of a vulnerable web \napplication or a specific browser-based vulnerability.\nC. Zero-day\nA zero-day attack describes a vulnerability where a software patch or \nsimilar mitigation is not immediately available. A link in an email by itself \ndoes not describe a zero-day attack.\nMore information:\nSY0-701, Objective 5.6 - Security Awareness https://professormesser.link/*********"}, {"question": "A63.  Which of the following risk management strategies would include the \npurchase and installation of an NGFW?", "options": ["Transfer", "Mitigate", "Accept", "Avoid"], "answer": "Mitigate", "explanation": "B. Mitigate\nMitigation is a strategy that decreases the threat level. This is commonly \ndone through the use of additional security systems and monitoring, such \nas an NGFW (Next-Generation Firewall).\nThe incorrect answers:\nA. Transfer\nTransferring risk would move the risk from one entity to another. Adding \nan NGFW would not transfer any risk to another party.\nC. Accept\nThe acceptance of risk is a position where the owner understands the risk \nand has decided to accept the potential results.\nD. Avoidance\nWith risk avoidance, the owner of the risk decides to stop participating in \na high-risk activity. This effectively avoids the risky activity and prevents \nany future issues.\nMore information:\nSY0-701, Objective 3.2 - Firewall Types https://professormesser.link/701030206"}, {"question": "A64.  An organization is implementing a security model where all application \nrequests must be validated at a policy enforcement point. Which of the \nfollowing would BEST describe this model?", "options": ["Public key infrastructure", "Zero trust", "Discretionary access control", "Federation"], "answer": "Zero trust", "explanation": "B. Zero trust\nZero trust describes a model where nothing is inherently trusted and \neverything must be verified to gain access. A central policy enforcement \npoint is commonly used to implement a zero trust architecture.\nThe incorrect answers:\nA. Public key infrastructure\nA public key infrastructure (PKI) uses public and private keys to provide \nconfidentiality and integrity. Asymmetric encryption and digital signatures \nare used as foundational technologies in PKI.\nC. Discretionary access control.\nDiscretionary access control is an authorization method where the owner \nof the data determines the scope and type of access. A discretionary \naccess control model does not specifically define how the authorization is \nimplemented.\nD. Federation\nFederation provides a way to manage authentication to a third-party \ndatabase. Federation does not describe the use of a policy enforcement \npoint.\nMore information:\nSY0-701, Objective 1.2 - Zero Trust https://professormesser.link/*********"}, {"question": "A65.  A company is installing a new application in a public cloud. Which of \nthe following determines the assignment of data security in this cloud \ninfrastructure?", "options": ["Playbook", "Audit committee", "Responsibility matrix", "Right-to-audit clause"], "answer": "Responsibility matrix", "explanation": "C. Responsibility matrix\nA cloud responsibility matrix is usually published by the provider to \ndocument the responsibilities for all cloud-based services. For example, \nthe customer responsibilities for an IaaS (Infrastructure as a Service) \nimplementation will be different than SaaS (Software as a Service).\nThe incorrect answers:\nA. Playbook\nA playbook provides conditional steps to follow when managing an \norganization's processes and procedures. For example, the process of \nrecovering from a virus infection would be documented in a playbook.\nB. Audit committee\nAn audit committee oversees the risk management activities for an \norganization. For example, the committee would be responsible for \nverifying the security implementation documented in the responsibility \nmatrix.\nD. Right-to-audit clause\nA right-to-audit clause is often included in a third-party contract to define \nthe terms and conditions around periodic audits. This is often part of a \nlarger product or services contract.\nMore information:\nSY0-701, Objective 3.1 - Cloud Infrastructures https://professormesser.link/*********"}, {"question": "A66.  When decommissioning a device, a company documents the type and \nsize of storage drive, the amount of RAM, and any installed adapter cards. \nWhich of the following describes this process?", "options": ["Destruction", "Sanitization", "Certification", "Enumeration"], "answer": "Enumeration", "explanation": "D. Enumeration\nEnumeration describes the detailed listing of all parts in a particular \ndevice. For a computer, this could include the CPU type, memory, storage \ndrive details, keyboard model, and more.\nThe incorrect answers:\nA. Destruction\nDestruction involves physically damaging a device or component to \nprevent any future use or data access. Although the company may choose \nto destroy these computers at a later date, this question does not describe \nthe destruction process.\nB. Sanitization\nSanitization deletes data from storage media and allows the storage device \nto be used in the future. For example, a sector-by-sector format would \nsanitize a hard drive and allow the drive to be installed into another \ncomputer without the concern of a data breach.\nC. Certification\nIf a third-party is providing destruction services, they often will certify the \nwork and document which device serial numbers were destroyed as part of \ntheir service.\nMore information:\nSY0-701, Objective 4.2 - Asset Management https://professormesser.link/*********"}, {"question": "A67.  An attacker has sent more information than expected in a single API \ncall, and this has allowed the execution of arbitrary code. Which of the \nfollowing would BEST describe this attack?", "options": ["Buffer overflow", "Replay attack", "Cross-site scripting", "DDoS"], "answer": "Buffer overflow", "explanation": "<PERSON><PERSON> <PERSON><PERSON> overflow\nThe results of a buffer overflow can cause random results, but sometimes \nthe actions can be repeatable and controlled. In the best possible case for \nthe hacker, a buffer overflow can be manipulated to execute code on the \nremote device.\nThe incorrect answers:\nB. Replay attack\nA replay attack does not require the sending of more information than \nexpected, and often a replay attack consists of normal traffic and expected \napplication input.\nC. Cross-site scripting\nA cross-site scripting attack allows a third party to take advantage of the \ntrust a browser might have with another website. This question involves an \nAPI call and does not appear to reference a browser or third-party website.\nD. DDoS\nA DDoS (Distributed Denial of Service) renders a service unavailable, \nand it involves the input of many devices to operate. A DDoS would not \nrequire sending more information than expected, and it rarely results in \nthe execution of arbitrary code.\nMore information:\nSY0-701, Objective 2.3 - Buffer Overflows https://professormesser.link/*********"}, {"question": "A68.  A company encourages users to encrypt all of their confidential materials \non a central server. The organization would like to enable key escrow as \na backup option. Which of these keys should the organization place into \nescrow?", "options": ["Private", "CA", "Session", "Public"], "answer": "Private", "explanation": "A. Private\nWith asymmetric encryption, the private key is used to decrypt \ninformation that has been encrypted with the public key. To ensure \ncontinued access to the encrypted data, the company must have a copy of \neach private key. \nThe incorrect answers:\nB. CA\nA CA (Certificate Authority) key is commonly used to validate the digital \nsignature from a trusted CA. This is not commonly used for user data \nencryption.\nC. Session\nSession keys are commonly used temporarily to provide confidentiality \nduring a single session. Once the session is complete, the keys are \ndiscarded. Session keys are not used to provide long-term data encryption.\nD. Public\nIn asymmetric encryption, a public key is already available to everyone. It \nwould not be necessary to escrow a public key.\nMore information:\nSY0-701, Objective 1.4 - Public Key Infrastructure https://professormesser.link/*********"}, {"question": "A69.  A company is in the process of configuring and enabling host-based \nfirewalls on all user devices. Which of the following threats is the \ncompany addressing?", "options": ["Default credentials", "Vishing", "Instant messaging", "On-path"], "answer": "Instant messaging", "explanation": "C. Instant messaging\nInstant messaging is commonly used as an attack vector, and one way to \nhelp protect against malicious links delivered by instant messaging is a \nhost-based firewall.\nThe incorrect answers:\nA<PERSON> credentials\nUsers commonly login with unique credentials that are specific to the user. \nA host-based firewall would not identify the use of a default username and \npassword.\nB. Vishing\nVishing, or voice phishing, occurs over a phone or other voice \ncommunication method. A host-based firewall would not be able to \nprotect against a voice-related attack vector.\nD. On-path\nA on-path attack describes a third-party in the middle of a \ncommunications path. The victims of an on-path attack are usually not \naware an attack is taking place, so a host-based firewall would not be able \nto detect an on-path attack.\nMore information:\nSY0-701, Objective 2.2 - Common Threat Vectors https://professormesser.link/*********"}, {"question": "A70.  A manufacturing company would like to use an existing router to separate \na corporate network from a manufacturing floor. Both networks use \nthe same physical switch, and the company does not want to install any \nadditional hardware. Which of the following would be the BEST choice \nfor this segmentation?", "options": ["Connect the corporate network and the manufacturing floor with a VPN", "Build an air gapped manufacturing floor network", "Use host-based firewalls on each device", "Create separate VLANs for the corporate network and the manufacturing floor"], "answer": "Connect the corporate network and the manufacturing floor with a VPN", "explanation": "<PERSON><PERSON> <PERSON><PERSON> separate VLANs for the corporate network and\nthe manufacturing floor\nCreating VLANs (Virtual Local Area Networks) will segment a network \nwithout requiring additional switches. \nThe incorrect answers:\n<PERSON><PERSON> Connect the corporate network and the manufacturing floor  \nwith a VPN\nA VPN (Virtual Private Network) would encrypt all information between \nthe two networks, but it would not provide any segmentation. This process \nwould also commonly require additional hardware to provide VPN \nconnectivity.\nB. Build an air gapped manufacturing floor network\nAn air gapped network would require separate physical switches on each \nside of the gap, and this would require the purchase of an additional \nswitch.\nC. Use host-based firewalls on each device\nWhile personal firewalls provide protection for individual devices, they \ndo not segment networks. It’s also uncommon for personal firewalls to be \ninstalled on manufacturing equipment.\nMore information:\nSY0-701, Objective 2.5 - Segmentation and Access Control https://professormesser.link/*********"}, {"question": "A71.  An organization needs to provide a remote access solution for a newly \ndeployed cloud-based application. This application is designed to be used \nby mobile field service technicians. Which of the following would be the \nbest option for this requirement?", "options": ["RTOS", "CRL", "Zero-trust", "SASE"], "answer": "SASE", "explanation": "D. SASE\nA SASE (Secure Access Service Edge) solution is a next-generation VPN \ntechnology designed to optimize the process of secure communication to \ncloud services. \nThe incorrect answers:\nA. RTOS\nAn RTOS (Real-time Operating System) is an OS designed for industrial \nequipment, automobiles, and other time-sensitive applications.\nB. CRL\nA CRL (Certificate Revocation List) is used to determine if a certificate \nhas been administratively revoked. A CRL would not provide any remote \naccess functionality.\nC. Zero-trust\nZero-trust is a security strategy where all devices on the network are \nverified before connecting to another device. Zero-trust does not provide \nremote access functions.\nMore information:\nSY0-701, Objective 3.2 - Secure Communication https://professormesser.com/security-plus/sy0-701/sy0-701-video/secure-communication-sy0-701"}, {"question": "A72.  A company is implementing a quarterly security awareness campaign. \nWhich of the following would MOST likely be part of this campaign?", "options": ["Suspicious message reports from users", "An itemized statement of work", "An IaC configuration file", "An acceptable use policy document"], "answer": "Suspicious message reports from users", "explanation": "A. Suspicious message reports from users\nA security awareness campaign often involves automated phishing \nattempts, and most campaigns will include a process for users to report a \nsuspected phishing attempt to the IT security team.\nThe incorrect answers:\nB. An itemized statement of work\nA statement of work (SOW) is commonly used for service engagements. \nThe SOW provides a list of deliverables for the professional services, and \nthis list is often used to determine if the services were completed.\nC. An IaC configuration file\nAn IaC (Infrastructure as Code) configuration file describes an \ninfrastructure configuration commonly used by cloud-based systems. An \nIaC configuration file would not be used by a security awareness campaign.\nD. An acceptable use policy document\nAn acceptable use policy (AUP) is defined by an employer to describe the \nproper use of technology and systems within an organization. The AUP \nitself is not part of a security awareness campaign.\nMore information:\nSY0-701, Objective 5.6 - Security Awareness https://professormesser.link/*********"}, {"question": "A73. A recent report shows the return of a vulnerability that was previously \npatched four months ago. After researching this issue, the security team \nhas found a recent patch has reintroduced this vulnerability on the servers. \nWhich of the following should the security administrator implement to \nprevent this issue from occurring in the future?", "options": ["Containerization", "Data masking", "802.1X", "Change management"], "answer": "Change management", "explanation": "D. Change management\nThe change management process includes a testing phase that can help \nidentify potential issues relating to an application change or upgrade.\nThe incorrect answers:\nA. Containerization\nContainerization is an efficient method of deploying application instances, \nbut it doesn't provide any mitigation for security vulnerabilities.\nB. Data masking\nData masking can be used to limit access to sensitive data, but it does not \nprevent the implementation of a security vulnerability.\nC. 802.1X\n802.1X is a standard for port-based network access control, and it can \nhelp manage the authentication process of network users. 802.1X does not \nprovide any mitigation for security vulnerabilities.\nMore information:\nSY0-701, Objective 1.3 - Change Management Process https://professormesser.link/*********"}, {"question": "A74.  A security manager would like to ensure that unique hashes are used with \nan application login process. Which of the following would be the BEST \nway to add random data when generating a set of stored password hashes?", "options": ["Salting", "Obfuscation", "Key stretching", "Digital signature"], "answer": "Salting", "explanation": "A. Salting\nAdding random data, or salt, to a password when performing the hashing \nprocess will create a unique hash, even if other users have chosen the same \npassword.\nThe incorrect answers:\nB. Obfuscation\nObfuscation is the process of making something difficult for humans to \nread or understand. The obfuscation process isn't commonly associated \nwith adding random information to hashes.\nC. Key stretching\nKey stretching uses a cryptographic key multiple times for additional \nprotection against brute force attacks. Key stretching by itself does not \ncommonly add random data to the hashing process.\nD. Digital signature\nDigital signatures use a hash and asymmetric encryption to provide \nintegrity of data. Digital signatures aren't commonly used for storing \npasswords.\nMore information:\nSY0-701, Objective 1.4 - Hashing and Digital Signatures https://professormesser.link/*********"}, {"question": "A75.  Which cryptographic method is used to add trust to a digital certificate?", "options": ["Steganography", "Hash", "Symmetric encryption", "Digital signature"], "answer": "Digital signature", "explanation": "D. Digital signature\nA certificate authority will digitally sign a certificate to add trust. If you \ntrust the certificate authority, you can therefore trust the certificate.\nThe incorrect answers:\nA. Steganography\nSteganography is a technique for hiding information inside of another \nmedia type. Steganography is a method of obfuscating data and does not \nprovide a method of adding trust to a certificate.\nB. Hash\nA hash can help verify that the certificate has not been altered, but it does \nnot provide additional third-party trust.\nC. Symmetric encryption\nSymmetric encryption provides data confidentiality, but it doesn't add any \nadditional trust to the encryption process.\nMore information:\nSY0-701, Objective 1.4 - Hashing and Digital Signatures https://professormesser.link/*********"}, {"question": "A76.  A company is using SCAP as part of their security monitoring processes. \nWhich of the following would BEST describe this implementation?", "options": ["Train the user community to better identify phishing attempts", "Present the results of an internal audit to the board", "Automate the validation and patching of security issues", "Identify and document authorized data center visitors"], "answer": "Automate the validation and patching of security issues", "explanation": "<PERSON>. Automate the validation and patching of security issues\nSCAP (Security Content Automation Protocol) focuses on the \nstandardization of vulnerability management across multiple security tools. \nThis allows different tools to identify and act on the same security criteria.\nThe incorrect answers:\nA. Train the user community to better identify phishing attempts\nSecurity awareness training is an important part of an overall security \nstrategy, but the training process does not generally involve SCAP .\nB. Present the results of an internal audit to the board\nA presentation of audit results can provide important feedback, but the \npresentation itself does not generally use SCAP .\nD. Identify and document authorized data center visitors\nThe identification and documentation process for visitors is an important \nsecurity policy, but it does not generally require the use of SCAP .\nMore information:\nSY0-701, Objective 4.4 - Security Tools https://professormesser.link/*********"}, {"question": "A77.  An organization maintains a large database of customer information for \nsales tracking and customer support. Which person in the organization \nwould be responsible for managing the access rights to this data?", "options": ["Data processor", "Data owner", "Data subject", "Data custodian"], "answer": "Data custodian", "explanation": "D. Data custodian\nThe data custodian manages access rights and sets security controls  \nto the data.\nThe incorrect answers:\nA. Data processor\nThe data processor manages the operational use of the data, but not the \nrights and permissions to the information.\nB. Data owner\nThe data owner is usually a higher-level executive who makes business \ndecisions regarding the data.\nC. Data subject\nThe data subjects are the individuals who have their personal information \ncontained in this customer information database.\nMore information:\nSY0-701, Objective 5.1 - Data Roles and Responsibilities https://professormesser.link/*********"}, {"question": "A78.  An organization’s content management system currently labels files \nand documents as “Public” and “Restricted.” On a recent update, a new \nclassification type of “Private” was added. Which of the following would \nbe the MOST likely reason for this addition?", "options": ["Minimized attack surface", "Simplified categorization", "Expanded privacy compliance", "Decreased search time"], "answer": "Expanded privacy compliance", "explanation": "C. Expanded privacy compliance\nThe labeling of data as private is often associated with compliance and \nconfidentiality concerns.\nThe incorrect answers:\nA. Minimized attack surface\nThe categorization of data has little impact on the size of the potential \nattack surface associated with a system.\nB. Simplified categorization\nAdding additional categories would not commonly be considered a \nsimplification.\nD. Decreased search time\nAdding additional classifications would not necessarily provide any \ndecreased search times.\nMore information:\nSY0-701, Objective 3.3 - Data Types and Classifications https://professormesser.link/*********"}, {"question": "A79.  A corporate security team would like to consolidate and protect the \nprivate keys across all of their web servers. Which of these would be the \nBEST way to securely store these keys?", "options": ["Integrate an HSM", "Implement full disk encryption on the web servers", "Use a TPM", "Upgrade the web servers to use a UEFI BIOS"], "answer": "Integrate an HSM", "explanation": "A. Integrate an HSM\nAn HSM (Hardware Security Module) is a high-end cryptographic \nhardware appliance that can securely store keys and certificates for all \ndevices.\nThe incorrect answers:\nB. Implement full disk encryption on the web servers\nFull-disk encryption would only protect the keys if someone does not have \nthe proper credentials, and it won’t help consolidate all of the web server \nkeys to a central point.\nC. Use a TPM\nA TPM (Trusted Platform Module) is used on individual devices to \nprovide cryptographic functions and securely store encryption keys. \nIndividual TPMs would not provide any consolidation of web server \nprivate keys.\nD. Upgrade the web servers to use a UEFI BIOS\nA UEFI (Unified Extensible Firmware Interface) BIOS (Basic Input/\nOutput System) does not provide any additional security or consolidation \nfeatures for web server private keys.\nMore information:\nSY0-701, Objective 1.4 - Encryption Technologies https://professormesser.link/701010404"}, {"question": "A80.  A security technician is reviewing this security log from an IPS:\nALERT 2023-06-01 13:07:29 [163bcf65118-179b547b]\nCross-Site Scripting in JSON Data\n*************:3332 -> *************:80\nURL/index.html - Method POST - Query String \"-\"\nUser Agent: curl/7.21.3 (i386-redhat-linux-gnu) libcurl/7.21.3 \nNSS/******** zlib/1.2.5 libidn/1.19 libssh2/1.2.7\nDetail: token=\"<script>\" key=\"key7\" value=\"<script>alert(2)</script>\"\nWhich of the following can be determined from this log information? \n(doua)\n ", "multiple answers": 2, "options": ["The alert was generated from a malformed User Agent header", "The alert was generated from an embedded script", "The attacker’s IP address is *************", "The attacker’s IP address is *************", "The alert was generated due to an invalid client port number"], "answer": ["The alert was generated from an embedded script", "The attacker’s IP address is *************"], "explanation": "The Answer: B. The alert was generated from an embedded script and \nC. The attacker’s IP address is ************* \nThe details of the IPS (Intrusion Prevention System) alert show a script value embedded into JSON ( JavaScript Object Notation) data. The IPS log also shows the flow of the attack with an arrow in the middle. \nThe attacker was IP address ************* with port 3332, and the victim was ************* over port 80.\nThe incorrect answers:\nA. The alert was generated from a malformed User Agent header \nThe user agent information is provided as additional supporting data associated with the alert. The agent itself is not the cause of this alert. \nD. The attacker’s IP address is ************* \nThe attacker’s IP address is listed first, so the victim's IP address is *************.\nE. The alert was generated due to an invalid client port number \nThe port number associated with the client, 3332, is a valid port number and not associated with the cause of the alert.\nMore information:\nSY0-701, Objective 4.9 - Log Data https://professormesser.link/*********"}, {"question": "A81. Which of the following describes a monetary loss if one event occurs?", "options": ["ALE", "SLE", "RTO", "ARO"], "answer": "SLE", "explanation": "B. SLE\nSLE (Single Loss Expectancy) describes the financial impact of \na single event.\nThe incorrect answers:\nA. ALE\nALE (Annual Loss Expectancy) is the financial loss over an entire  \n12-month period.\nC. RTO\nRTO (Recovery Time Objectives) define a timeframe needed to restore a \nparticular service level.\nD. ARO\nThe ARO (Annualized Rate of Occurrence) is the number of times an \nevent will occur in a 12-month period.\nMore information:\nSY0-701, Objective 5.2 - Risk Analysis https://professormesser.link/*********"}, {"question": "A82.  A user with restricted access has typed this text in a search field of an \ninternal web-based application:  \n \nUSER77' OR '1'='1  \n \nAfter submitting this search request, all database records are displayed on \nthe screen. Which of the following would BEST describe this search?", "options": ["Cross-site scripting", "Buffer overflow", "SQL injection", "SSL stripping"], "answer": "SQL injection", "explanation": "C. SQL injection\nSQL (Structured Query Language) injection takes advantage of poor \ninput validation to circumvent the application and allows the attacker to \nquery the database directly.\nThe incorrect answers:\nA. Cross-site scripting\nCross-site scripting takes advantage of a third-party trust to a web \napplication. The attack demonstrated in this question does not use another \nuser's credentials or access rights to obtain information.\nB. Buffer overflow\nA buffer overflow uses an application vulnerability to submit more \ninformation than an application can properly manage. The attack syntax \nin this question is specific to SQL injections, and it does not appear to be \nmanipulating a buffer overflow vulnerability.\nD. SSL stripping\nSSL stripping is a downgrade attack that modifies web site addresses to \nallow access to encrypted information. The attack in this question does not \nappear to include a third-party.\nMore information:\nSY0-701, Objective 2.3 - SQL Injection https://professormesser.link/*********"}, {"question": "A83.  A user has opened a helpdesk ticket complaining of poor system \nperformance, excessive pop up messages, and the cursor moving \nwithout anyone touching the mouse. This issue began after they opened \na spreadsheet from a vendor containing part numbers and pricing \ninformation. Which of the following is MOST likely the cause of this \nuser's issues?", "options": ["On-path", "<PERSON><PERSON>", "Trojan horse", "Logic bomb"], "answer": "Trojan horse", "explanation": "C. Trojan horse\nSince a Trojan horse is usually disguised as legitimate software, the \nvictim often doesn’t realize they’re installing malware. Once the Trojan is \ninstalled, the attacker can install additional software to control the infected \nsystem.\nThe incorrect answers:\nA. On-path\nAn on-path attack commonly occurs without any knowledge to the parties \ninvolved, and there’s usually no additional notification that an attack is \nunderway.\nB. Worm\nA worm is malware that can replicate itself between systems without any \nuser intervention, so a spreadsheet that requires additional a user to click \nwarning messages would not be categorized as a worm.\nD. Logic bomb\nA logic bomb is malware that installs and operates silently until a certain \nevent occurs. Once the logic bomb has been triggered, the results usually \ninvolve loss of data or a disabled operating system.\nMore information:\nSY0-701, Objective 1.2 - An Overview of Malware https://professormesser.link/*********"}, {"question": "A84.  A web-based manufacturing company processes monthly charges to credit \ncard information saved in the customer's profile. All of the customer \ninformation is encrypted and protected with additional authentication \nfactors. Which of the following would be the justification for these \nsecurity controls?", "options": ["Chain of custody", "Password vaulting", "Compliance reporting", "Sandboxing"], "answer": "Compliance reporting", "explanation": "C. Compliance reporting\nThe storage of sensitive information such as customer details and payment \ninformation may require additional reporting to ensure compliance with \nthe proper security controls.\nThe incorrect answers:\nA. Chain of custody\nChain of custody describes the control and integrity of collected evidence. \nChain of custody would not involve the implementation of encryption and \nauthentication factors in this example.\nB. Password vaulting\nPassword vaults are used as secure storage and retrieval of authentication \ncredentials. The protection of user data is not associated with password \nvaulting.\nD. Sandboxing\nSandboxing is the process of running a service or system in a protected \nenvironment. This sandbox allows for testing and analysis without affecting \nother systems that may currently be in production.\nMore information:\nSY0-701, Objective 5.4 - Compliance https://professormesser.link/*********"}, {"question": "A85.  A security manager has created a report showing intermittent network \ncommunication from certain workstations on the internal network to one \nexternal IP address. These traffic patterns occur at random times during \nthe day. Which of the following would be the MOST likely reason for \nthese traffic patterns?", "options": ["On-path attack", "Keylogger", "Replay attack", "Brute force"], "answer": "Keylogger", "explanation": "B. Keylogger\nA keylogger captures keystrokes and occasionally transmits this \ninformation to the attacker for analysis. The traffic patterns identified \nby the security manager could potentially be categorized as malicious \nkeylogger transfers.\nThe incorrect answers:\nA. On-path attack\nAn on-path attack is an exploit often associated with a device monitoring \ndata in the middle of a conversation. This question did not provide any \nevidence of third-party monitoring.\nC. Replay attack\nA replay attack is often used by an attacker to gain access to a service \nthrough the use of credentials gathered from a previous authentication. \nInternal devices communicating to an external server would not be a \ncommon pattern for a replay attack.\nD. Brute force\nA brute force attack attempts to find authentication credentials by \nattempting to guess a password. In this example, the source of the traffic \nand the traffic patterns don't match those seen with common brute force \nattempts.\nMore information:\nSY0-701, Objective 2.4 - Other Malware Types https://professormesser.link/*********"}, {"question": "A86.  The security policies in a manufacturing company prohibit the \ntransmission of customer information. However, a security administrator \nhas received an alert that credit card numbers were transmitted as an \nemail attachment. Which of the following was the MOST likely source \nof this alert message?", "options": ["IPS", "DLP", "RADIUS", "IPsec"], "answer": "DLP", "explanation": "B. DLP\nDLP (Data Loss Prevention) technologies can identify and block the \ntransmission of sensitive data across the network.\nThe incorrect answers:\nA. IPS\nIPS (Intrusion Prevention System) signatures are useful for identifying \nknown vulnerabilities, but they don't commonly provide a way to identify \nand block PII (Personally Identifiable Information) or sensitive data.\nC. RADIUS\nRADIUS (Remote Authentication Dial-In User Service) is an \nauthentication protocol commonly used to validate user credentials. \nRADIUS would not be used to identify sensitive data transfers.\nD. IPsec\nIPsec (Internet Protocol Security) is a protocol suite for authenticating \nand encrypting network communication. IPsec does not include any \nfeatures for identifying and alerting on sensitive information.\nMore information:\nSY0-701, Objective 4.4 - Security Tools https://professormesser.link/*********"}, {"question": "A87.  A security administrator has configured a virtual machine in a screened \nsubnet with a guest login account and no password. Which of the \nfollowing would be the MOST likely reason for this configuration?", "options": ["The server is a honeypot for attracting potential attackers", "The server is a cloud storage service for remote users", "The server will be used as a VPN concentrator", "The server is a development sandbox for third-party  \nprogramming projects"], "answer": "The server is a honeypot for attracting potential attackers", "explanation": "A. The server is a honeypot for attracting potential attackers\nA screened subnet is a good location to configure services that can be \naccessed from the Internet, and building a system that can be easily \ncompromised is a common tactic for honeypot systems.\nThe incorrect answers:\nB. The server is a cloud storage service for remote users\nAlthough cloud storage is a useful service, configuring storage on a server \nwith an open guest account is not a best practice.\nC. The server will be used as a VPN concentrator\nVPN (Virtual Private Networking) concentrators should be installed \non secure devices, and configuring an open guest account would not be \nconsidered a secure configuration.\nD. The server is a development sandbox for third-party  \nprogramming projects\nIt would not be secure to configure a development sandbox on a system \nwith an open guest account.\nMore information:\nSY0-701, Objective 1.2 - Deception and Disruption https://professormesser.link/*********"}, {"question": "A88.  A security administrator is configuring a DNS server with a SPF record. \nWhich of the following would be the reason for this configuration?", "options": ["Transmit all outgoing email over an encrypted tunnel", "List all servers authorized to send emails", "Digitally sign all outgoing email messages", "Obtain disposition instructions for emails marked as spam"], "answer": "List all servers authorized to send emails", "explanation": "B. List all servers authorized to send emails\nSPF (Sender Policy Framework) is used to publish a list of all authorized \nemail servers for a specific domain.\nThe incorrect answers:\nA. Transmit all outgoing email over an encrypted tunnel\nThe option to use encrypted protocols for email transfer is configured in \nthe email server and not in the DNS (Domain Name System) server.\nC. Digitally sign all outgoing email messages\n<PERSON><PERSON><PERSON> (Domain Keys Identified Mail) is used to publish the public key \nused for the digital signature for all outgoing email.\nD. Obtain disposition instructions for emails marked as spam\nA DMARC (Domain-based Message Authentication, Reporting, and \nConformance) record announces the preferred email disposition if a \nmessage is identified as spam. DMARC options include accepting the \nmessages, sending them to a spam folder, or simply rejecting the emails.\nMore information:\nSY0-701, Objective 4.5 - Email Security https://professormesser.link/*********"}, {"question": "A89.  A company would like to securely deploy applications without the \noverhead of installing a virtual machine for each system. Which of the \nfollowing would be the BEST way to deploy these applications?", "options": ["Containerization", "IoT", "Proxy", "RTOS"], "answer": "Containerization", "explanation": "A. Containerization\nApplication containerization uses a single virtual machine to use as a \nfoundation for separate application \"containers.\" These containers are \nimplemented as isolated instances, and an application in one container is \nnot inherently accessible from other containers on the system.\nThe incorrect answers:\nB. IoT\nIoT (Internet of Things) is a broad category of embedded devices often \ninstalled in our homes and businesses. IoT devices are not commonly \nassociated with the application deployment process.\nC. Proxy\nProxies can be used as security devices, but they aren't commonly used for \ndeploying application instances.\nD. RTOS\nRTOS (Real-Time Operating Systems) are designed for time-sensitive \napplications and services. Manufacturing equipment and transportation \nsystems often incorporate an RTOS.\nMore information:\nSY0-701, Objective 3.1 - Other Infrastructure Concepts https://professormesser.link/*********"}, {"question": "A90.  A company has just purchased a new application server, and the security \ndirector wants to determine if the system is secure. The system is currently \ninstalled in a test environment and will not be available to users until the \nroll out to production next week. Which of the following would be the \nBEST way to determine if any part of the system can be exploited?", "options": ["Tabletop exercise", "Vulnerability scanner", "DDoS", "Penetration test"], "answer": "Penetration test", "explanation": "D. Penetration test\nA penetration test can be used to actively exploit potential vulnerabilities \nin a system or application. This could cause a denial of service or loss of \ndata, so the best practice is to perform the penetration test during non-\nproduction hours or in a test environment.\nThe incorrect answers:\nA. Tabletop exercise\nA tabletop exercise is used to talk through a security event with an \nincident response team around a conference room table. This is commonly \nperformed as a training device instead of performing a full-scale disaster \ndrill.\nB. Vulnerability scanner\nVulnerability scanners may identify a vulnerability, but they do not actively \nattempt to exploit the vulnerability.\nC. DDoS\nA DDoS (Distributed Denial of Service) attack is often used to disable \na service or application, but it doesn't provide any particular information \nregarding an application vulnerability.\nMore information:\nSY0-701, Objective 4.3 - Penetration Testing https://professormesser.link/*********"}, {"question": "B6. A security administrator has performed an audit of the organization’s \nproduction web servers, and the results have identified default \nconfigurations, web services running from a privileged account, and \ninconsistencies with SSL certificates. Which of the following would be the \nBEST way to resolve these issues?", "options": ["Server hardening", "Multi-factor authentication", "Enable HTTPS", "Run operating system updates"], "answer": "Server hardening", "explanation": "A. Server hardening\nMany applications and services include secure configuration guides to \nassist in hardening the system. These hardening steps will make the system \nas secure as possible while simultaneously allowing the application to run \nefficiently.\nThe incorrect answers:\nB. Multi-factor authentication\nAlthough multi-factor authentication is always a good best practice, \nsimply enabling multiple authentication methods would not resolve the \nissues identified during the audit.\nC. Enable HTTPS\nMost web servers will use HTTPS to ensure that network communication \nis encrypted. However, requiring encrypted network traffic would not \ncorrect the issues identified during the audit.\nD. Run operating system updates\nKeeping the system updated is another good best practice, but the issues \nidentified during the audit were not related to OS patches. Many of the \nissues identified in the audit appear to be related to the configuration \nof the web server, so any resolution should focus on correcting these \nconfiguration issues.\nMore information:\nSY0-701, Objective 2.5 - Hardening Techniques https://professormesser.link/*********"}, {"question": "B7. A shipping company stores information in small regional warehouses \naround the country. The company maintains an IPS at each warehouse to \nwatch for suspicious traffic patterns. Which of the following would BEST \ndescribe the security control used at the warehouse?", "options": ["Deterrent", "Compensating", "Directive", "Detective"], "answer": "Detective", "explanation": "D. Detective\nAn IPS can detect, alert, and log an intrusion attempt. The IPS could also \nbe categorized as a preventive control, since it has the ability to actively \nblock known attacks.\nThe incorrect answers:\nA. Deterrent\nA deterrent discourages an intrusion attempt, but it doesn't directly \nprevent the access. An application splash screen or posted warning sign \nwould be categorized as a deterrent.\nB. Compensating\nA compensating control can’t prevent an attack, but it can provide an \nalternative when an attack occurs. For example, a compensating control \nwould include the re-imaging of a compromised server.\nC. Directive\nDirective control types are guidelines offered to help direct a subject \ntowards security compliance. Training users on the proper storage of \nsensitive files would be an example of a directive control.\nMore information:\nSY0-701, Objective 1.1 - Security Controls https://professormesser.link/*********"}, {"question": "B8. The Vice President of Sales has asked the IT team to create daily backups \nof the sales data. The Vice President is an example of a:", "options": ["Data owner", "Data controller", "Data steward", "Data processor"], "answer": "Data owner", "explanation": "A. Data owner\nThe data owner is accountable for specific data, so this person is often a \nsenior officer of the organization.\nThe incorrect answers:\nB. Data controller\nA data controller manages the processing of the data. For example, a \npayroll department would be a data controller, and a payroll servicing \ncompany would be the data processor.\nC. Data steward\nThe data steward manages access rights to the data. In this example, the IT \nteam would be the data steward.\nD. Data processor\nThe data processor is often a third-party that processes data on behalf of \nthe data controller.\nMore information:\nSY0-701, Objective 5.1 - Data Roles and Responsibilities\nhttps://professormesser.link/*********"}, {"question": "B9. A security engineer is preparing to conduct a penetration test of a thirdparty website. Part of the preparation involves reading through social media posts for information about this site. Which of the following describes this practice? ", "options": ["Partially known environment", "OSINT", "Exfiltration", "Active reconnaissance"], "answer": "OSINT", "explanation": "B. OSINT\nOSINT (Open Source Intelligence) describes the process of obtaining \ninformation from open sources such as social media sites, corporate \nwebsites, online forums, and other publicly available locations.\nThe incorrect answers:\nA. Partially known environment \nA partially known environment describes how aware an attacker might be \nabout a test. The attacker may have access to some information about the \ntest, but not all information is disclosed.\nC. Exfiltration\nExfiltration describes the theft of data by an attacker. \nD. Active reconnaissance\nActive reconnaissance would show some evidence of data gathering. \nFor example, performing a ping scan or DNS query wouldn’t exploit a \nvulnerability, but it would show that someone was gathering information.\nMore information:\nSY0-701, Objective 4.3 - Threat Intelligence https://professormesser.link/*********"}, {"question": "B10.   A company would like to orchestrate the response when a virus is \ndetected on company devices. Which of the following would be the \nBEST way to implement this function?", "options": ["Active reconnaissance", "Log aggregation", "Vulnerability scan", "Escalation scripting"], "answer": "Escalation scripting", "explanation": "D. Escalation scripting\nScripting and automation can provide methods to automate or orchestrate \nthe escalation response when a security issue is detected.\nThe incorrect answers:\nA. Active reconnaissance \nActive reconnaissance will gather information about a system, but it does \nnot provide any ongoing monitoring or response features.\nB. Log aggregation\nLog aggregation provides a method of centralizing evidence and log files \nfor reporting and future analysis. The aggregated log does not inherently \nprovide a response to a security event.\nC. Vulnerability scan\nA vulnerability scan will identify any known vulnerabilities that may be \nassociated with a system. However, a vulnerability scan will not identify \nreal-time infections or automate the response.\nMore information:\nSY0-701, Objective 4.7 - Scripting and Automation https://professormesser.link/*********"}, {"question": "B11. A user in the accounting department has received a text message from \nthe CEO. The message requests payment by cryptocurrency for a recently \npurchased tablet. Which of the following would BEST describe this \nattack?", "options": ["Brand impersonation", "Watering hole attack", "Smishing", "Typosquatting"], "answer": "Smishing", "explanation": "C. Smishing\nSmishing is phishing using SMS (Short Message Service), and is more \ncommonly referenced as text messaging. A message allegedly from the \nCEO asking for an unusual payments using cryptocurrency or gift cards \nwould be categorized as smishing.\nThe incorrect answers:\nA. Brand impersonation\nBrand impersonation usually involves a third-party pretending to be an \nemployee or representative of another (usually well-known) company. This \ntext message did not claim a particular brand or trademark as part of the \nattack.\nB. Watering hole attack\nA watering hole attack requires users to visit a central website or location. \nViewing this text message did not require the user to visit any third-party \nwebsites. \nD. Typosquatting\nA typosquatting attack commonly uses a misspelling of a domain name to \nredirect victims to a malicious website.\nMore information:\nSY0-701, Objective 2.1 - Phishing https://professormesser.link/*********"}, {"question": "B12.   A company has been informed of a hypervisor vulnerability that could \nallow users on one virtual machine to access resources on another \nvirtual machine. Which of the following would BEST describe this \nvulnerability?", "options": ["Containerization", "Jailbreaking", "SDN", "Escape"], "answer": "Escape", "explanation": "D. Escape\nA VM (Virtual Machine) escape is a vulnerability that allows \ncommunication between separate VMs.\nThe incorrect answers:\nA. Containerization\nContainerization is an application deployment architecture that uses \na self-contained group of application code and dependencies. Using \ncontainerization, many separate containers can be deployed simultaneously \non a single system.\nB. Jailbreaking\nJailbreaking describes the replacement of firmware on a mobile phone \nor tablet with the goal of enabling or allowing features that would not \nnormally be available. For example, a jailbroken phone or tablet can install \nsoftware from locations other than the primary app store.\nC. SDN\nSDN (Software-Defined Networking) separates the control plane of \ndevices from the data plane. This allows for more automation and dynamic \nchanges to the infrastructure.\nMore information:\nSY0-701, Objective 2.3 - Virtualization Vulnerabilities https://professormesser.link/701020309"}, {"question": "B13. While working from home, users are attending a project meeting over \na web conference. When typing in the meeting link, the browser is \nunexpectedly directed to a different website than the web conference. \nUsers in the office do not have any issues accessing the conference site. \nWhich of the following would be the MOST likely reason for this issue?", "options": ["Buffer overflow", "Wireless disassociation", "Amplified DDoS", "DNS poisoning"], "answer": "DNS poisoning", "explanation": "D. DNS poisoning\nAn attacker with access to a DNS (Domain Name System) server can \nmodify the DNS configuration files and redirect users to a different \nwebsite. Anyone using a different DNS server may not see any problems \nwith connectivity to the original site.\nThe incorrect answers:\n<PERSON><PERSON> overflow\nA buffer overflow vulnerability is associated with application input \nthat exceeds the expected input size. A buffer overflow would cause an \napplication to fail or perform unusually, but a buffer overflow would not \nappear as a redirected web server from a DNS lookup.\nB. Wireless deauthentication\nWireless deauthentication would cause users on a wireless network to \nconstantly disconnect. Wireless deauthentication would not cause a \nredirection of a website.\nC. Amplified DDoS\nAn amplified DDOS (Distributed Denial of Service) would attack a \nservice from many different devices and cause the service to be unavailable.  \nThis attack sends specially crafted packets to maximize the amount of \ntraffic seen in the response. In this example, the service did not document \nany availability problems.\nMore information:\nSY0-701, Objective 2.4 - DNS Attacks https://professormesser.link/*********"}, {"question": "B14. A company is launching a new internal application that will not start \nuntil a username and password is entered and a smart card is plugged into \nthe computer. Which of the following BEST describes this process?", "options": ["Federation", "Accounting", "Authentication", "Authorization"], "answer": "Authentication", "explanation": "C. Authentication\nThe process of proving who you say you are is authentication. In this \nexample, the password and smart card are two factors of authentication, \nand both reasonably prove that the person with the login credentials is \nauthentic.\nThe incorrect answers:\nA. Federation \nFederation provides a way to authenticate and authorize between two \ndifferent organizations. In this example, the authentication process uses \ninternal information without any type of connection or trust to a  \nthird-party.\nB. Accounting\nAccounting will document information regarding a user’s session, such as \nlogin time, data sent and received, files transferred, and logout time.\nD. Authorization\nThe authorization process assigns users to resources. This process \ncommonly occurs after the authentication process is complete.\nMore information:\nSY0-701, Objective 1.2 \nAuthentication, Authorization, and Accounting https://professormesser.link/*********"}, {"question": "B15. An online retailer is planning a penetration test as part of their PCI DSS \nvalidation. A third-party organization will be performing the test, and \nthe online retailer has provided the Internet-facing IP addresses for their \npublic web servers. No other details were provided. What penetration \ntesting methodology is the online retailer using?", "options": ["Known environment", "Passive reconnaissance", "Partially known environment", "Benchmarks"], "answer": "Partially known environment", "explanation": "C. Partially known environment\nA partially known environment test is performed when the attacker knows \nsome information about the victim, but not all information is available.\nThe incorrect answers:\nA. Known environment \nA known environment test is performed when the attacker has complete \ndetails about the victim’s systems and infrastructure.\nB. Passive reconnaissance\nPassive reconnaissance is the process of gathering information from \npublicly available sites, such as social media or corporate websites.\nD. Benchmarks\nSecurity benchmarks describe a set of best practices to apply to an \napplication, operating system, or any other service. A benchmark does not \ndescribe the information provided to a vulnerability scanning service.\nMore information:\nSY0-701, Objective 5.5 - Penetration Tests https://professormesser.link/*********"}, {"question": "B16.   A manufacturing company produces radar used by commercial and \nmilitary organizations. A recently proposed policy change would allow the \nuse of mobile devices inside the facility. Which of the following would be \nthe MOST significant threat vector issue associated with this change in \npolicy?", "options": ["Unauthorized software on rooted devices", "Remote access clients on the mobile devices", "Out of date mobile operating systems", "Loss of intellectual property"], "answer": "Loss of intellectual property", "explanation": "D. Loss of intellectual property\nThe exfiltration of confidential information and intellectual property is \nrelatively simple with an easily transportable mobile phone. Organizations \nassociated with sensitive products or services must always be aware of the \npotential for information leaks using files, photos, or video.\nThe incorrect answers:\nA. Unauthorized software on rooted devices \nAlthough unauthorized software use can be a security issue, it isn’t as \nsignificant as the exfiltration of intellectual property.\nB. Remote access clients on the mobile devices\nIt’s sometimes convenient to have a remote access client available, and this \ntype of access can certainly be a concern if the proper security is not in \nplace. However, the much more significant security issue in this list would \nbe associated with the ease of photos and videography when working with \nconfidential information.\nC. Out of date mobile operating systems\nHaving an outdated operating system can potentially include security \nvulnerabilities, but these vulnerabilities do not have the significance of an \nactive data exfiltration method.\nMore information:\nSY0-701, Objective 2.2 - Common Threat Vectors https://professormesser.link/*********"}, {"question": "B17. Which of the following would be the BEST way for an organization to \nverify the digital signature provided by an external email server?", "options": ["Perform a vulnerability scan", "View the server's device certificate", "Authenticate to a RADIUS server", "Check the DKIM record"], "answer": "Check the DKIM record", "explanation": "D. Check the DKIM record\nA DKIM (Domain Keys Identified Mail) record is a DNS (Domain \nName System) entry that includes the public key associated with an email \nserver's digital signatures. A legitimate email server will digitally sign all \noutgoing emails and provide the public key in their DNS for third-party \nvalidation.\nThe incorrect answers:\nA. Perform a vulnerability scan \nA vulnerability scan can provide information on any unpatched \napplications or services, but it won't provide digital signature verification \nfor incoming email messages.\nB. View the server's device certificate\nA device certificate can validate the trust of a system, but it does not \nprovide digital signature validation for email servers.\nC. Authenticate to a RADIUS server\nA RADIUS server can verify account credentials, but it does not provide \na method for validating the digital signatures provided by a third-party \nemail server.\nMore information:\nSY0-701, Objective 4.5 - Email Security https://professormesser.link/*********"}, {"question": "B18. A company is using older operating systems for their web servers and \nare concerned of their stability during periods of high use. Which of \nthe following should the company use to maximize the uptime and \navailability of this service?", "options": ["Cold site", "UPS", "Redundant routers", "Load balancer"], "answer": "Load balancer", "explanation": "D. Load balancer\nA load balancer maintains a pool of servers and can distribute the load \nacross those devices. If a device fails, the other servers will continue to \noperate and provide the necessary services.\nThe incorrect answers:\nA. Cold site\nA cold site is commonly used for disaster recovery and would require \nbuilding an infrastructure and installing software before the site would be \nfunctional. Moving the web services to a cold site would not be an efficient \nform of server resiliency.\nB. UPS\nA UPS (Uninterruptible Power Supply) provides an alternative power \nsource when the main power is no longer available. Although this would \nprovide additional uptime for power faults, it does not provide resiliency if \nan operating system crashes.\nC. Redundant routers\nMaintaining multiple routers is common in highly available networks, but \nmultiple routers will not provide uptime if the server operating system was \nto fail.\nMore information:\nSY0-701, Objective 3.4 - Resiliency https://professormesser.link/*********"}, {"question": "B19. A user in the accounting department would like to email a spreadsheet \nwith sensitive information to a list of third-party vendors. Which of the \nfollowing would be the BEST way to protect the data in this email?", "options": ["Full disk encryption", "Key exchange algorithm", "Salted hash", "Asymmetric encryption"], "answer": "Asymmetric encryption", "explanation": "D. Asymmetric encryption\nAsymmetric encryption uses a recipient's public key to encrypt data, \nand this data can only be decrypted with the recipient's private key. This \nencryption method is commonly used with software such as PGP or \nGPG.\nThe incorrect answers:\nA. Full disk encryption\nFull disk encryption protects all data saved on a storage drive, but it does \nnot provide any data protection for messages or attachments sent between \nemail servers.\nB. Key exchange algorithm\nA key exchange algorithm can be used to securely exchange key \ninformation between devices, but it does not provide a method of \nencrypting data. \nC. Salted hash\nA salted hash describes a hash value that includes some additional data \n(the salt) to provide randomization. A salted hash does not provide data \nconfidentiality or encryption.\nMore information:\nSY0-701, Objective 1.4 - Encrypting Data https://professormesser.link/*********"}, {"question": "B20. A system administrator would like to segment the network to give the \nmarketing, accounting, and manufacturing departments their own private \nnetwork. The network communication between departments would \nbe restricted for additional security. Which of the following should be \nconfigured on this network?", "options": ["VPN", "RBAC", "VLAN", "SDN"], "answer": "VLAN", "explanation": "C. VLAN\nA VLAN (Virtual Local Area Network) is a common method of using \na switch to logically segment a network. The devices in each segmented \nVLAN can only communicate with other devices in the same VLAN. A \nrouter is used to connect VLANs, and this router can often be used to \ncontrol traffic flows between the VLANs.\nThe incorrect answers:\nA. VPN \nA VPN (Virtual Private Network) is an encryption technology used \nto secure network connections between sites or remote end-user \ncommunication. VPNs are not commonly used to segment internal \nnetwork communication.\nB. RBAC\nRBAC (Role-Based Access Control) describes a control mechanism for \nmanaging rights and permissions in an operating system. RBAC is not \nused for network segmentation. \nD. SDN\nSDN (Software Defined Networking) separates the planes of operation \nso that infrastructure devices would have a defined control plane and data \nplane. SDN would not be used when segmenting internal networks.\nMore information:\nSY0-701, Objective 2.5 - Segmentation and Access Control https://professormesser.link/*********"}, {"question": "B21. A technician at an MSP has been asked to manage devices on third-party \nprivate network. The technician needs command line access to internal \nrouters, switches, and firewalls. Which of the following would provide the \nnecessary access?", "options": ["HSM", "Jump server", "NAC", "Air gap"], "answer": "Jump server", "explanation": "B. Jump server\nA jump server is a highly secured device commonly used to access secure \nareas of another network. The technician would first connect to the jump \nserver using SSH or a VPN tunnel, and then \"jump\" from the jump server \nto other devices on the inside of the protected network. This would allow \ntechnicians at an MSP (Managed Service Provider) to securely access \ndevices on their customer's private networks.\nThe incorrect answers:\nA. HSM\nAn HSM (Hardware Security Module) is a secure method of \ncryptographic key backup and hardware-based cryptographic offloading.\nC. NAC\nNAC (Network Access Control) is a broad term describing access control \nbased on a health check or posture assessment. NAC will deny access to \ndevices that don't meet the minimum security requirements.\nD. Air gap\nAn air gap is a segmentation strategy that separates devices or networks by \nphysically disconnecting them from each other.\nMore information:\nSY0-701, Objective 3.2 - Network Appliances https://professormesser.link/*********"}, {"question": "B22. A transportation company is installing new wireless access points in their \ncorporate office. The manufacturer estimates the access points will operate \nan average of 100,000 hours before a hardware-related outage. Which of \nthe following describes this estimate?", "options": ["MTTR", "RPO", "RTO", "MTBF"], "answer": "MTBF", "explanation": "D. MTBF\nThe MTBF (Mean Time Between Failures) is the average time expected \nbetween outages. This is usually an estimation based on the internal device \ncomponents and their expected operational lifetime.\nThe incorrect answers:\nA. MTTR \nMTTR (Mean Time to Repair) is the time required to repair a product or \nsystem after a failure.\nB. RPO\nRPO (Recovery Point Objectives) define how much data loss would be \nacceptable during a recovery. \nC. RTO\nRTO (Recovery Time Objectives) define the minimum objectives required \nto get up and running to a particular service level.\nMore information:\nSY0-701, Objective 5.2 - Business Impact Analysis https://professormesser.link/*********"}, {"question": "B23. A security administrator is creating a policy to prevent the disclosure \nof credit card numbers in a customer support application. Users of the \napplication would only be able to view the last four digits of a credit card \nnumber. Which of the following would provide this functionality?", "options": ["Hashing", "Tokenization", "Masking", "Salting"], "answer": "Masking", "explanation": "C. Masking\nData masking hides data from being viewed. The full credit card numbers \nare stored in a database, but only a limited view of this data is available \nwhen accessing the information from the application.\nThe incorrect answers:\nA. Hashing\nHashing is a method of storing a digital fingerprint of data. In this \nexample, the last four digits displayed are the actual card numbers and not \na hash of the card numbers.\nB. Tokenization\nTokenization replaces sensitive data with a non-sensitive placeholder. In \nthis example, the only visible information is part of the actual card number. \nTokenization is not used to replace any of the card numbers.\nD. Salting\nSalting adds randomized data when performing a hashing function. \nSalting is often used to add additional randomization when storing \npasswords.\nMore information:\nSY0-701, Objective 3.3 - Protecting Data https://professormesser.link/*********"}, {"question": "B24. A user is authenticating through the use of a PIN and a fingerprint. \nWhich of the following would describe these authentication factors?", "options": ["Something you know, something you are", "Something you are, somewhere you are", "Something you have, something you know", "Somewhere you are, something you are"], "answer": "Something you know, something you are", "explanation": "A. Something you know, something you are\nA PIN (Personal Identification Number) is something you know, and a \nfingerprint is something you are.\nThe incorrect answers:\nB. Something you are, somewhere you are \nA fingerprint would be categorized as something you are, but a somewhere \nyou are could be a set of GPS coordinates or IP addresses.\nC. Something you have, something you know\nSomething you have could be an smart ID card or phone app, and \nsomething you know could be a PIN or password. \nD. Somewhere you are, something you are\nSomewhere you are would be a location, and something you are would be \na biometric reading.\nMore information:\nSY0-701, Objective 4.6 - Multi-factor Authentication https://professormesser.link/*********"}, {"question": "B25. A security administrator is configuring the authentication process used by \ntechnicians when logging into wireless access points and switches. Instead \nof using local accounts, the administrator would like to pass all login \nrequests to a centralized database. Which of the following would be the \nBEST way to implement this requirement?", "options": ["COPE", "AAA", "IPsec", "SIEM"], "answer": "AAA", "explanation": "B. AAA\nUsing AAA (Authentication, Authorization, and Accounting) is a \ncommon method of centralizing authentication. Instead of having separate \nlocal accounts on different devices, users can authenticate with account \ninformation maintained in a centralized database.\nThe incorrect answers:\nA. COPE\nCOPE (Corporate-owned, personally enabled) devices are purchased by \nthe organization and enabled for both business and personal use. A COPE \ndevice does not provide any centralized authentication functionality.\nC. IPsec\nIPsec is commonly used as an encrypted tunnel between sites or endpoints. \nIt’s useful for protecting data sent over the network, but IPsec isn’t used to \ncentralize the authentication process. \nD. SIEM\nA SIEM (Security Information and Event Management) service provides \ncentralized logging and reporting for network infrastructure devices. A \nSIEM service does not provide any centralized authentication features.\nMore information:\nSY0-701, Objective 4.1 - Wireless Security Settings https://professormesser.link/*********"}, {"question": "B26. A recent audit has determined that many IT department accounts have \nbeen granted Administrator access. The audit recommends replacing these \npermissions with limited access rights. Which of the following would \ndescribe this policy?", "options": ["Password vaulting", "Offboarding", "Least privilege", "Discretionary access control"], "answer": "Least privilege", "explanation": "C. Least privilege\nThe policy of least privilege limits the rights and permissions of a user \naccount to only the access required to accomplish their objectives. This \npolicy would limit the scope of an attack originating from a user in the IT \ndepartment.\nThe incorrect answers:\nA. Password vaulting\nPassword vaulting is a secure way to store and retrieve passwords, but it \ndoesn't include a policy for limiting system access.\nB. Offboarding\nThe offboarding process describes the policies and procedures associated \nwith someone leaving the organization or someone who is no longer an \nemployee of the company.\nD. Discretionary access control\nWith discretionary access control (DAC), access and permissions are \ndetermined by the owner or originator of the files or resources.\nMore information:\nSY0-701, Objective 4.6 - Access Controls https://professormesser.link/*********"}, {"question": "B27. A recent security audit has discovered usernames and passwords which \ncan be easily viewed in a packet capture. Which of the following did the \naudit identify?", "options": ["Weak encryption", "Improper patch management", "Insecure protocols", "Open ports"], "answer": "Insecure protocols", "explanation": "C. Insecure protocols\nAn insecure authentication protocol will transmit information \"in the \nclear,\" or without any type of encryption or protection.\nThe incorrect answers:\nA. <PERSON> encryption\nA weak encryption cipher will appear to protect data, but instead can \nbe commonly circumvented to reveal the plaintext. In this example, the \nusernames and passwords were not encrypted in any way and could be \nviewed in a packet capture.\nB. Improper patch management\nMaintaining systems to the latest patch version will protect against \nvulnerabilities and security issues. Sending information in the clear over \nthe network is not commonly associated with an unpatched system.\nD. Open ports\nOpen ports are usually associated with a service or application on a \ndevice. An open port is not commonly associated with any encryption or \nprotected network communication.\nMore information:\nSY0-701, Objective 4.5 - Secure Protocols https://professormesser.link/*********"}, {"question": "B28. Before deploying a new application, a company is performing an internal \naudit to ensure all of their servers are configured with the appropriate \nsecurity features. Which of the following would BEST describe this \nprocess?", "options": ["Due care", "Active reconnaissance", "Data retention", "Statement of work"], "answer": "Due care", "explanation": "A. Due care\nDue care describes a duty to act honestly and in good faith. Due diligence \nis often associated with third-party activities, and due care tends to refer to \ninternal activities.\nThe incorrect answers:\nB. Active reconnaissance\nActive reconnaissance refers to the process of collecting information \nbefore a penetration test. Active reconnaissance includes activities that will \ncommunicate to devices where traffic can be logged.\nC. Data retention\nData retention involves the collection and storage of data over time. For \nexample, many organizations are required to collect and store years of \nemail records or financial documents.\nD. Statement of work\nA statement of work is often used during a professional services \nengagement to detail a list of specific tasks to complete. In this example, all \nof the work is part of an internal audit and does not include any mention \nof third-party professional services.\nMore information:\nSY0-701, Objective 5.4 - Compliance https://professormesser.link/*********"}, {"question": "B29. An organization has previously purchased insurance to cover a \nransomware attack, but the costs of maintaining the policy have increased \nabove the acceptable budget. The company has now decided to cancel \nthe insurance policies and address potential ransomware issues internally. \nWhich of the following would best describe this action?", "options": ["Mitigation", "Acceptance", "Transference", "Risk-avoidance"], "answer": "Acceptance", "explanation": "B. Acceptance\nRisk acceptance is a business decision that places the responsibility of the \nrisky activity on the organization itself.\nThe incorrect answers:\nA. Mitigation\nIf the organization was to purchase additional backup facilities and update \ntheir backup processes to include offline backup storage, they would be \nmitigating the risk of a ransomware infection.\nC. Transference\nPurchasing insurance to cover a risky activity is a common method of \ntransferring risk from the organization to the insurance company. \nD. Risk-avoidance\nTo avoid the risk of ransomware, the organization would need to \ncompletely disconnect from the Internet and disable all methods that \nransomware might use to infect a system. This risk response technique \nwould most likely not apply to ransomware.\nMore information:\nSY0-701, Objective 5.2 - Risk Management Strategies https://professormesser.link/*********"}, {"question": "B30. Which of these threat actors would be MOST likely to install a \ncompany's internal application on a public cloud provider?", "options": ["Organized crime", "Nation state", "Shadow IT", "<PERSON><PERSON><PERSON><PERSON>"], "answer": "Shadow IT", "explanation": "C. Shadow IT\nShadow IT is an internal organization within the company but is not part \nof the IT department. Shadow IT often circumvents or ignores existing IT \npolicies to build their own infrastructure with company resources.\nThe incorrect answers:\nA. Organized crime \nOrganized crime is usually motivated by money. An organized crime \ngroup is more interested in stealing information than installing company \napplications in a public cloud.\nB. Nation state\nNation states are highly sophisticated hackers, and their efforts are usually \nfocused on obtaining confidential government information or disrupting \ngovernmental operations. \n<PERSON>. Hacktivist\nA hacktivist often has a political statement to make, and their hacking \nefforts would commonly result in a public display of that information. \nHowever, a hacktivist would not install company application on a public \ncloud provider's network.\nMore information:\nSY0-701, Objective 2.1 - Threat Actors https://professormesser.link/*********"}, {"question": "B31. An IPS report shows a series of exploit attempts were made against \nexternally facing web servers. The system administrator of the web servers \nhas identified a number of unusual log entries on each system. Which of \nthe following would be the NEXT step in the incident response process?", "options": ["Check the IPS logs for any other potential attacks", "Create a plan for removing malware from the web servers", "Disable any breached user accounts", "Disconnect the web servers from the network"], "answer": "Disconnect the web servers from the network", "explanation": "<PERSON><PERSON> Disconnect the web servers from the network\nThe unusual log entries on the web server indicate that the system may \nhave been exploited. In that situation, the servers should be contained to \nprevent all connectivity to those systems.\nThe incorrect answers:\nA. Check the IPS logs for any other potential attacks \nBefore looking for additional intrusions, the devices showing a potential \nexploit should be contained.\n<PERSON><PERSON> <PERSON>reate a plan for removing malware from the web servers\nThe eradication and recovery processes should occur after the systems have \nbeen isolated and contained. \nC. Disable any breached user accounts\nDisabling accounts is part of the recovery process, and it should occur after \nthe exploited servers are contained.\nMore information:\nSY0-701, Objective 4.8 - Incident Response https://professormesser.link/*********"}, {"question": "B32. A security administrator is viewing the logs on a laptop in the shipping \nand receiving department and identifies these events:\n8:55:30 AM | D:\\Downloads\\ChangeLog-5.0.4.scr | Quarantine Success\n  9:22:54 AM | C:\\Program Files\\Photo Viewer\\ViewerBase.dll | Quarantine Failure\n  9:44:05 AM | C:\\Sales\\Sample32.dat | Quarantine Success\n  \nWhich of the following would BEST describe the circumstances \nsurrounding these events?", "options": ["The antivirus application identified three viruses andquarantined two viruses", "The host-based firewall blocked two traffic flows", "A host-based allow list has blocked two applications from \nexecuting", "A network-based IPS has identified two known vulnerabilities"], "answer": "The antivirus application identified three viruses andquarantined two viruses", "explanation": "A. The antivirus application identified three viruses and\nquarantined two viruses\nThe logs are showing the name of files on the local device and a quarantine \ndisposition, which indicates that two of the files were moved (quarantined) \nto a separate area of the drive. This will prevent the malicious files from \nexecuting and will safely store the files for any future investigation. The \nsecond file in the list failed the quarantine process, and was most likely \nbecause the library was already in use by the operating system and could \nnot be moved.\nThe incorrect answers:\nB. The host-based firewall blocked two traffic flows\nA host-based firewall will allow or deny traffic flows based on IP address, \nport number, application, or other criteria. A host-based firewall does not \nblock traffic flows based on the name of an existing file, and the firewall \nprocess would not quarantine or move files to other folders\n AnswersC. A host-based allow list has blocked two applications from executing\nThe “quarantine” disposition refers to a file that has been moved from \none location to another. An allow list function would simply stop \nthe application from executing without changing the location of an \napplication file. \nD. A network-based IPS has identified two known vulnerabilities\nThe logs from a network-based IPS (Intrusion Prevention System) would \nnot commonly be located on a user’s laptop, and those logs would display \nallow or deny dispositions based on the name of a known vulnerability. A \nnetwork-based IPS would also not commonly move (quarantine) files on \nan end-user’s computer.\nMore information:\nSY0-701, Objective 4.9- Log Data https://professormesser.link/*********"}, {"question": "B33. In the past, an organization has relied on the curated Apple App Store to \navoid issues associated with malware and insecure applications. However, \nthe IT department has discovered an iPhone in the shipping department \nwith applications not available on the Apple App Store. How did the \nshipping department user install these apps on their mobile device?", "options": ["Side loading", "Malicious update", "VM escape", "Cross-site scripting"], "answer": "Side loading", "explanation": "A. Side loading\nIf Apple’s iOS has been circumvented using jailbreaking, a user can install \napps without using the Apple App Store. Circumventing a curated app \nstore to install an app manually is called side loading.\nThe incorrect answers:\nB. Malicious update\nA malicious update would patch an existing app and would not commonly \ninstall a different app onto a mobile device.\nC. VM escape\nVM (Virtual Machine) escape describes the unauthorized access of one \nVM from a different VM on the same hypervisor. An app installation on a \nphone is not related to virtual machines. \nD. Cross-site scripting\nCross-site scripting is an attack that uses the trust in a browser to gain \naccess to a third-party site. The installation of an app isn't commonly \nassociated with cross-site scripting.\nMore information:\nSY0-701, Objective 2.3 - Mobile Device Vulnerabilities https://professormesser.link/*********"}, {"question": "B34. A company has noticed an increase in support calls from attackers. \nThese attackers are using social engineering to gain unauthorized access \nto customer data. Which of the following would be the BEST way to \nprevent these attacks?", "options": ["User training", "Next-generation firewall", "Internal audit", "Penetration testing"], "answer": "User training", "explanation": "A. User training\nMany social engineering attacks do not involve technology, so the best \nway to prevent the attack is to properly train users to watch for these \ntechniques.\nThe incorrect answers:\nB. Next-generation firewall\nA next-generation firewall can provide extensive protection against attacks \ninvolving technology, but a firewall can't stop a phone conversation or \nsimilar type of social engineering.\nC. Internal audit\nAn internal audit may be able to recognize and report on the increase \nin social engineering attacks, but an audit does not provide a method of \nstopping the attack from originally occurring.\nD. Penetration testing\nPenetration testing can identify vulnerabilities and can attempt to exploit \nthose vulnerabilities. Penetration testing does not block an attack from \noccurring.\nMore information:\nSY0-701, Objective 5.6 - User Training https://professormesser.link/*********"}, {"question": "B35. As part of an internal audit, each department of a company has been \nasked to compile a list of all devices, operating systems, and applications \nin use. Which of the following would BEST describe this audit?", "options": ["Attestation", "Self-assessment", "Regulatory compliance", "Vendor monitoring"], "answer": "Self-assessment", "explanation": "B. Self-assessment\nA self-assessment describes an organization performing their own security \nchecks.\nThe incorrect answers:\nA. Attestation\nAttestation is commonly one of the last steps when performing an audit. \nThis attestation is an opinion of the truth or accuracy of a company’s \nsecurity position.\nC. Regulatory compliance\nRegulatory compliance is often required to validate a specific security \nposture. For example, an organization storing credit card information may \nbe required by regulation to ensure the confidentiality of that data. This \nquestion does not mention any type of regulation as the reason for this \ninformation gathering.\nD. Vendor monitoring\nWhen working with a third-party, it's often important to maintain an \nongoing audit and monitoring processes with the vendor. In this example, \nall of the information gathering is with internal company departments.\nMore information:\nSY0-701, Objective 5.5 - Audits and Assessments https://professormesser.link/*********"}, {"question": "B36. A company is concerned about security issues at their remote sites. Which \nof the following would provide the IT team with more information of  \npotential shortcomings?", "options": ["Gap analysis", "Policy administrator", "Change management", "Dependency list"], "answer": "Gap analysis", "explanation": "A. Gap analysis\nA gap analysis is a formal process comparing the current security posture \nwith where the company would like to be. This often examines many \ndifferent aspects of the overall security environment.\nThe incorrect answers:\nB. Policy administrator\nThe Policy Administrator is used in a zero-trust environment to generate \naccess tokens or credentials.\nC. Change management\nThe change management process is important for the controlled \ndeployment of system changes, but it doesn't help provide an overview of \nsecurity shortcomings.\nD. Dependency list\nA list of dependencies is often used during technical change management \nto plan for any potential changes. Before a change can occur, all of the \ndependencies associated with that change must be addressed.\nMore information:\nSY0-701, Objective 1.2 - Gap Analysis https://professormesser.link/*********"}, {"question": "B37. An attacker has identified a number of devices on a corporate network \nwith the username of “admin” and the password of “admin.” Which of the \nfollowing describes this situation?", "options": ["Open service ports", "Default credentials", "Unsupported systems", "<PERSON><PERSON>"], "answer": "Default credentials", "explanation": "B. Default credentials\nWhen a device is first installed, it will often have a default set of \ncredentials such as admin/password or admin/admin. If these default \ncredentials are never changed, they would allow access by anyone who \nknows the default configuration.\nThe incorrect answers:\nA. Open service ports \nService ports are commonly opened when an inbound connection needs to \nbe made to a service. For example, a web server will open ports 80 and 443 \nto allow all incoming traffic requests by the service.\nC. Unsupported systems\nUnsupported systems describe devices or services no longer supported \nby the manufacturer. An unsupported system may not receive ongoing \nsecurity patches or updates.\nD. Phishing\nPhishing uses social engineering to obtain sensitive or private information. \nA device using the default credentials would not require a phishing attack \nto determine the valid username and password.\nMore information:\nSY0-701, Objective 2.2 - Common Threat Vectors https://professormesser.link/*********"}, {"question": "B38. A security administrator attends an annual industry convention with \nother security professionals from around the world. Which of the \nfollowing attacks would be MOST likely in this situation?", "options": ["Smishing", "Supply chain", "SQL injection", "Watering hole"], "answer": "Watering hole", "explanation": "D. Watering hole\nA watering hole attack infects a third-party visited by the intended \nvictims. An industry convention would be a perfect location to attack \nsecurity professionals.\nThe incorrect answers:\nA. Smishing\nSmishing, or SMS phishing, is a phishing attack over text messaging. A \nsecurity administrator attending an industry event would not be the best \npossible scenario for smishing.\nB. Supply chain\nA supply chain attack infects part of the product manufacturing process in \nan attempt to also infect everything further down the chain. An industry \ntrade event would not be a common vector for a supply chain attack.\nC. SQL injection\nA SQL (Structured Query Language) injection attack takes advantage of \na software vulnerability to allow direct access to a SQL database. A SQL \ninjection is not commonly directed towards an individual or an event.\nMore information:\nSY0-701, Objective 2.2 - Watering Hole Attacks https://professormesser.link/*********"}, {"question": "B39. A transportation company headquarters is located in an  area with frequent power surges and outages. The security  administrator is concerned about the potential for  downtime and hardware failures. Which of the following  would provide the most protection against these issues?  doua.", "multiple answers": 2, "options": ["UPS", "Parallel processing", "Snapshots", "Multi-cloud systemrd", "Load balancing", "Generator"], "answer": ["UPS", "Generator"], "explanation": "BThe Answers: A. UPS and F. Generator\n  A UPS (Uninterruptible Power Supply) can provide backup power for a\n  limited time when the main power source is unavailable, and a generator\n  can maintain uptime as long as a fuel source is available.\n  The incorrect answers:\n  B. Parallel processing\n  Parallel processing uses multiple processors across multiple systems to\n  improve the performance of an application. Parallel processing will not\n  protect against power outages.\n  C. Snapshots\n  A snapshot is a type of backup commonly associated with virtual machines\n  (VMs). Taking the snapshot of a VM can provide an easy method of\n  reverting to an earlier configuration, but it doesn't help for power issues.\n  D. Multi-cloud system\n  An application hosted across multiple cloud providers would not provide\n  any resiliency for power-related issues in a local data center.\n  E. Load balancing\n  Load balancers provide a method of managing busy services by increasing\n  the number of available servers and balancing the load between them. A  load balancer won't provide any help with power issues, however.\n  More information:\n  SY0-701, Objective 3.4 - Power Resiliency\n  https://professormesser.link/*********"}, {"question": "B40. An organization has developed an in-house mobile device app for order  processing. The developers would like the app to identify revoked server  certificates without sending any traffic over the corporate Internet  connection. Which of the following must be configured to allow this  functionality?", "options": ["CSR generation", "OCSP stapling", "Key escrow", "Wildcard"], "answer": "OCSP stapling", "explanation": "B. OCSP stapling\nThe use of OCSP (Online Certificate Status Protocol) requires \ncommunication between the client and the issuing CA (Certificate \nAuthority). If the CA is an external organization, then validation checks \nwill communicate across the Internet. The certificate holder can verify \ntheir own status and avoid client Internet traffic by storing the status \ninformation on an internal server and “stapling” the OCSP status into the \nSSL/TLS handshake.\nThe incorrect answers:\nA. CSR generation\nA CSR (Certificate Signing Request) is used during the key creation \nprocess. The certificate is sent to the CA to be signed as part of the CSR.\nC. Key escrow\nKey escrow will provide a third-party with access to decryption keys. The \nescrow process is not involved in real-time server revocation updates. \nD. Wildcard\nA wildcard certificate can be used across many different systems matching \nthe fully qualified domain name associated with the wildcard.\nMore information:\nSY0-701, Objective 1.4 - Certificates https://professormesser.link/*********"}, {"question": "B41. A security administrator has been asked to build a network link to secure \nall communication between two remote locations. Which of the following \nwould be the best choice for this task?", "options": ["SCAP", "Screened subnet", "IPsec", "Network access control"], "answer": "IPsec", "explanation": "C. IPsec\nIPsec (Internet Protocol Security) is commonly used to create a VPN \n(Virtual Private Network) protected tunnel between devices or locations.\nThe incorrect answers:\nA. SCAP \nThe SCAP (Security Content Automation Protocol) is used as a common \nprotocol across multiple security tools. SCAP is not used to provide an \nencrypted tunnel between two locations.\nB. Screened subnet\nA screened subnet is a protected area commonly used to host public \nservices without allowing access to an organization's internal private \nnetwork.\nD. Network access control\nNetwork access control (NAC) describes the authentication and \nauthorization process when devices connect to a network. NAC is not used \nto connect two sites over an encrypted channel.\nMore information:\nSY0-701, Objective 3.2 - Secure Communication https://professormesser.link/701030206"}, {"question": "B42. A Linux administrator has received a ticket complaining of response \nissues with a database server. After connecting to the server, the \nadministrator views this information:\nFilesystem Size Used Avail Use% Mounted on\n  /dev/xvda1 158G 158G 0 100% /\n  \nWhich of the following would BEST describe this information?", "options": ["Buffer overflow", "Resource consumption", "SQL injection", "Race condition"], "answer": "Resource consumption", "explanation": "B. Resource consumption\nThe available storage on the local filesystem has been depleted, and the \ninformation shows 0 bytes available. More drive space would need to be \navailable for the server to return to normal response times.\nThe incorrect answers:\n<PERSON><PERSON> overflow\nA buffer overflow allows an attacker to manipulate the contents of \nmemory. A filesystem at 100% utilization does not describe the  \ncontents in memory.\nC. SQL injection\nA SQL injection is a network attack type used to access database \ninformation directly. A SQL injection would not cause significant storage \ndrive utilization.\nD. Race condition\nA race condition is a programming issue where a portion of the \napplication is making changes not seen by other parts of the application. \nA race condition does not commonly use all available storage space on the \ndevice.\nMore information:\nSY0-701, Objective 2.4 - Indicators of Compromise https://professormesser.link/701020415"}, {"question": "B43. Which of the following can be used for credit card transactions from a \nmobile device without sending the actual credit card number across the \nnetwork?", "options": ["Tokenization", "Hashing", "Steganography", "Masking"], "answer": "Tokenization", "explanation": "A. Tokenization\nTokenization replaces sensitive data with a non-sensitive placeholder. \nTokenization is commonly used for NFC (Near-Field Communication) \npayment systems, and sends a single-use token across the network instead \nof the actual credit card information.\nThe incorrect answers:\nB. Hashing\nHashing creates a digital \"fingerprint\" of data, but hashing isn't used to \ntransfer card numbers or other financial details from one device to another.\nC. Steganography\nSteganography describes hiding data within other media types. For \nexample, it's common to use steganography to hide text documents within \nan image file. However, steganography is not commonly used to transfer \ncredit card transactions across the network.\nD. Masking\nData masking hides some of the original data to protect sensitive \ninformation. Credit card transfers cannot omit or hide data necessary to \ncomplete the transaction.\nMore information:\nSY0-701, Objective 3.3 - Protecting Data https://professormesser.link/*********"}, {"question": "B44. A security administrator receives a report each week showing a Linux \nvulnerability associated with a Windows server. Which of the following \nwould prevent this information from appearing in the report?", "options": ["Alert tuning", "Application benchmarking", "SIEM aggregation", "Data archiving"], "answer": "Alert tuning", "explanation": "A. Alert tuning\nOur monitoring systems are not always perfect, and many require ongoing \ntuning to properly configure alerts and notifications of important events.\nThe incorrect answers:\nB. Application benchmarking \nCreating an application benchmark can help with the planning and \nimplementation of security monitoring. However, the creation of an \napplication benchmark does not change the alert messages created by a \nthird-party monitoring system.\nC. SIEM aggregation\nA SIEM (Security Information and Event Manager) can be used to \naggregate all log files to a centralized reporting system. Creating a \ncentralized log repository does not remove invalid alerts from a weekly \nreport.\nD. Data archiving\nMany organizations are required to archive data for long-term security \nmonitoring. Simply archiving the data does not change the alert \nnotification in a weekly report.\nMore information:\nSY0-701, Objective 4.4 - Security Monitoring https://professormesser.link/*********"}, {"question": "B45. Which of the following would a company use to calculate the loss of a \nbusiness activity if a vulnerability is exploited?", "options": ["Risk tolerance", "Vulnerability classification", "Environmental variables", "Exposure factor"], "answer": "Exposure factor", "explanation": "D. Exposure factor\nAn exposure factor describes a loss of value to the organization. For \nexample, a network throughput issue might limit access to half of the \nusers, creating a 50% exposure factor. A completely disabled service would \ncalculated as a 100% exposure factor.\nThe incorrect answers:\nA. Risk tolerance \nRisk tolerance describes the amount of risk that would be acceptable to an \norganization. For example, an organization may tolerate the risk involved \nwith a delay so that patches can be tested prior to deployment.\nB. Vulnerability classification\nMost vulnerabilities are classified into categories and are often assigned a \nscore to designate the severity of the known vulnerability.\nC. Environmental variables\nAn environmental variable is considered when prioritizing patches and \nsecurity responses. For example, a device in the production network \nenvironment will probably have priority over the devices in a test lab \nenvironment.\nMore information:\nSY0-701, Objective 4.3 - Analyzing Vulnerabilities https://professormesser.link/*********"}, {"question": "B46. An administrator is designing a network to be compliant with a security \nstandard for storing credit card numbers. Which of the following would \nbe the BEST choice to provide this compliance?", "options": ["Implement RAID for all storage systems", "Connect a UPS to all servers", "DNS should be available on redundant servers", "Perform regular audits and vulnerability scans"], "answer": "Perform regular audits and vulnerability scans", "explanation": "D. Perform regular audits and vulnerability scans\nA focus of credit card storage compliance is to keep credit card \ninformation private. The only option matching this requirement is \nscheduled audits and ongoing vulnerability scans.\nThe incorrect answers:\nA. Implement RAID for all storage systems \nRAID (Redundant Array of Independent Disks) is an important \nconsideration for any project that stores data, but using a RAID array \nis not part of this compliance requirement. Although compliance may \ninclude backups, RAID is not a backup technology.\nB<PERSON> Connect a UPS to all servers\nIntegrating a UPS (Uninterruptible Power Supply) is an important way \nto maintain power during an outage, but it's not required for security \ncompliance for data storage.\nC. DNS should be available on redundant servers\nName resolution can be an important service on the network, but \nmaintaining redundant DNS servers isn't required for compliance with \nsensitive data storage.\nMore information:\nSY0-701, Objective 5.4 - Compliance https://professormesser.link/*********"}, {"question": "B47. A company is accepting proposals for an upcoming project, and one of \nthe responses is from a business owned by a board member. Which of the \nfollowing would describe this situation?", "options": ["Due diligence", "Vendor monitoring", "Conflict of interest", "Right-to-audit"], "answer": "Conflict of interest", "explanation": "C. Conflict of interest\nA conflict of interest occurs when a personal interest in a business \ntransaction could compromise the judgment of the people involved. \nPersonal and family relationships between organizations may potentially \nbe a conflict of interest.\nThe incorrect answers:\nA. Due diligence\nDue diligence is the process of investigating and verifying information \nbefore doing business with an organization.\nB. Vendor monitoring\nVendor monitoring involves ongoing management of the vendor \nrelationship, including ongoing reviews, periodic audits, and other checks \nand balances.\nD. Right-to-audit\nA right-to-audit clause is commonly added to business contracts to ensure \naccess to periodic audits when working with a third-party.\nMore information:\nSY0-701, Objective 5.3 - Third-party Risk Assessment https://professormesser.link/*********"}, {"question": "B48. A company has rolled out a new application that requires the use of a \nhardware-based token generator. Which of the following would be the \nBEST description of this access feature?", "options": ["Something you know", "Somewhere you are", "Something you are", "Something you have"], "answer": "Something you have", "explanation": "D. Something you have\nThe use of the hardware token generator requires the user be in possession \nof the device during the login process.\nThe incorrect answers:\nA. Something you know \nThe number, or token, created by the token generator isn’t previously \nknown by the user, and there’s no requirement to remember the tokens \nonce the authentication process is complete.\nB. Somewhere you are\nThe location of an individual can be a useful authentication factor when \nevaluating the validity of a login request. Someone authenticating from an \nunexpected location or country may be subject to additional authentication \nchecks.\nC. Something you are\nSomething you are describes a biometric factor, such as a fingerprint or \nfacial scan. The token generator works without any type of biometric scan.\nMore information:\nSY0-701, Objective 4.6 - Multi-factor Authentication https://professormesser.link/*********"}, {"question": "B49. A company has signed an SLA with an Internet service provider. Which \nof the following would BEST describe the requirements of this SLA?", "options": ["The customer will connect to remote sites over an IPsec tunnel", "The service provider will provide 99.99% uptime", "The customer applications use HTTPS over tcp/443", "Customer application use will be busiest on the 15th  \nof each month"], "answer": "The service provider will provide 99.99% uptime", "explanation": "B. The service provider will provide 99.99% uptime\nAn SLA (Service Level Agreement) is a contract specifying the minimum \nterms for provided services. It’s common to include uptime, response \ntimes, and other service metrics in an SLA.\nThe incorrect answers:\nA. The customer will connect to remote sites over an IPsec tunnel \nA service level agreement describes the minimum service levels provided \nto the customer. You would not commonly see descriptions of how the \nservice will be used in the SLA contract.\nC. The customer applications use HTTPS over tcp/443\nThe protocols used by the customer’s applications aren’t part of the service \nrequirements from the ISP . \nD. Customer application use will be busiest on the 15th of each month\nThe customer’s application usage isn’t part of the service requirements \nfrom the ISP .\nMore information:\nSY0-701, Objective 5.3 - Agreement Types https://professormesser.link/*********"}, {"question": "B50. An attacker has created multiple social media accounts and is posting \ninformation in an attempt to get the attention of the media. Which of the \nfollowing would BEST describe this attack?", "options": ["On-path", "Watering hole", "Misinformation campaign", "<PERSON><PERSON>"], "answer": "Misinformation campaign", "explanation": "C. Misinformation campaign\nMisinformation campaigns are carefully crafted attacks that exploit social \nmedia and traditional media.\nThe incorrect answers:\nA. On-path \nAn on-path attack uses an attacker in the middle of a conversation to \ncapture or modify information as it traverses the network.\nB. Watering hole\nA watering hole attack uses a carefully selected attack location to infect \nvisitors to a specific website.\nD. Phishing\nA phishing attack uses social engineering to convince the victim to \ndisclose private or sensitive information.\nMore information:\nSY0-701, Objective 2.4 - Other Social Engineering Attacks https://professormesser.link/*********"}, {"question": "B51. Which of the following would be the BEST way to protect credit card \naccount information when performing real-time purchase authorizations?", "options": ["Masking", "DLP", "Tokenization", "NGFW"], "answer": "Tokenization", "explanation": "C. Tokenization\nTokenization is a technique that replaces user data with a non-sensitive \nplaceholder, or token. Tokenization is commonly used on mobile devices \nduring a purchase to use a credit card without transmitting the physical \ncredit card number across the network.\nThe incorrect answers:\nA. Masking\nData masking hides sensitive data by replacing the information with a \nnon-sensitive alternative. An example of masking would be replacing an \naccount number on a receipt with hash marks or asterisks.\nB. DLP\nDLP (Data Loss Prevention) solutions can identify and block sensitive \ndata from being sent over the network. DLP does not provide any \nadditional security or protection for real-time financial transactions.\nD. NGFW\nAn NGFW (Next-Generation Firewall) is an application-aware security \ntechnology. NGFW solutions can provide additional controls for specific \napplications, but they won't provide any additional account protections \nwhen sending financial details.\nMore information:\nSY0-701, Objective 3.3 - Protecting Data https://professormesser.link/*********"}, {"question": "B52. A company must comply with legal requirements for storing customer \ndata in the same country as the customer's mailing address. Which of the \nfollowing would describe this requirement?", "options": ["Geographic dispersion", "Least privilege", "Data sovereignty", "Exfiltration"], "answer": "Data sovereignty", "explanation": "C. Data sovereignty\nData sovereignty laws can mandate how data is handled and stored. Data \nresiding in a country is usually subject to the laws of that country, and \ncompliance regulations may not allow the data to be moved outside of the \ncountry.\nThe incorrect answers:\nA. Geographic dispersion \nGeographic dispersion describes a data resiliency technique for \ndistributing data to different locations to maintain uptime and availability.\nB. Least privilege\nLeast privilege refers to a set of rights and permissions that would limit \naccess based on a user's job requirements. Least privilege does not describe \nthe storage of information based on a geographic location.\nD. Exfiltration\nExfiltration describes the removal or theft of data by a third-party. \nExfiltration is not associated with the geographic storage of information.\nMore information:\nSY0-701, Objective 3.3 - States of Data https://professormesser.link/*********"}, {"question": "B53. A company is installing access points in all of their remote sites. Which \nof the following would provide confidentiality for all wireless data?", "options": ["802.1X", "WPA3", "RADIUS", "MDM"], "answer": "WPA3", "explanation": "B. WPA3\nWPA3 (Wi-Fi Protected Access 3) is an encryption protocol used on \nwireless networks. All data sent over a WPA3-protected wireless network \nwill be encrypted.\nThe incorrect answers:\nA. 802.1X\n802.1X is a standard for authentication using AAA (Authentication, \nAuthorization and Accounting) services. 802.1X is commonly used in \nconjunction with LDAP , RADIUS, or similar authentication services.\nC. RADIUS\nRADIUS (Remote Authentication Dial-In User Service) is an \nauthentication protocol used for centralized authentication. RADIUS \nis commonly used in conjunction with 802.1X, but RADIUS does not \nprovide data confidentiality or encryption.\nD. MDM\nAn MDM (Mobile Device Manager) is used to manage and control an \norganization's mobile phones and tablets. MDM policies are not used to \nmanage the confidentiality settings of a wireless access point.\nMore information:\nSY0-701, Objective 4.1 - Wireless Security Settings https://professormesser.link/*********"}, {"question": "B54. A security administrator has found a keylogger installed in an update \nof the company's accounting software. Which of the following would \nprevent the transmission of the collected logs?", "options": ["Prevent the installation of all software", "Block all unknown outbound network traffic at the Internet firewall", "Install host-based anti-virus software", "Scan all incoming email attachments at the email gateway"], "answer": "Block all unknown outbound network traffic at the Internet firewall", "explanation": "B. Block all unknown outbound network traffic at the\nInternet firewall\nKeylogging software has two major functions; record user input, and \ntransmit that information to a remote location. Local file scanning and \nsoftware best-practices can help prevent the initial installation, and \ncontrolling outbound network traffic can block unauthorized file transfers.\nThe incorrect answers:\nA. Prevent the installation of all software \nBlocking software installations may prevent the initial malware infection, \nbut it won’t provide any control of outbound data.\nC. Install host-based anti-virus software\nA good anti-virus application can identify malware before the installation \noccurs, but anti-virus does not commonly provide any control of network \ncommunication. \nD. Scan all incoming email attachments at the email gateway\nMalware can be installed from many sources, and sometimes the source \nis unexpected. Scanning or blocking executables at the email gateway can \nhelp prevent infection but it won’t provide any control of outbound file \ntransfers.\nMore information:\nSY0-701, Objective 2.4 - Other Malware Types https://professormesser.link/*********"}, {"question": "B55. A user in the marketing department is unable to connect to the wireless network. After authenticating with a username and password, the user \nreceives this message:\n-- -- --  \nThe connection attempt could not be completed.  \nThe Credentials provided by the server could not be validated.  \nRadius Server: radius.example.com  \nRoot CA: Example.com Internal CA Root Certificate  \n-- -- --\nThe access point is configured with WPA3 encryption and 802.1X \nauthentication.  \n \nWhich of the following is the MOST likely reason for this login issue?", "options": ["The user’s computer is in the incorrect VLAN", "The RADIUS server is not responding", "The user’s computer does not support WPA3 encryption", "The user is in a location with an insufficient wireless signal ", "The client computer does not have the proper certificate installed"], "answer": "The client computer does not have the proper certificate installed", "explanation": "E. The client computer does not have the proper\ncertificate installed\nThe error message states that the server credentials could not be validated. \nThis indicates that the certificate authority that signed the server’s \ncertificate is either different than the CA certificate installed on the \nclient’s workstation, or the client workstation does not have an installed \ncopy of the CA’s certificate. This validation process ensures that the client \nis communicating to a trusted server and there are no on-path attacks \noccurring.\nThe incorrect answers:\nA. The user’s computer is in the incorrect VLAN \nThe RADIUS server certificate validation process should work properly \nfrom all VLANs. The error indicates that the communication process is \nworking properly, so an incorrect VLAN would not be the cause of this \nissue.219\nPractice Exam B - AnswersB. The RADIUS server is not responding\nIf the RADIUS server had no response to the user, then the process would \nsimply timeout. In this example, the error message indicates that the \ncommunication process is working between the RADIUS server and the \nclient’s computer. \nC. The user’s computer does not support WPA3 encryption\nThe first step when connecting to a wireless network is to associate with \nthe 802.11 access point. If WPA3 encryption was not supported, the \nauthentication process would not have occurred and the user’s workstation \nwould not have seen the server credentials.\nD. The user is in a location with an insufficient wireless signal\nThe error message regarding server validation indicates that the wireless \nsignal is strong enough to send and receive data on the wireless network.\nMore information:\nSY0-701, Objective 1.4 - Certificates https://professormesser.link/*********"}, {"question": "B56. A security administrator has created a new policy prohibiting the use of \nMD5 hashes due to collision problems. Which of the following describes \nthe reason for this new policy?", "options": ["Two different messages have different hashes", "The original message can be derived from the hash", "Two identical messages have the same hash", "Two different messages share the same hash"], "answer": "Two different messages share the same hash", "explanation": "D. Two different messages share the same hash\nA well-designed hashing algorithm will create a unique hash value for \nevery possible input. If two different inputs create the same hash, the hash \nalgorithm has created a collision.\nThe incorrect answers:\nA. Two different messages have different hashes \nIn normal operation, two different inputs will create two different hash \noutputs.\nB. The original message can be derived from the hash\nHashing is a one-way cipher, and you cannot derive the original message \nfrom a hash value. \nC. Two identical messages have the same hash\nTwo identical messages should always create exactly the same hash output.\nMore information:\nSY0-701, Objective 2.4 - Cryptographic Attacks https://professormesser.link/701020413"}, {"question": "B57. A security administrator has been tasked with hardening all internal web \nservers to control access from certain IP address ranges and ensure all \ntransferred data remains confidential. Which of the following should the \nadministrator include in his project plan? (doua)\n ", "multiple answers": 2, "options": ["Change the administrator password", "Use HTTPS for all server communication", "Uninstall all unused software", "Enable a host-based firewall", "Install the latest operating system update"], "answer": ["Use HTTPS for all server communication", "Enable a host-based firewall"], "explanation": "B. Use HTTPS for all server communication, and  \n<PERSON><PERSON> En<PERSON> a host-based firewall\nUsing the secure HTTPS (Hypertext Transfer Protocol Secure) protocol \nwill ensure that all network communication is encrypted between the web \nserver and the client devices. A host-based firewall can be used to allow or \ndisallow traffic from certain IP address ranges.\nThe incorrect answers:\nA. Change the administrator password\nOccasionally changing administrator passwords is a good best practice, \nbut it doesn't directly address the goals of IP address filtering and data \nconfidentiality.\nC. Uninstall all unused software\nUninstalling unused software is an important hardening technique, but \nuninstalling software does not control access from IP address ranges, and \nit does not provide any data confidentiality.\nE. Install the latest operating system update\nInstalling an operating system update can be a useful security task, but an \nOS update does not directly encrypt network traffic and does not control \naccess from certain IP addresses.\nMore information:\nSY0-701, Objective 2.5 - Hardening Techniques\nhttps://professormesser.link/*********"}, {"question": "B58.  A security administrator has identified the installation of ransomware \non a database server and has quarantined the system. Which of the \nfollowing should be followed to ensure that the integrity of the evidence \nis maintained?", "options": ["E-discovery", "Non-repudiation", "Chain of custody", "Legal hold"], "answer": "Chain of custody", "explanation": "C. Chain of custody\nA chain of custody is a documented record of the evidence. The chain of \ncustody also documents the interactions of every person who comes into \ncontact with the evidence to maintain the integrity.\nThe incorrect answers:\nA. E-discovery\nE-discovery is the process of collecting, preparing, reviewing, interpreting, \nand producing electronic documents. However, e-discovery does not \nprovide any additional integrity of the data.\nB. Non-repudiation\nNon-repudiation ensures the author of a document cannot be disputed. \nNon-repudiation does not provide any method of tracking and managing \ndigital evidence.\nD. Legal hold\nA legal hold is a technique for preserving important evidence, but it \ndoesn't provide any mechanism for the ongoing integrity of that evidence.\nMore information:\nSY0-701, Objective 4.8 - Digital Forensics https://professormesser.link/*********"}, {"question": "B59. Which of the following would be the BEST option for application \ntesting in an environment completely separated from the production \nnetwork?", "options": ["Virtualization", "VLANs", "Cloud computing", "Air gap"], "answer": "Air gap", "explanation": "D. Air gap\nAn air gapped network removes all connectivity between components and \nensures there would be no possible communication path between the test \nnetwork and the production network.\nThe incorrect answers:\nA. Virtualization \nAlthough virtualization provides the option to connect devices in a \nprivate network, there’s still the potential for a misconfigured network \nconfiguration or an application to communicate externally.\nB. VLANs\nVLANs (Virtual Local Area Networks) are a common segmentation \ntechnology, but a router could easily connect the VLANs to the \nproduction network. \nC. Cloud computing\nCloud-based technologies provide for many network options, and it’s \ncommon to maintain a connection between the cloud and the rest of the \nnetwork.\nMore information:\nSY0-701, Objective 3.1 - Network Infrastructure Concepts https://professormesser.link/*********"}, {"question": "B60. A security engineer is planning the installation of a new IPS. The network \nmust remain operational if the IPS is turned off or disabled. Which of the \nfollowing would describe this configuration?", "options": ["Containerization", "Load balancing", "Fail open", "Tunneling"], "answer": "Fail open", "explanation": "C. Fail open\nAn IPS (Intrusion Prevention System) designed to fail open will maintain \nnetwork connectivity during an outage or failure of the IPS. Even if the \nIPS was not actively preventing an intrusion, the network would still be up \nand running.\nThe incorrect answers:\nA. Containerization\nApplication containerization describes the process of creating a \ndeployment strategy where each application runs in a self-contained \nimage. Containerization allows organizations to quickly deploy and run \ndifferent application instances on the same hardware.\nB. Load balancing\nA load balancer will divide a single load among many different servers to \nprovide faster response and a more efficient use of network resources. Load \nbalancers do not maintain connectivity for an intrusion prevention system.\nD. Tunneling\nTunneling describes the process of transferring data inside of another \nprotocol type, such as sending encrypted data over a VPN (Virtual Private \nNetwork).  Tunneling would not maintain network connectivity if an IPS \nwas to fail.\nMore information:\nSY0-701, Objective 3.2 - Intrusion Prevention https://professormesser.link/701030202"}, {"question": "B61. Which of the following describes the process of hiding data from others \nby embedding the data inside of a different media type?", "options": ["Hashing", "Obfuscation", "Encryption", "Masking"], "answer": "Obfuscation", "explanation": "B. Obfuscation\nObfuscation is the process of taking something normally understandable \nand making it very difficult to understand or to be seen. One common \nobfuscation method used by steganography is to embed a document \nwithin an image file.\nThe incorrect answers:\nA. Hashing\nHashing creates a digital \"fingerprint\" based on the contents of data. This \nhash provides a method of checking data integrity, but it doesn't hide data \nwithin other media types.\nC. Encryption\nEncrypting source code will provide data confidentiality, but encrypting \nthe data does not require any type of subterfuge. Conversely, hiding \ndata within another media type does not necessarily require any type of \nencryption.\nD. Masking\nData masking hides portions of the data by replacing it with a different \nvalue. For example, replacing a credit card number with a series of asterisks \nwould be a common form of data masking.\nMore information:\nSY0-701, Objective 3.3 - Protecting Data https://professormesser.link/*********"}, {"question": "B62. Which of the following vulnerabilities would be the MOST significant \nsecurity concern when protecting against a hacktivist?", "options": ["Data center access with only one authentication factor", "Spoofing of internal IP addresses when accessing an intranet server", "Employee VPN access uses a weak encryption cipher", "Lack of patch updates on an Internet-facing database server"], "answer": "Lack of patch updates on an Internet-facing database server", "explanation": "<PERSON><PERSON> <PERSON><PERSON> of patch updates on an Internet-facing\ndatabase server\nOne of the easiest ways for a third-party to obtain information is through \nan existing Internet connection. A hacktivist could potentially exploit an \nunpatched server to obtain unauthorized access to the operating system \nand data.\nThe incorrect answers:\nA. Data center access with only one authentication factor \nMost hacktivists don’t have access to walk around inside of your \nbuilding, and they certainly wouldn’t have access to secure areas. A single \nauthentication method would commonly prevent unauthorized access \nto a data center for both employees and non-employees, although more \nauthentication factors would provide additional security.\nB. Spoofing of internal IP addresses when accessing an intranet server\nIntranet servers are not accessible from the outside. This makes them an \nunlikely target for hacktivists and other non-employees. \nC. Employee VPN access uses a weak encryption cipher\nA weak encryption cipher can be a security issue, but a potential \nexploitation would require the network traffic to begin any decryption \nattempts. Although this scenario would technically be possible if someone \nwas to catch an employee on a public wireless network, it’s not the most \nsignificant security issue in the available list.\nMore information:\nSY0-701, Objective 2.1 - Threat Actors https://professormesser.link/*********"}, {"question": "B63. A company is installing a security appliance to protect the organization's \nweb-based applications from attacks such as SQL injections and \nunexpected input. Which of the following would BEST describe this \nappliance?", "options": ["WAF", "VPN concentrator", "UTM", "SASE"], "answer": "WAF", "explanation": "A. WAF\nA WAF (Web Application Firewall) is designed as a firewall for \nweb-based applications. WAFs are commonly used to protect against \napplication attacks such as injections, cross-site scripting, and invalid input \ntypes.\nThe incorrect answers:\nB. VPN concentrator\nA VPN (Virtual Private Network) concentrator is the central connectivity \npoint for all remote VPN users. A VPN concentrator would not be used as \nprotection against application attacks.\nC. UTM\nA UTM (Unified Threat Management) appliance acts as a traditional \nfirewall, and many UTMs may also include additional features such \nas intrusion prevention and content filtering. However, UTMs are not \ncommonly used for protection of web-based applications.\nD. SASE\nSASE (Secure Access Service Edge) is a cloud-aware version of a VPN \nclient, and it is commonly deployed as a client on the user device. A SASE \nsolution would not commonly be used to protect a web-based application.\nMore information:\nSY0-701, Objective 3.2 - Firewall Types https://professormesser.link/*********"}, {"question": "B64. Which of the following would be the BEST way to determine if files \nhave been modified after the forensics data acquisition process  \nhas occurred?", "options": ["Use a tamper seal on all storage devices", "Create a hash of the data", "Image each storage device for future comparison", "Take screenshots of file directories with file sizes"], "answer": "Create a hash of the data", "explanation": "<PERSON><PERSON> Create a hash of the data\nA hash creates a unique value and can be quickly validated at any time \nin the future. If the hash value changes, then the data must have also \nchanged.\nThe incorrect answers:\nA. Use a tamper seal on all storage devices \nA physical tamper seal will identify if a device has been opened or \nphysically modified, but it cannot identify any changes to the data on the \nstorage device.\nC. Image each storage device for future comparison\nA copy of the data would allow for comparisons later, but the process of \ncomparing the data would take much more time than simply validating \na hash value. It’s also possible that someone could tamper with both the \noriginal data and the copy of the data.\nD. Take screenshots of file directories with file sizes\nIt’s very easy to change the contents of a file without changing the size of \nthe file. Storing the file sizes would not provide any data integrity checks.\nMore information:\nSY0-701, Objective 4.8 - Digital Forensics https://professormesser.link/*********"}, {"question": "B65. A system administrator is implementing a password policy that would \nrequire letters, numbers, and special characters to be included in every \npassword. Which of the following controls MUST be in place to enforce \nthis password policy?", "options": ["Length", "Expiration", "Reuse", "Complexity"], "answer": "Complexity", "explanation": "D. Complexity\nAdding different types of characters to a password requires technical \ncontrols that increase password complexity.\nThe incorrect answers:\nA. Length \nAdding all of these character types to a password do not necessarily \nchange the length of the password.\nB. Expiration\nA common password security policy is an expiration date. This password \nexpiration requires the user to periodically change their password.\nC. Reuse\nThe controls that prohibit the reuse of passwords do not control the \ncharacters used in the password.\nMore information:\nSY0-701, Objective 4.6 - Password Security https://professormesser.link/*********"}, {"question": "B66. Which of the following would a company follow to deploy a weekly \noperating system patch?", "options": ["Tabletop exercise", "Penetration testing", "Change management", "Internal audit"], "answer": "Change management", "explanation": "C. Change management\nChange management is a formal process used to control and manage any \nchanges to hardware, software, or any other part of the IT infrastructure.\nThe incorrect answers:\nA. Tabletop exercise\nA tabletop exercise is associated with disaster recovery planning. Instead \nof performing a full-scale disaster recovery test, the organization's key \ndecision makers sit at a table and describe the steps they would follow if a \ndisaster was to occur.\nB. Penetration testing\nPenetration testing is used to find vulnerabilities and other security issues. \nAlthough a penetration test might discover an unpatched system, the \nprocess of deploying the patch would be managed by the change control \nprocess.\nD. Internal audit\nInternal audits are important security validations, but an audit would not \nbe used to deploy patches to company devices.\nMore information:\nSY0-701, Objective 1.3 - Change Management Process https://professormesser.link/*********"}, {"question": "B67. Which of the following would be the MOST likely result of plaintext \napplication communication?", "options": ["Buffer overflow", "Replay attack", "Resource consumption", "Directory traversal"], "answer": "Replay attack", "explanation": "B. Replay attack\nTo perform a replay attack, the attacker needs to capture the original \nnon-encrypted content. If an application is not using encrypted \ncommunication, the data capture process is a relatively simple process for  \nthe attacker.\nThe incorrect answers:\n<PERSON><PERSON> overflow\nA buffer overflow takes advantage of an application vulnerability and can \nperform this overflow over both an encrypted or non-encrypted channel.\nC. Resource consumption\nResource consumption describes the use of network bandwidth or storage \nspace, but those resource issues don't necessarily require the network \ncommunication to be sent in the clear.\nD. Directory traversal\nDirectory traversal is commonly associated with unexpected access to the \nfile system of a server. Non-encrypted communication is not a prerequisite \nin a directory traversal attack.\nMore information:\nSY0-701, Objective 2.4 - Replay Attacks https://professormesser.link/*********"}, {"question": "B68. A system administrator believes that certain configuration files on a Linux \nserver have been modified from their original state. The administrator has \nreverted the configurations to their original state, but he would like to be \nnotified if they are changed again. Which of the following would be the \nBEST way to provide this functionality?", "options": ["HIPS", "File integrity monitoring", "Application allow list", "WAF"], "answer": "File integrity monitoring", "explanation": "B. File integrity monitoring\nFile integrity monitoring software (i.e., Tripwire, System File Checker, \netc.) can be used to alert if the contents of a file are modified.\nThe incorrect answers:\nA. HIPS \nThe use of HIPS (Host-based Intrusion Prevention System) would \nhelp identify any security vulnerabilities, but there’s nothing relating \nto this issue that would indicate it was caused by an operating system \nor application vulnerability. A HIPS would not commonly alert on the \nmodification of a specific file.\nC. Application allow list\nIn this example, we’re not sure how the file was changed or if a separate \napplication or editor was used. If the change was made with a valid \napplication, an allow list would not provide any feedback or alerts. \nD. WAF\nA WAF (Web Application Firewall) is used to protect web-based \napplications from malicious attack. The example in this question was not \nrelated to a web-based application.\nMore information:\nSY0-701, Objective 4.5 - Monitoring Data https://professormesser.link/701040506"}, {"question": "B69. A security administrator is updating the network infrastructure to support \n802.1X. Which of the following would be the BEST choice for this \nconfiguration?", "options": ["LDAP", "SIEM", "SNMP traps", "SPF"], "answer": "LDAP", "explanation": "A. LDAP\n802.1X is a standard for authentication, and LDAP (Lightweight \nDirectory Access Protocol) is a common protocol used for centralized \nauthentication. Other protocols such as RADIUS, TACACS+, or Kerberos \nwould also be options for 802.1X authentication.\nThe incorrect answers:\nB. SIEM \nA SIEM (Security Information and Event Management) system is \ndesigned to consolidate log files from multiple devices, quickly search \nthrough data, and create long-term reports. A SIEM does not provide any \nadditional functionality for the authentication process.\nC. SNMP traps\nSNMP (Simple Network Management Protocol) traps are used to provide \nalerts and alarms from servers and infrastructure devices. SNMP is not an \nauthentication protocol. \nD. SPF\nSPF (Sender Policy Framework) is an email security standard used to \nvalidate authorized mail senders. The SPF information is added to a DNS \n(Domain Name System) server and accessed by email recipients.\nMore information:\nSY0-701, Objective 4.6 - Identity and Access Management https://professormesser.link/*********"}, {"question": "B70. A company owns a time clock appliance, but the time clock doesn’t \nprovide any access to the operating system and it doesn't provide a \nmethod to upgrade the firmware. Which of the following describes this \nappliance?", "options": ["End-of-life", "ICS", "SDN", "Embedded system"], "answer": "Embedded system", "explanation": "D. Embedded system\nAn embedded system often does not provide access to the OS and may \nnot provide a method of upgrading the system firmware.\nThe incorrect answers:\nA. End-of-life \nA device at its end-of-life is no longer supported by the vendor. In this \nexample, the vendor support status isn’t mentioned.\nB. ICS\nICS (Industrial Control Systems) devices are large industrial systems and \nusually involve manufacturing equipment or power generation equipment. \nA time clock would not be categorized as an ICS.\nC. SDN\nAn SDN (Software Defined Network) is commonly used as a method of \ndeploying network components by separating a device into a data plane, \ncontrol plane, and management plane. A time clock appliance would not \nbe categorized as an SDN.\nMore information:\nSY0-701, Objective 3.1 - Other Infrastructure Concepts https://professormesser.link/*********"}, {"question": "B71. A company has deployed laptops to all employees, and each laptop is \nenumerated during each login. Which of the following is supported with \nthis configuration?", "options": ["If the laptop hardware is modified, the security team is alerted", "Any malware identified on the system is automatically deleted", "Users are required to use at least two factors of authentication", "The laptop is added to a private VLAN after the login process"], "answer": "If the laptop hardware is modified, the security team is alerted", "explanation": "A. If the laptop hardware is modified, the security team\nis alerted\nThe enumeration process identifies and reports on the hardware and \nsoftware installed on the laptop. If this configuration is changed, an alert \ncan be generated.\nThe incorrect answers:\nB. Any malware identified on the system is automatically deleted\nAlthough it's very likely the laptop is running some type of anti-malware \nsoftware, this question was regarding the enumeration process.\nC. Users are required to use at least two factors of authentication\nIt's always a good idea to support multifactor authentication, but the \nenumeration process does not support any additional authentication \nfactors.\nD. The laptop is added to a private VLAN after the login process\nMany organizations can identify the login and automatically move that \ndevice to the correct VLANs. The enumeration mentioned does not \nprovide this functionality.\nMore information:\nSY0-701, Objective 4.2 - Asset Management https://professormesser.link/*********"}, {"question": "B72. A security manager believes that an employee is using their laptop to \ncircumvent the corporate Internet security controls through the use of \na cellular hotspot. Which of the following could be used to validate this \nbelief? (doua)", "multiple answers": 2, "options": ["HIPS", "UTM logs", "Web application firewall events", "Host-based firewall logs", "Next-generation firewall logs"], "answer": ["HIPS", "Host-based firewall logs"], "explanation": "A. HIPS and D.  Host-based firewall logs\nIf the laptop is not communicating across the corporate network, then \nthe only evidence of the traffic would be contained on the laptop itself. \nA HIPS (Host-based Intrusion Prevention System) logs and host-based \nfirewall logs may contain information about recent traffic flows to systems \noutside of the corporate network.\nThe incorrect answers:\nB. UTM logs \nA unified threat management appliance is commonly located in the core of \nthe network. The use of a cellular hotspot would circumvent the UTM and \nthis traffic would not be logged.\nC. Web application firewall events\nWeb application firewalls are commonly used to protect internal web \nservers. Outbound Internet communication would not be logged, and \nanyone circumventing the existing security controls would also not be \nlogged. \nE. Next-generation firewall logs\nAlthough a next-generation firewall keeps detailed logs, any systems \ncommunicating outside of the normal corporate Internet connection \nwould not appear in those logs.\nMore information:\nSY0-701, Objective 2.5 - Hardening Techniques\nhttps://professormesser.link/*********"}, {"question": "B73. An application developer is creating a mobile device app that will require \na true random number generator real-time memory encryption. Which of \nthe following technologies would be the BEST choice for this app?", "options": ["HSM", "Secure enclave", "NGFW", "Self-signed certificates"], "answer": "Secure enclave", "explanation": "B. Secure enclave\nA secure enclave describes a hardware processor designed for security. The \nsecure enclave monitors the boot process, create true random numbers, \nstore root cryptography keys, and much more.\nThe incorrect answers:\nA. HSM\nAn HSM (Hardware Security Module) is often implemented as a high-\nend cryptographic hardware appliance. HSMs are often used as secure \nstorage for cryptographic keys.\nC. NGFW\nAn NGFW (Next Generation Firewall) is an application aware firewall \nand is commonly used to manage traffic flows. An NGFW would not be \nable to provide true random numbers or real-time memory encryption on \na device.\nD. Self-signed certificates\nA self-signed certificate is a digital certificate created on a private \nCertificate Authority and digitally signed by the private CA. A certificate \ndoes not provide randomization functions or memory encryption \ncapabilities.\nMore information:\nSY0-701, Objective 1.4 - Encryption Technologies https://professormesser.link/701010404"}, {"question": "B74. Which of the following would be a common result of a successful \nvulnerability scan?", "options": ["Usernames and password hashes from a server", "A list of missing software patches", "A copy of image files from a private file share", "The BIOS configuration of a server"], "answer": "A list of missing software patches", "explanation": "B. A list of missing software patches\nA vulnerability scan can identify vulnerabilities and list the patches \nassociated with those vulnerabilities.\nThe incorrect answers:\nA. Usernames and password hashes from a server \nThis type of secure information cannot be obtained through a  \nvulnerability scan.\nC. A copy of image files from a private file share\nA private file share would prevent any access by unauthorized users, and a \nvulnerability scan would not have access to private data. \nD. The BIOS configuration of a server\nPrivate information, such as a device’s BIOS configuration, is not available \nfrom a vulnerability scan.\nMore information:\nSY0-701, Objective 4.3 - Vulnerability Scanning https://professormesser.link/701040301"}, {"question": "B75. When connected to the wireless network, users at a remote site receive \nan IP address which is not part of the corporate address scheme. \nCommunication over this network is also slower than the wireless \nconnections elsewhere in the building. Which of the following would be \nthe MOST likely reason for these issues?", "options": ["Rogue access point", "Domain hijack", "DDoS", "Encryption is enabled"], "answer": "Rogue access point", "explanation": "A. Rogue access point\nA rogue access point is an unauthorized access point added by a user \nor attacker. This access point may not necessarily be malicious, but it \ndoes create significant security concerns and unauthorized access to the \ncorporate network.\nThe incorrect answers:\nB. Domain hijack\nA domain hijacking would be associated with unauthorized access to a \ndomain name. In this example, the wireless IP addressing and performance \nissues do not appear to be related to a domain hijack.\nC. DDoS\nA DDOS (Distributed Denial of Service) would cause outages or slow \nperformance to a service. A DDoS would not commonly modify or update \nany local IP addresses.\nD. Encryption is enabled\nWireless encryption protocols are relatively efficient and do not contribute \nto a significant delay of network traffic. An encrypted wireless network \nwould also not assign IP addresses outside of the expected range.\nMore information:\nSY0-701, Objective 2.2 - Common Threat Vectors https://professormesser.link/*********"}, {"question": "B76. A company has identified a compromised server, and the security team \nwould like to know if an attacker has used this device to move between \nsystems. Which of the following would be the BEST way to provide this \ninformation?", "options": ["DNS server logs", "Penetration test", "NetFlow logs", "Email metadata"], "answer": "NetFlow logs", "explanation": "C. NetFlow logs\nNetFlow information can provide a summary of network traffic, \napplication usage, and details of network conversations. The NetFlow logs \nwill show all conversations from this device to any others in the network.\nThe incorrect answers:\nA. DNS server logs\nDNS server logs will document all name resolutions, but an attacker may \nnot be using a DNS server and may prefer accessing devices by IP address.\nB. Penetration test\nA penetration test may identify any vulnerabilities that exist on the server, \nbut it won't provide any information about traffic flows or connections \ninitiated by an attacker.\nD. Email metadata\nAn email header contains the IP addresses of email servers used to transfer \nthe message, and security signatures to verify the sender. The metadata in \nan email header would not contain information on traffic flows associated \nwith this attacker.\nMore information:\nSY0-701, Objective 4.4 - Security Tools https://professormesser.link/*********"}, {"question": "B77. A system administrator has protected a set of system backups with \nan encryption key. The system administrator used the same key when \nrestoring files from this backup. Which of the following would BEST \ndescribe this encryption type?", "options": ["Asymmetric", "Key escrow", "Symmetric", "Out-of-band key exchange"], "answer": "Symmetric", "explanation": "C. Symmetric\nSymmetric encryption uses the same key for both encryption and \ndecryption.\nThe incorrect answers:\nA. Asymmetric \nAsymmetric encryption uses different keys for encryption and decryption.\nB. Key escrow\nKey escrow describes a third-party which holds the decryption keys for \nyour data. \nD. Out-of-band key exchange\nKeys can be transferred between people or systems over the network (in-\nband) or outside the normal network communication (out-of-band). In \nthis example, the key wasn’t exchanged between people or systems, since \nthe system administrator is the same person who encrypted and decrypted.\nMore information:\nSY0-701, Objective 1.4 - Public Key Infrastructure https://professormesser.link/*********"}, {"question": "B78. A new malware variant takes advantage of a vulnerability in a popular \nemail client. Once installed, the malware forwards all email attachments \nwith credit card information to an external email address. Which of the \nfollowing would limit the scope of this attack?", "options": ["Enable MFA on the email client", "Scan outgoing traffic with DLP", "Require users to enable the VPN when using email", "Update the list of malicious URLs in the firewall"], "answer": "Scan outgoing traffic with DLP", "explanation": "B. Scan outgoing traffic with DLP\nDLP (Data Loss Prevention) systems are designed to identify sensitive \ndata transfers. If the DLP finds a data transfer with financial details, \npersonal information, or other private information, the DLP can block the \ndata transfer.\nThe incorrect answers:\nA. Enable MFA on the email client \nMFA (Multi-Factor Authentication) can provide more security during \nthe authentication process, but the description of the malware did not \nassociate the exploit with the login process. The malware will most likely \nwait for the user to login before transferring the data.\nC. Require users to enable the VPN when using email\nA VPN (Virtual Private Network) can protect data between systems, but \nit won't prevent malware from sending data once it connects to the email \nsystem. \nD. Update the list of malicious URLs in the firewall\nBlocking known URLs (Uniform Resource Locators) in a firewall is a \nuseful way to prevent access to known malicious sites, but it won't prevent \nmalware from sending email messages.\nMore information:\nSY0-701, Objective 4.5 - Monitoring Data https://professormesser.link/701040506"}, {"question": "B79. An organization has identified a security breach and has removed the \naffected servers from the network. Which of the following is the NEXT \nstep in the incident response process?", "options": ["Eradication", "Preparation", "Recovery", "Detection", "Containment"], "answer": "Eradication", "explanation": "A. Eradication\nThe incident response process is preparation, detection, analysis, \ncontainment, eradication, recovery, and lessons learned. Once a system has \nbeen contained, any malware or breached user accounts should be removed \nfrom the system.\nThe incorrect answers:\nB. Preparation \nBefore an incident occurs, you should compile contact information, \nincident handling hardware and software, analysis resources, and other \nimportant tools and policies.\nC. Recovery\nThe focus of the recovery process is to get all of the systems back to \nnormal. This phase removes malware, deletes breached user accounts, and \nfixes any vulnerabilities. \nD. Detection\nDetection of an event can be challenging, but it usually consists of IPS \nreports, anti-virus alerts, configuration change notifications, and other \nindicators.\nE. Containment\nIn this example, the containment and isolation occurred when the affected \nservers were removed from the network.\nMore information:\nSY0-701, Objective 4.8 - Incident Response https://professormesser.link/*********"}, {"question": "B80. A security administrator has been tasked with storing and protecting \ncustomer payment and shipping information for a three-year period. \nWhich of the following would describe the source of this data?", "options": ["Controller", "Owner", "Data subject", "Processor"], "answer": "Data subject", "explanation": "C. Data subject\nIn data privacy, the data subject describes an individual with personal data. \nPayment details and shipping addresses describe personal information \nfrom a data subject.\nThe incorrect answers:\nA. Controller \nA data controller manages the processing of the data. A payroll \ndepartment would be an example of a data controller.\nB. Owner\nThe data owner is commonly accountable for all of the data, and the owner \noften manages the people and systems associated with processing and \nsecuring the data.\nD. Processor\nA data processor manages the data on behalf of the data controller. If the \ndata controller is the payroll department, a third-party payroll company \nwould be the data processor.\nMore information:\nSY0-701, Objective 5.4 - Privacy https://professormesser.link/*********"}, {"question": "B81. Which of the following would be the main reasons why a system \nadministrator would use a TPM when configuring full disk encryption? \n(doua)\n ", "multiple answers": 2, "options": ["Allows the encryption of multiple volumes", "Uses burned-in cryptographic keys", "Stores certificates in a hardware security module", "Maintains a copy of the CRL", "Includes built-in protections against brute-force attacks"], "answer": ["Uses burned-in cryptographic keys", "Includes built-in protections against brute-force attacks"], "explanation": "B. Uses burned-in cryptographic keys and  \nE. Includes built-in protections against brute-force attacks\nA TPM (Trusted Platform Module) is part of a computer’s motherboard, \nand it’s specifically designed to assist and protect with cryptographic \nfunctions. Full disk encryption (FDE) can use the burned-in TPM keys \nto verify the local device hasn’t changed, and there are security features in \nthe TPM to prevent brute-force or dictionary attacks against the full disk \nencryption login credentials.\nThe incorrect answers:\n<PERSON><PERSON> Allows the encryption of multiple volumes \nThe use of a TPM is not associated with the number of volumes encrypted \nwith FDE.\nC. Stores certificates in a hardware security module\nA hardware security module (HSM) is high-end cryptographic hardware \nspecifically designed for large-scale secured storage on the network. An \nHSM server is a separate device and is not associated with an individual \ndevice’s TPM. \nD. Maintains a copy of the CRL\nA CRL (Certificate Revocation List) is maintained by the Certificate \nAuthority and is not part of the TPM.\nMore information:\nSY0-701, Objective 3.2 - Encryption Technologies https://professormesser.link/701010404"}, {"question": "B82. A security administrator is using an access control where each file or folder is assigned a security clearance level, such as “confidential” or “secret.” The security administrator then assigns a maximum security level to each user. What type of access control is used in this network?", "options": ["Mandatory", "Rule-based", "Discretionary", "Role-based"], "answer": "Mandatory", "explanation": "A. Mandatory\nMandatory access control uses a series of security levels (i.e., public, \nprivate, secret) and assigns those levels to each object in the operating \nsystem. Users are assigned a security level, and they would only have access \nto objects that meet or are below that assigned security level.\nThe incorrect answers:\nB. Rule-based \nRule-based access control determines access based on a series of system-\nenforced rules. An access rule might require a particular browser be used \nto complete a web page form, or access to a file or system is only allowed \nduring certain times of the day.\nC. Discretionary\nDiscretionary access control allows the owner of an object to assign access. \nIf a user creates a spreadsheet, the user can then assign users and groups to \nhave a particular level of access to that spreadsheet. \nD. Role-based\nRole-based access control assigns a user’s permissions based on their role \nin the organization. For example, a manager would have a different set of \nrights and permissions than a team lead.\nMore information:\nSY0-701, Objective 4.6 - Access Controls https://professormesser.link/*********"}, {"question": "B83. A security administrator is reviewing a report showing a number of \ndevices on internal networks are connecting with servers in the data \ncenter network. Which of the following security systems should be added \nto prevent internal systems from accessing data center devices?", "options": ["VPN", "IPS", "SIEM", "ACL"], "answer": "ACL", "explanation": "D. ACL\nAn ACL (Access Control List) is a security control commonly \nimplemented on routers to allow or restrict traffic flows through the \nnetwork.\nThe incorrect answers:\nA. VPN \nA VPN (Virtual Private Network) can be used to secure data traversing \nthe network, but it’s not commonly used to control traffic flows on an \ninternal network.\nB. IPS\nAn IPS (Intrusion Prevention System) is designed to identify and block \nknown vulnerabilities traversing the network. An IPS is not used to \ncontrol other traffic flows. \nC. SIEM\nA SIEM (Security Information and Event Management) server is \ncommonly used to consolidate and report on log files. A SIEM would not \nbe able to control or limit network communication.\nMore information:\nSY0-701, Objective 2.5 - Segmentation and Access Control https://professormesser.link/*********"}, {"question": "B84. A financial services company is headquartered in an area with a high \noccurrence of tropical storms and hurricanes. Which of the following \nwould be MOST important when restoring services disabled by a storm?", "options": ["Disaster recovery plan", "Stakeholder management", "Change management", "Retention policies"], "answer": "Disaster recovery plan", "explanation": "A. Disaster recovery plan\nA disaster recovery plan is a comprehensive set of processes for large-scale \noutages that affect the organization. Natural disasters, technology failures, \nand human-created disasters would be reasons to implement a disaster \nrecovery plan.\nThe incorrect answers:\nB. Stakeholder management\nStakeholder management describes the relationship IT has with the their \ncustomers. Although stakeholder management is common for the change \ncontrol process, the priority after a major event is to start the disaster \nrecovery process.\nC. Change management\nChange management is an important process when making any type of \nplanned and expected change to the infrastructure. When a tropical storm \naffects uptime and availability, the disaster recovery plan would be the \nbetter choice.\nD. Retention policies\nRetention policies specify the type and amount of data that must be \nbacked up and stored. These policies are often self-imposed or part of a \nlarger set of rules and regulations.\nMore information:\nSY0-701, Objective 4.2 - Incident Response Planning https://professormesser.link/601040202"}, {"question": "B85. A user in the mail room has reported an overall slowdown of his shipping \nmanagement software. An anti-virus scan did not identify any issues, but \na more thorough malware scan identified a kernel driver which is not \npart of the original operating system installation. Which of the following \nmalware was installed on this system?", "options": ["Rootkit", "Logic bomb", "Bloatware", "Ransomware", "Keylogger"], "answer": "Rootkit", "explanation": "A. Rootkit\nA rootkit often modifies core system files and becomes effectively invisible \nto the rest of the operating system. The modification of system files and \nspecialized kernel-level drivers are common rootkit techniques.\nThe incorrect answers:\nB. Logic bomb \nA logic bomb waits for a predefined event, and then executes at that event \ntime. This event may be a time of day, a user event, or any other identifiable \nevent.\nC. Bloatware\nBloatware consists of apps which have been preinstalled onto new phones, \ntablets, or computers. Some of these apps can create resource contention \nfor CPU time, memory capacity, or free storage space.\nD. Ransomware\nRansomware makes itself quite visible on your system, and it usually \npresents warning messages and information on how to remove the \nransomware from the system.\nE. Keylogger\nA keylogger captures keyboard and mouse input and sends that \ninformation to another device. This usually means the keylogger has a \nvisible component in the list of processes, and the keylogger traffic can \noften be seen on the network.\nMore information:\nSY0-701, Objective 2.4 - Other Malware Types https://professormesser.link/*********"}, {"question": "B86. A virus scanner has identified a macro virus in a word processing file \nattached to an email. Which of the following information could be \nobtained from the metadata of this file?", "options": ["IPS signature name and number", "Operating system version", "Date and time when the file was created", "Alert disposition"], "answer": "Date and time when the file was created", "explanation": "C. Date and time when the file was created\nThe data and time the file was created is commonly found in the metadata \nof the document.\nThe incorrect answers:\nA. IPS signature name and number\nThe metadata is stored in the word processing file, and the IPS will not \nchange the information stored in the file or appear anywhere in the \ndocument itself.\nB. Operating system version\nWord processing files are not specific to an operating system, so it would \nnot be common to find OS information stored in the metadata of a word \nprocessing file. \nD. Alert disposition\nThe alert information created when the macro virus was discovered would \nnot be included as part of the word processing file metadata.\nMore information:\nSY0-701, Objective 4.9 - Log Data https://professormesser.link/*********"}, {"question": "B87. When a person enters a data center facility, they must check-in before \nthey are allowed to move further into the building. People who are leaving \nmust be formally checked-out before they are able to exit the building. \nWhich of the following would BEST facilitate this process?", "options": ["Access control vestibule", "Air gap", "Pressure sensors", "<PERSON><PERSON><PERSON>"], "answer": "Access control vestibule", "explanation": "A. Access control vestibule\nAn access control vestibule is commonly used to control the flow of \npeople through a particular area. Unlocking the one door of the vestibule \ncommonly restricts the other door from opening, thereby preventing \nsomeone from walking through without stopping. It’s common in large \ndata centers to have a single room as the access control vestibule where \nusers are checked in and out of the facility.\nThe incorrect answers:\nB. Air gap \nAn air gap is a security control that creates a physical separation between \ndevices or networks. An air gap is not used to manage the flow of people.\nC. Pressure sensors\nA pressure sensor detects a change in force, and they are commonly used \nfor floor and window sensors. Pressure sensors are not used for the check-\nin process at a data center.\nD. Bollards\nBollards are often used to channel people through a specific access point \nand prevent vehicles from entering the area. Bollards may help to protect \nthe outside of a data center building, but bollards are not used as access \ncontrol devices for data centers or other secure facilities.\nMore information:\nSY0-701, Objective 1.2 - Physical Security https://professormesser.link/*********"}, {"question": "B88. A security administrator has discovered an employee exfiltrating \nconfidential company information by embedding data within image files \nand emailing the images to a third-party. Which of the following would \nbest describe this activity?", "options": ["Digital signatures", "Steganography", "Salting", "Data masking"], "answer": "Steganography", "explanation": "B. Steganography\nSteganography is the process of hiding information within another \ndocument. For example, one common method of steganography embeds \ndata or documents within image files.\nThe incorrect answers:\nA. Digital signatures \nA digital signature is a cryptographic method used to check the integrity, \nauthentication, and non-repudiation of a message. Digital signatures are \nnot used to hide information within image files.\nC. Salting\nSalting adds information the hashing process to ensure a unique hash \nvalue. The salting process does not involve embedding or hiding data \nwithin other types of media.\nD. Data masking\nData masking replaces the display of sensitive information with another \nvalue. Replacing a credit card number on a receipt with a series of asterisks \nwould be an example of data masking.\nMore information:\nSY0-701, Objective 1.4 - Obfuscation https://professormesser.link/*********"}, {"question": "B89. A third-party has been contracted to perform a penetration test on a \ncompany's public web servers. The testing company has been provided \nwith the external IP addresses of the servers. Which of the following \nwould describe this scenario?", "options": ["Defensive", "Active reconnaissance", "Partially known environment", "Regulatory"], "answer": "Partially known environment", "explanation": "C. Partially known environment\nA partially known environment provides limited information about the \ntesting systems and networks during a penetration test.\nThe incorrect answers:\nA. Defensive\nA defensive approach to penetration tests usually involves the blue team \nidentifying incoming attacks in real-time.\nB. Active reconnaissance\nActive reconnaissance gathers information which could be visible on \nnetwork traffic logs and packet captures. The IP addresses of the servers \nwere provided by the client and are not part of a reconnaissance process.\nD. Regulatory\nSome organizations may be required by regulation to perform ongoing \nvulnerability scans and security tasks. There's no mention in this question \nregarding any legal or regulatory requirements.\nMore information:\nSY0-701, Objective 5.5 - Penetration Tests https://professormesser.link/*********"}, {"question": "B90. Which of the following would be the best way to describe the estimated \nnumber of laptops that might be stolen in a fiscal year?", "options": ["ALE", "SLE", "ARO", "MTTR"], "answer": "ARO", "explanation": "C. ARO\nThe ARO (Annualized Rate of Occurrence) describes the number of \ninstances estimated to occur in a year. For example, if the organization \nexpect to lose seven laptops to theft in a year, the ARO for laptop theft is \nseven.\nThe incorrect answers:\nA. ALE \nThe ALE (Annual Loss Expectancy) is the expected cost for all events in a \nsingle year. If it costs $1,000 to replace a single laptop (the SLE) and you \nexpect to lose seven laptops in a year (the ARO), the ALE for laptop theft  \nis $7,000.\nB. SLE\nSLE (Single Loss Expectancy) is the monetary loss if a single event \noccurs. If one laptop is stolen, the cost to replace that single laptop is the \nSLE, or $1,000. \nD. MTTR\nMTTR (Mean Time to Repair) is the time required to repair a product or \nsystem after a failure.\nMore information:\nSY0-701, Objective 5.2 - Risk Analysis https://professormesser.link/*********"}, {"question": "C6. A finance company is legally required to maintain seven years of tax \nrecords for all of their customers. Which of the following would be the \nBEST way to implement this requirement?", "options": ["Automate a script to remove all tax information more than seven years old", "Print and store all tax records in a seven-year cycle", "Allow users to download tax records from their account login", "Create a separate daily backup archive for all applicable tax records"], "answer": "Create a separate daily backup archive for all applicable tax records", "explanation": "<PERSON><PERSON> <PERSON>reate a separate daily backup archive for all\napplicable tax records\nAn important consideration for a data retention mandate is to always have \naccess to the information over the proposed time frame. In this example, \na daily backup would ensure tax information is constantly archived over \na seven year period and could always be retrieved if needed. If data was \ninadvertently deleted from the primary storage, the backup would still \nmaintain a copy.\nThe incorrect answers:\n<PERSON><PERSON> Automate a script to remove all tax information more than  \nseven years old \nThe requirement is to maintain data for at least seven years, but there’s no \nrequirement to remove that data after that time frame. For example, some \nfinancial information may need to be retained well beyond the seven year \nmandate.\nB. Print and store all tax records in a seven-year cycle\nPaper has its place, but creating physical output of tax records and storing \nthem for seven years would include a significant cost in time, materials, \nand inventory space. The requirement to store data for seven years doesn’t \nrequire the information to be stored in a physical form. \nC. Allow users to download tax records from their account login\nIncluding a feature to allow access to their records is useful for the user \ncommunity, but it doesn’t provide any data protection for the seven year \nretention period.\nMore information:\nSY0-701, Objective 4.2 - Asset Management https://professormesser.link/*********"}, {"question": "C7. A system administrator is designing a data center for an insurance \ncompany’s new public cloud and would like to automatically rotate \nencryption keys on a regular basis. Which of the following would provide \nthis functionality?", "options": ["TPM", "Key management system", "Secure enclave", "XDR"], "answer": "Key management system", "explanation": "B. Key management system\nA key management system is used to manage large security key \nimplementations from a central console. This includes creating keys,  \nassociating keys with individuals, rotating keys on regular intervals, and \nlogging all key use.\nThe incorrect answers:\nA. TPM\nA TPM (Trusted Platform Module) provides cryptographic features on \na hardware device. TPMs are often integrated onto the motherboard or \nsystem board of an individual device or component.\nC. Secure enclave\nA secure enclave is usually implemented as a cryptographic hardware \nprocessor and provides extensive security features for a device.\nD. XDR\nXDR (Extended Detection and Response) clients are often installed onto \nuser devices to correlate endpoint, network, and cloud data to identify \nmalicious software and attacks.\nMore information:\nSY0-701, Objective 1.4 - Encryption Technologies https://professormesser.link/701010404"}, {"question": "C8. A newly installed IPS is flagging a legitimate corporate application as \nmalicious network traffic. Which of the following would be the BEST way \nto resolve this issue?", "options": ["Disable the IPS signature", "Block the application", "Log all IPS events", "Tune the IPS alerts"], "answer": "Tune the IPS alerts", "explanation": "<PERSON><PERSON> Tune the IPS alerts\nEach signature of an IPS can commonly be tuned to properly alert on a \nlegitimate issue. Tuning the IPS can properly identify and block attacks \nand allow all legitimate traffic.\nThe incorrect answers:\nA<PERSON> Disable the IPS signature\nDisabling the IPS signature would certainly remove the alerts, but it also \nwould prevent the IPS from blocking an actual attack.\nB. Block the application\nBlocking the corporate application would be an unusual response, \nespecially when the application is legitimate and does not pose a security \nconcern.\nC. Log all IPS events\nIt's useful to log all security events, but simply logging the events doesn't \ncorrect the false positive report.\nMore information:\nSY0-701, Objective 4.4 - Security Monitoring https://professormesser.link/*********"}, {"question": "C9. A security administrator has identified an internally developed application \nwhich allows modification of SQL queries through the web-based front-\nend. Which of the following changes would resolve this vulnerability?", "options": ["Store all credentials as salted hashes", "Verify the application's digital signature", "Validate all application input", "Obfuscate the application's source code"], "answer": "Validate all application input", "explanation": "C. Validate all application input\nInput validation would examine the input from the client and make sure \nthat the input is expected and not malicious. In this example, validating \nthe input would prevent any SQL (Structured Query Language) injection \nthrough the web front-end.\nThe incorrect answers:\nA. Store all credentials as salted hashes \nAlthough credential storage should commonly be salted and hashed, \nchanging the process for storing passwords would not resolve any issues \nrelated to application input.\nB. Verify the application's digital signature\nCode that has been digitally signed by the application developer can be \nevaluated to ensure that nothing has changed with the application code \nsince it was published, but it would not prevent any type of code injection.\nD. Obfuscate the application's source code\nObfuscation makes something normally understandable very difficult to \nunderstand. Obfuscating the source code of the application would make it \nmuch more difficult to read, but it wouldn't prevent a SQL injection.\nMore information:\nSY0-701, Objective 4.1 - Application Security https://professormesser.link/701040105"}, {"question": "C10. A system administrator is implementing a fingerprint scanner to \nprovide access to the data center. Which of the following authentication \ntechnologies would be associated with this access?", "options": ["Digital signature", "Hard authentication token", "Security key", "Something you are"], "answer": "Something you are", "explanation": "D. Something you are\nAn authentication factor of \"something you are\" often refers to a physical \ncharacteristic. This factor commonly uses fingerprints, facial recognition, or \nsome other biometric characteristic to match a user to an authentication \nattempt.\nThe incorrect answers:\nA. Digital signature\nA digital signature is a cryptographic method used to verify the source and \ncontents of data. Adding a fingerprint scanner would not provide a digital \nsignature.\nB. Hard authentication token\nMany software-based authentication tokens are available for our mobile \nphones and tablets, but there are also stand-alone hard authentication \ntokens that are often attached to a keyring or lanyard. \nC. Security key\nA USB (Universal Serial Bus) security key commonly stores a digital \nsignature for authentication or user verification. A USB key is not \ncommonly part of a fingerprint scanner.\nMore information:\nSY0-701, Objective 4.6 - Multi-factor Authentication https://professormesser.link/*********"}, {"question": "C11. The IT department of a transportation company maintains an on-site \ninventory of chassis-based network switch interface cards. If a failure \noccurs, the on-site technician can replace the interface card and have the \nsystem running again in sixty minutes. Which of the following BEST \ndescribes this recovery metric?", "options": ["MTBF", "MTTR", "RPO", "RTO"], "answer": "MTTR", "explanation": "B. MTTR\nMTTR (Mean Time To Restore) is the amount of time required to get \nback up and running. This is sometimes called Mean Time To Repair.\nThe incorrect answers:\nA. MTBF \nMTBF (Mean Time Between Failures) is a prediction of how long the \nsystem will be operational before a failure occurs.\nC. RPO\nAn RPO (Recovery Point Objective) is a qualifier that determines when \nthe system is recovered. A recovered system may not be completely \nrepaired, but it will be running well enough to maintain a certain level of \noperation. \nD. RTO\nAn RTO (Recovery Time Objective) is the service level goal to work \ntowards when recovering a system and getting back up and running.\nMore information:\nSY0-701, Objective 5.2 - Business Impact Analysis https://professormesser.link/*********"}, {"question": "C12. A company maintains a server farm in a large data center. These servers \nare used internally and are not accessible from outside of the data center. \nThe security team has discovered a group of servers was breached before \nthe latest security patches were applied. Breach attempts were not logged \non any other servers. Which of these threat actors would be MOST likely \ninvolved in this breach?", "options": ["Organized crime", "Insider", "Nation state", "Unskilled attacker"], "answer": "Insider", "explanation": "B. Insider\nNone of these servers are accessible from the outside, and the only \nservers with any logged connections were also susceptible to the latest \nvulnerabilities. To complete this attack, there would need a very specific \nknowledge of the vulnerable systems and a way to communicate with \nthose servers.\nThe incorrect answers:\nA. Organized crime\nOrganized crime can be very effective at hacking systems and companies, \nbut they can only affect systems where they have access. Internal servers \nwould not be accessible to anyone on the outside of the organization.\nC. Nation state\nA nation state would have the resources needed to attack a network, gain \naccess to the internal systems, and then somehow monitor the update \nprocesses for each server. However, the scope and breadth of such an attack \nwould be complex, and this would make the nation state a very speculative \noption and not the most likely choice from the available list. \nD. Unskilled attacker\nUnskilled attackers don’t usually have access to an internal network, and \nthey generally aren’t knowledgeable enough to track the status of which \nsystems may have been recently updated.\nMore information:\nSY0-701, Objective 2.1 - Threat Actors https://professormesser.link/*********"}, {"question": "C13. An organization has received a vulnerability scan report of their Internet-\nfacing web servers. The report shows the servers have multiple Sun Java \nRuntime Environment ( JRE) vulnerabilities, but the server administrator \nhas verified that JRE is not installed. Which of the following would be \nthe BEST way to handle this report?", "options": ["Install the latest version of JRE on the server", "Quarantine the server and scan for malware", "Harden the operating system of the web server", "Ignore the JRE vulnerability alert"], "answer": "Ignore the JRE vulnerability alert", "explanation": "<PERSON><PERSON> Ignore the JRE vulnerability alert\nIt’s relatively common for vulnerability scans to show vulnerabilities that \ndon’t actually exist, especially if the scans are not credentialed. An issue \nthat is identified but does not actually exist is a false positive, and it can be \ndismissed once the alert has been properly researched.\nThe incorrect answers:\n<PERSON><PERSON> the latest version of JRE on the server \nThe system administrator verified that JRE was not currently installed \non the server, so it would not be possible for that vulnerability to actually \nexist. Installing an unneeded version of JRE on the server could potentially \nopen the server to actual vulnerabilities.\nB. Quarantine the server and scan for malware\nThe JRE false positive isn’t an indication of malware, and there's no \nmention of any additional vulnerabilities or reports of malware. \nC. Harden the operating system of the web server\nAlthough it’s always a good best practice to harden the operating system \nof an externally-facing server, this vulnerability scan report doesn’t indicate \nany particular vulnerability with the operating system itself. If the scan \nidentified specific OS vulnerabilities, then additional hardening may be \nrequired.\nMore information:\nSY0-701, Objective 4.3 - Analyzing Vulnerabilities https://professormesser.link/*********"}, {"question": "C14. A user downloaded and installed a utility for compressing and \ndecompressing files. Immediately after installing the utility, the user’s \noverall workstation performance degraded and it now takes twice as much \ntime to perform any tasks on the computer. Which of the following is the \nBEST description of this malware infection?", "options": ["Ransomware", "Bloatware", "Logic bomb", "Trojan"], "answer": "Trojan", "explanation": "D. Trojan\nA Trojan horse is malicious software that pretends to be something \nbenign. The user will install the software with the expectation that it will \nperform a particular function, but in reality it is installing malware on the \ncomputer.\nThe incorrect answers:\nA. Ransomware \nRansomware will lock a system and present a message to the user with \ninstructions on how to unlock the system. This usually involves sending \nthe attacker money in exchange for the unlock key.\nB. Bloatware\nBloatware is delivered as numerous and often unnecessary applications \nwhich have been pre-installed to a system. \nC. Logic bomb\nA logic bomb will execute when a certain event occurs, such as a specific \ndate and time.\nMore information:\nSY0-701, Objective 2.4 - An Overview of Malware https://professormesser.link/*********"}, {"question": "C15. Which of the following is the process for replacing sensitive data with a \nnon-sensitive and functional placeholder?", "options": ["Steganography", "Tokenization", "Retention", "Masking"], "answer": "Tokenization", "explanation": "B. Tokenization\nTokenization replaces sensitive data with a token, and this token can be \nused as a functional placeholder for the original data. Tokenization is \ncommonly used with credit card processing and mobile devices.\nThe incorrect answers:\nA. Steganography\nSteganography is a method of hiding data within another media type. For \nexample, one common type of steganography is hiding information within \na digital image.\nC. Retention\nData retention specifies the amount of time that data should be stored or \nsaved. Retention policies do not commonly replace sensitive data.\nD. Masking\nData masking hides some of the original data to protect it from view. \nWhile hidden, this data cannot be used for any functional purpose.\nMore information:\nSY0-701, Objective 3.3 - Protecting Data https://professormesser.link/*********"}, {"question": "C16. A security administrator has installed a new firewall to protect a web \nserver VLAN. The application owner requires all web server sessions \ncommunicate over an encrypted channel. Which rule should the security \nadministrator add to the firewall rulebase?", "options": ["Source: ANY, Destination: ANY, Protocol: TCP, Port: 23, <PERSON>y", "Source: ANY, Destination: ANY, Protocol: TCP, Port: 22, Allow", "Source: ANY, Destination: ANY, Protocol: TCP, Port: 80, Allow", "Source: ANY, Destination: ANY, Protocol: TCP, Port: 443, Allow"], "answer": "Source: ANY, Destination: ANY, Protocol: TCP, Port: 443, Allow", "explanation": "D. Source: ANY, Destination: ANY, Protocol: TCP, Port: 443, Allow\nMost web servers use tcp/443 for HTTPS (Hypertext Transfer Protocol \nSecure) for encrypted web server communication This rule allows HTTPS \nencrypted traffic to be forwarded to the web server over tcp/443.\nThe incorrect answers:\nA. Source: ANY, Destination: ANY, Protocol: TCP, Port: 23, Deny  \nThe insecure Telnet protocol commonly uses tcp/23, but most web servers \nwould not be listening on tcp/23. An explicit tcp/23 deny rule would not \nprovide any additional web server security.\nB. Source: ANY, Destination: ANY, Protocol: TCP, Port: 22, Allow\nThe SSH (Secure Shell) protocol uses tcp/22 to provide encrypted \nterminal communication, but the web server does not use SSH when \ncommunicating with client web browsers. \nE. Source: ANY, Destination: ANY, Protocol: TCP, Port: 80, Allow\nUnencrypted web communication commonly uses tcp/80. Since the \napplication owner requires encrypted communication, allowing HTTP \nover tcp/80 should not be allowed through the firewall.\nMore information:\nSY0-701, Objective 4.5 - Firewalls https://professormesser.link/701040501"}, {"question": "C17. Which of these would be used to provide multi-factor authentication?", "options": ["USB-connected storage drive with FDE", "Employee policy manual", "Just-in-time permissions", "Smart card with picture ID"], "answer": "Smart card with picture ID", "explanation": "D. Smart card with picture ID\nA smart card commonly includes a certificate that can be used as a \nmultifactor authentication of something you have. These smart cards are \ncommonly combined with an employee identification card, and often \nrequire a separate PIN (Personal Identification Number) as an additional \nauthentication factor.\nThe incorrect answers:\nA. USB-connected storage drive with FDE \nFDE (Full Disk Encryption) will protect the data on a drive, but it doesn’t \nprovide a factor of authentication.\nB. Employee policy manual\nEmployee policy manuals aren’t commonly associated with a specific \nindividual, so they are not a good factor of authentication. \nC. Just-in-time permissions\nJust-in-time permissions provide a method of distributing login \ncredentials on a temporary or as-needed basis. Just-in-time permissions \nmay or may not include any type of multi-factor authentication.\nMore information:\nSY0-701, Objective 4.6 - Multi-Factor Authentication https://professormesser.link/*********"}, {"question": "C18. A company's network team has been asked to build an IPsec tunnel to a \nnew business partner. Which of the following security risks would be the \nMOST important to consider?", "options": ["Supply chain attack", "Unsupported systems", "Business email compromise", "Typosquatting"], "answer": "Supply chain attack", "explanation": "A. Supply chain attack\nA direct connection to a third-party creates potential access for an attacker.  \nMost organizations will include a firewall to help monitor and protect \nagainst any supply chain attacks.\nThe incorrect answers:\nB. Unsupported systems\nAlthough unsupported systems can be a significant security concern, this \nquestion did not document any issues with outdated or legacy devices.\nC. Business email compromise\nBusiness email compromise uses an organization's existing email addresses \nas an attack destination. A business email compromise does not require an \nIPsec tunnel to a partner location.\nD. Typosquatting\nTyposquatting uses misspelled domain names in an effort to mislead \na victim. This often takes the form of phishing emails or unauthorized \nwebsite links. There are no domain name spelling issues associated with \nthis new IPsec connection.\nMore information:\nSY0-701, Objective 2.2 - Common Threat Vectors https://professormesser.link/*********"}, {"question": "C19. A company's human resources team maintains a list of all employees \nparticipating in the corporate savings plan. A third-party financial \ncompany uses this information to manage stock investments for the \nemployees. Which of the following would describe this financial \ncompany?", "options": ["Processor", "Owner", "Controller", "<PERSON><PERSON><PERSON><PERSON>"], "answer": "Processor", "explanation": "A. Processor\nA data processor performs some type of action to the data, and this is \noften a different group within the organization or a third-party company. \nIn this example, the third-party financial organization is the data processor \nof the employee's financial data.\nThe incorrect answers:\nB. Owner\nThe data owner is often an executive of the company and is ultimately \nresponsible for the use and security of this data.\nC. Controller\nA data controller manages the data. In this example, the human resources \nteam would control the access and use of the employee data.\nD. Custodian\nA data custodian is responsible for the accuracy, privacy, and security of \nthe data. Many organizations will hire data custodians to ensure all data is \nproperly protected and maintained.\nMore information:\nSY0-701, Objective 5.1 - Data Roles and Responsibilities https://professormesser.link/*********"}, {"question": "C20. A technology company is manufacturing a military-grade radar tracking \nsystem designed to identify any nearby unmanned aerial vehicles (UAVs). \nThe UAV detector must be able to instantly identify and react to a vehicle \nwithout delay. Which of the following would BEST describe this tracking \nsystem?", "options": ["RTOS", "IoT", "ICS", "SDN"], "answer": "RTOS", "explanation": "A. RTOS\nThis tracking system requires an RTOS (Real-Time Operating System) \nto instantly react to input without any significant delays or queuing in \nthe operating system. Operating systems used by the military, automobile \nmanufacturers, and industrial equipment companies often use RTOS to \nprocess certain transactions without any significant delays.\nThe incorrect answers:\nB. IoT \nIoT (Internet of Things) devices are generally associated with home \nautomation and do not have a requirement for real-time operation.\nC. ICS\nAn ICS (Industrial Control System) is often a dedicated network used \nexclusively to manage and control manufacturing equipment, power \ngeneration equipment, water management systems, and other industrial \nmachines. Although some industrial control systems may use an RTOS, \nusing a real-time operating system is not a requirement of an ICS. \nD. SDN\nAn SDN (Software Defined Network) splits the functions of a network \ndevice into separate planes of operation. These logical units extend the \nfunctionality and management of a single device and provide a method of \neasily deploying devices in the cloud.\nMore information:\nSY0-701, Objective 3.1 - Other Infrastructure Concepts https://professormesser.link/*********"}, {"question": "C21. An administrator is writing a script to convert an email message to a help \ndesk ticket and assign the ticket to the correct department. Which of the \nfollowing should be administrator use to complete this script?", "options": ["Role-based access controls", "Federation", "Due diligence", "Orchestration"], "answer": "Orchestration", "explanation": "D. Orchestration\nOrchestration describes the process of automation, and is commonly \nassociated with large scale automation or automating processes between \ndifferent systems.\nThe incorrect answers:\nA. Role-based access controls\nRole-based access control is used to associate a job function with a set of \nrights and permissions. The scripting described in this question does not \nspecifically require any role-based access controls.\nB. Federation\nFederation commonly describes the process of authenticating to one \nsystem using the credentials associated with another system. The scripting \nprocess in this question would not require federation.\nC. Due diligence\nDue diligence usually involves the investigation performed on a third party \nprior to doing business. An internal help desk script would not require any \ndue diligence.\nMore information:\nSY0-701, Objective 4.7 - Scripting and Automation\nhttp://professormesser.link/*********313"}, {"question": "C22. A security administrator would like a report showing how many attackers \nare attempting to use a known vulnerability to gain access to a corporate \nweb server. Which of the following should be used to gather this \ninformation?\n", "options": ["Application log", "<PERSON><PERSON><PERSON>", "IPS log", "Windows log"], "answer": "IPS log", "explanation": "C. IPS log\nAn IPS (Intrusion Prevention System) commonly uses a database of \nknown vulnerabilities to identify and block malicious network traffic. This \nlog of attempted exploits would provide the required report information.\nThe incorrect answers:\nA. Application log\nAn application log provides a summary of internal application functions \nand procedures. An application log would not commonly identify and log \nsecurity events.\nB. Metadata\nMetadata is a summary of information attached to a file or document. \nMetadata does not commonly store security events and would not be a \nvalid source for this reporting data.\nD. Windows log\nThe Windows Event Viewer shows the logs for the applications, security, \nand other aspects of the Windows operating system. An operating system \nlog would not commonly gather information on network-based attacks.\nMore information:\nSY0-701, Objective 4.9 - Log Data https://professormesser.link/*********"}, {"question": "C23. During a ransomware outbreak, an organization was forced to rebuild \ndatabase servers from known good backup systems. In which of the \nfollowing incident response phases were these database servers brought \nback online?", "options": ["Recovery", "Lessons learned", "Containment", "Detection"], "answer": "Recovery", "explanation": "A. Recovery\nThe recovery phase focuses on getting things back to normal after an \nattack. This is the phase that removes malware, fixes vulnerabilities, and \nrecovers the damaged systems.\nThe incorrect answers:\n<PERSON><PERSON> learned \nOnce an event is over, it’s useful to have a post-incident meeting to discuss \nthe things that worked and things that didn’t.\nC. Containment\nWhen an event occurs, it’s important to minimize the impact. Isolation \nand containment can help to limit the spread and effect of an event. \nD. Detection\nDetecting and identifying the event is an important step that initiates the \nrest of the incident response processes.\nMore information:\nSY0-701, Objective 4.8 - Incident Response https://professormesser.link/*********"}, {"question": "C24. A security administrator is installing a web server with a newly built \noperating system. Which of the following would be the best way to \nharden this OS?", "options": ["Create a backup schedule", "Install a device certificate", "Remove unnecessary software", "Disable power management features"], "answer": "Remove unnecessary software", "explanation": "C. Remove unnecessary software\nThe process of hardening an operating system makes it more difficult to \nattack. In this example, the only step that would limit the attack surface is \nto remove any unnecessary or unused software.\nThe incorrect answers:\nA. Create a backup schedule\nAlthough a backup schedule is an important security task, the process \nof performing backups doesn't make the system any more resistant to a \npotential attack.\nB. Install a device certificate\nA device certificate can be used to verify the ownership of a remote \nsystem. However, installing a device certificate does not make the remote \nsystem more resistant to an attack.\nD. Disable power management features\nDisabling the power management features of an operating system does not \ngenerally have any impact on the overall security of the system.\nMore information:\nSY0-701, Objective 2.5 - Hardening Techniques https://professormesser.link/*********"}, {"question": "C25. A network IPS has created this log entry:\nFrame 4: 937 bytes on wire (7496 bits), 937 bytes captured\nEthernet II, Src: HewlettP_82:d8:31, Dst: Cisco_a1:b0:d1\nInternet Protocol Version 4, Src: ***********, Dst: ************\nTransmission Control Protocol, Src Port: 3863, Dst Port: 1433\nApplication Data: SELECT * FROM users WHERE username='x'  \nor 'x'='x' AND password='x' or 'x'='x'  \n \nWhich of the following would describe this log entry?", "options": ["<PERSON><PERSON>", "Brute force", "SQL injection", "Cross-site scripting"], "answer": "SQL injection", "explanation": "C. SQL injection\nThe SQL injection is contained in the application data. The attacker was \nattempting to circumvent the authentication through the use of equivalent \nSQL statements ('x'='x').\nThe incorrect answers:\n<PERSON><PERSON> attempts use social engineering to gain access to private or \nsensitive information. This example does not appear to contain any private \ninformation.\nB. Brute force\nA brute force attempt would include many failed attempts at a password. \nThis log does not appear to have any repeated password attempts. \nD. Cross-site scripting\nCross-site scripting takes advantage of the trust a user has for a site. This \nexample does not appear to take advantage of any previous authentication \nor trust.\nMore information:\nSY0-701, Objective 2.3 - SQL Injection https://professormesser.link/*********"}, {"question": "C26. An incident response team would like to validate their disaster recovery \nplans without making any changes to the infrastructure. Which of the \nfollowing would be the best course of action?", "options": ["Tabletop exercise", "Hot site fail-over", "Simulation", "Penetration test"], "answer": "Tabletop exercise", "explanation": "A. Tabletop exercise\nA tabletop exercise is a walk-through exercise where the disaster recovery \nprocess can be discussed in a conference room without making any \nchanges to the existing systems.\nThe incorrect answers:\nB. Hot site fail-over\nA fail-over to a hot site would involve significant changes to the \ninfrastructure, services, and operations teams.\nC. Simulation\nA simulation is a useful test of disaster recovery processes, but it often \nrequires a change to the existing systems to properly test the simulated \ndisaster.\nD. Penetration test\nA penetration test will identify vulnerabilities, but it will not provide any \nevaluation of the disaster recovery process or policies.\nMore information:\nSY0-701, Objective 3.4 - Recovery Testing https://professormesser.link/701030403"}, {"question": "C27. A system administrator has installed a new firewall between the corporate \nuser network and the data center network. When the firewall is turned on \nwith the default settings, users complain the application in the data center \nis no longer working. Which of the following would be the BEST way to \ncorrect this application issue?", "options": ["Create a single firewall rule with an explicit deny", "Build a separate VLAN for the application", "Create firewall rules that match the application traffic flow", "Enable firewall threat blocking"], "answer": "Create firewall rules that match the application traffic flow", "explanation": "C. Create firewall rules that match the application\ntraffic flow\nBy default, most firewalls implicitly deny all traffic. Firewall rules must be \nbuilt to match the traffic flows, and only then will traffic pass through the \nfirewall.\nThe incorrect answers:\nA. Create a single firewall rule with an explicit deny \nBy default, most firewalls have an implicit deny as the last policy in the \nfirewall rules. If traffic does not match any other firewall rule, then the \nimplicit deny drops the traffic. Manually configuring an explicit deny \ndoesn’t provide any additional traffic control because of the already-\nexisting implicit deny, and it doesn’t allow any traffic to pass through the \nfirewall because the rule itself denies all traffic.\nB. Build a separate VLAN for the application\nVLAN (Virtual Local Area Network) separation can be used to manage \ntraffic flows or provide additional security options, but creating a VLAN \nwon’t bypass an existing firewall deny rule. \nD. Enable firewall threat blocking\nMany next-generation firewalls can identify and block malicious network \ntraffic in real-time. However, enabling this feature would not resolve any \nexisting communication issues between the user network and the data \ncenter network.\nMore information:\nSY0-701, Objective 4.5 - Firewalls https://professormesser.link/701040501"}, {"question": "C28. Which of these would be used to provide HA for a web-based  \ndatabase application?", "options": ["SIEM", "UPS", "DLP", "VPN concentrator"], "answer": "UPS", "explanation": "B. UPS\nHA (High Availability) means the service should always be on \nand available. The only device on this list providing HA is the UPS \n(Uninterruptible Power Supply). If power is lost, the UPS will provide \nelectricity using battery power or a gas-powered generator.\nThe incorrect answers:\nA. SIEM\nA SIEM (Security Information and Event Management) system \nconsolidates data from devices on the network and provides log searching \nand reporting features. A SIEM does not provide any HA functionality.\nC. DLP\nDLP (Data Loss Prevention) is a method of identifying and preventing \nthe transfer of personal or confidential information through the network. \nDLP does not provide any HA functionality. \nD. VPN concentrator\nA VPN (Virtual Private Network) concentrator is used as an endpoint to \nan endpoint VPN solution. VPN concentrators do not provide any HA \nfunctionality.\nMore information:\nSY0-701, Objective 3.4 - Power Resiliency https://professormesser.link/*********"}, {"question": "C29. Each year, a certain number of laptops are lost or stolen and must be \nreplaced by the company. Which of the following would describe the total \ncost the company spends each year on laptop replacements?", "options": ["SLE", "SLA", "ALE", "ARO"], "answer": "ALE", "explanation": "C. ALE\nThe ALE (Annual Loss Expectancy) is the total amount of the financial \nloss over an entire year.\nThe incorrect answers:\nA. SLE \nSLE (Single Loss Expectancy) describes the loss for a single incident.\nB. SLA\nSLA (Service Level Agreement) is a contractual agreement that specifies a \nminimum service level. \nD. ARO\nAn ARO (Annualized Rate of Occurrence) is the number of times an \nevent is expected to occur in a year.\nMore information:\nSY0-701, Objective 5.2 - Risk Analysis https://professormesser.link/*********"}, {"question": "C30. A network administrator is viewing a log file from a web server:  \n \nhttps://www.example.com/?s=/Index/think/\napp/invokefunction&function=call_user_func_\narray&vars[0]=md5&vars[1][0]=__HelloThinkPHP  \n \nWhich of the following would be the BEST way to prevent this attack?", "options": ["Static code analyzer", "Input validation", "Allow list", "Secure cookies"], "answer": "Input validation", "explanation": "B. Input validation\nIn this example, the attacker is attempting to use a remote code execution \nexploit. Input validation can be used to create a very specific filter of \nallowed input, and a strict validation process would have prevented the \nweb server from processing this attack information. \nThe incorrect answers:\nA. Static code analyzer\nA static code analyzer is useful when evaluating the security of existing \nsource code. In this example, the input is dynamic and is initiated  \nby the attacker.\nC. Allow list\nAn allow list would limit user access to an application, but it would not \nlimit the type of input from the users.\nD. Secure cookies\nSecure cookies ensure the information contained in the browser cookie \nis encrypted and only viewable by the end user. Secure cookies would not \nprevent a remote code execution attack.\nMore information:\nSY0-701, Objective 4.1 - Application Security https://professormesser.link/701040105"}, {"question": "C31. <PERSON> would like to send an email to <PERSON> and have <PERSON> verify that <PERSON> \nwas the sender of the email. Which of these should <PERSON> use to provide \nthis verification?", "options": ["Digitally sign with <PERSON>’s private key", "Digitally sign with <PERSON>’s public key", "Digitally sign with <PERSON>’s private key", "Digitally sign with <PERSON>’s public key"], "answer": "Digitally sign with <PERSON>’s private key", "explanation": "<PERSON><PERSON> Digitally sign with <PERSON>’s private key\nThe sender of a message digitally signs with their own private key to \nensure integrity, authentication, and non-repudiation of the signed \ncontents. The digital signature is validated with the sender’s public key.\nThe incorrect answers:\n<PERSON><PERSON> Digitally sign with <PERSON>’s public key \nSince everyone effectively has access to all public keys, adding a digital \nsignature with a publicly available key doesn’t provide any security features.\n<PERSON><PERSON> Digitally sign with <PERSON>’s private key\n<PERSON>’s private key would only be available to <PERSON>, so <PERSON> could not \npossibly use <PERSON>’s private key when performing any cryptographic \nfunctions. \n<PERSON><PERSON> Digitally sign with <PERSON>’s public key\nSince <PERSON>'s public key would be available to anyone, using <PERSON>’s public \nkey for a digital signature would not provide any security features.\nMore information:\nSY0-701, Objective 1.4 - Hashing and Digital Signatures https://professormesser.link/*********"}, {"question": "C32. The contract of a long-term temporary employee is ending. Which of these would be the MOST important part of the off-boarding process?", "options": ["Perform an on-demand audit of the user’s privileges", "Archive the decryption keys associated with the user account", "Document the user’s outstanding tasks", "Obtain a signed copy of the Acceptable Use Policies"], "answer": "Archive the decryption keys associated with the user account", "explanation": "B. Archive the decryption keys associated with the\nuser account\nWithout the decryption keys, it will be impossible to access any of the \nuser’s protected files once they leave the company. Given the other possible \nanswers, this one is the only one that would result in unrecoverable data \nloss if not properly followed.\nThe incorrect answers:\nA<PERSON> Perform an on-demand audit of the user’s privileges \nThe user’s account will be disabled once they leave the organization, so an \naudit of their privileges would not be very useful.\nC. Document the user’s outstanding tasks\nCreating documentation is important, but it’s not as important as retaining \nthe user’s data with the decryption keys. \nD. Obtain a signed copy of the Acceptable Use Policies\nAcceptable Use Policies (AUPs) are usually signed during the on-boarding \nprocess. You won’t need an AUP if the user is no longer accessing the \nnetwork.\nMore information:\nSY0-701, Objective 5.1 - Security Procedures https://professormesser.link/*********"}, {"question": "C33. A cybersecurity analyst has been asked to respond to a denial of service \nattack against a web server, and the analyst has collected the log files and \ndata from the server. Which of the following would allow a future analyst \nto verify the data as original and unaltered?", "options": ["E-discovery", "Root cause analysis", "Legal hold", "Data hashing"], "answer": "Data hashing", "explanation": "D. Data hashing\nData hashing creates a unique message digest based on stored data. If the \ndata is tampered with, a hash taken after the change will differ from the \noriginal value. This allows the forensic engineer to identify if information \nhas been changed. .\nThe incorrect answers:\nA. E-discovery\nE-Discovery (Electronic Discovery) describes the collection, preparation, \nreview, interpretation, and production of electronic documents. Collecting \ninformation through e-discovery does not guarantee the integrity of the \ndata.\nB. Root cause analysis\nA root cause analysis examines the evidence and determines the reason \nfor the breach or attack. Performing a root cause analysis can help prevent \nfuture attacks, but it would not ensure the integrity of the collected data.\nC. Legal hold\nA legal hold is a legal technique to preserve relevant information. This \nlegal hold will ensure the data remains accessible for any legal preparation \nthat needs to occur prior to litigation.\nMore information:\nSY0-701, Objective 4.8 - Digital Forensics https://professormesser.link/*********"}, {"question": "C34. A security administrator is reviewing authentication logs. The logs show \na large number of accounts with at least three failed authentication \nattempts during the previous week. Which of the following would BEST \nexplain this report data?", "options": ["Downgrade attack", "<PERSON><PERSON>", "Injection", "Spraying"], "answer": "Spraying", "explanation": "D. Spraying\nA spraying attack attempts to discover login credentials using a small \nnumber of authentication attempts. If the password isn't discovered in \nthose few attempts, the brute force process stops before any account \nlockouts occur. An attacker could potentially perform a spraying attack \nacross many accounts without any noticeable alerts or alarms.\nThe incorrect answers:\nA. Downgrade attack\nA downgrade attack takes advantage of a cryptographic weakness or \nvulnerability to gain access. This weakness is often due to an unpatched \napplication or a poorly implemented cryptographic process. In this \nexample, the attack is focused on a small number of brute force attempts \nand not on a cryptographic issue.\nB. Phishing\nPhishing is a useful attack for gathering information. Since a phishing \nattack often gathers valid authentication details, it's not necessary for the \nphishing process to also perform a brute force attack.\nC. Injection\nAn injection attack adds additional information to a data stream in an \nattempt to access systems or data which would normally not be accessible. \nAn injection attack does not generally perform a brute force attack.\nMore information:\nSY0-701, Objective 2.4 - Password Attacks https://professormesser.link/*********"}, {"question": "C35. A security administrator has been asked to block all browsing to casino \ngaming websites. Which of the following would be the BEST way to \nimplement this requirement?", "options": ["Tune the IPS signatures", "Block port tcp/443 on the firewall", "Configure 802.1X for web browsing", "Add a content filter rule"], "answer": "Add a content filter rule", "explanation": "D. Add a content filter rule\nWeb filters contain a large database of categorized website addresses, and \nthis allows an administrator to create rules to block browsing attempts to \nspecific content. For example, a content filter may allow browsing to news \nand business sites, but block browsing attempts to gaming and shopping \nsites.\nThe incorrect answers:\n<PERSON><PERSON> <PERSON>ne the IPS signatures \nAn IPS (Intrusion Prevention System) can identify and block known \nexploits within network traffic. An IPS does not maintain a categorized \nlist of websites, and tuning the IPS signatures would not block specific \nwebsite categories.\nB. Block port tcp/443 on the firewall\nBlocking a single port would not provide filtering on a specific website \ncategory. In this example, blocking all tcp/443 traffic would effectively \nblock all web browsing traffic to secure sites.\nC. Configure 802.1X for web browsing\n802.1X is an authentication protocol often used with network \naccess control. The 802.1X protocol does not provide any filtering or \ncategorization of website traffic.\nMore information:\nSY0-701, Objective 4.5 - Web Filtering https://professormesser.link/*********"}, {"question": "C36. A company is experiencing downtime and outages when application \npatches and updates are deployed during the week. Which of the \nfollowing would help to resolve these issues?", "options": ["Onboarding considerations", "Incident response policies", "Change management procedures", "Decentralized governance"], "answer": "Change management procedures", "explanation": "C. Change management procedures\nChange management defines a series of best practices for implementing \nchanges in a complex technical environment. The goals of change \nmanagement are to implement updates and changes while also \nmaintaining the uptime and availability of critical business systems.\nThe incorrect answers:\nA. Onboarding considerations \nThe onboarding process occurs when new employees join the organization. \nA change to the onboarding process would not correct the outages created \nby patches and updates.\nB. Incident response policies\nAn operating system update or application patch is not categorized as a \nsecurity incident, so updating or modifying an incident response process \nwould not have any effect on system availability.\nD. Decentralized governance\nIn this example, the governance of a system does not appear to be effective \nin managing changes, and decentralizing the governance would not \ncommonly provide any resolution for unmanaged changes.\nMore information:\nSY0-701, Objective 1.3 - Change Management Process https://professormesser.link/*********"}, {"question": "C37. A company is implementing a series of steps to follow when responding \nto a security event. Which of the following would provide this set of \nprocesses and procedures?", "options": ["MDM", "DLP", "Playbook", "Zero trust"], "answer": "Playbook", "explanation": "C. Playbook\nA playbook provides a conditional set of steps to follow when addressing \na specific event. An organization might have separate playbooks for \ninvestigating a data breach, responding to a virus infection, or recovering \nfrom a ransomware attack.\nThe incorrect answers:\nA. MDM\nAn MDM (Mobile Device Management) service provides configuration \nand control of remote devices. An MDM does not provide a checklist for \nhandling security events.\nB. DLP\nDLP (Data Loss Prevention) is a security solution for identifying and \nblocking the transfer of sensitive information across the network. A DLP \nwould not provide steps to follow during a security event.\nD. Zero trust\nZero trust is a security philosophy which considers all devices to be \nuntrusted. Inherent trust and trusted connections between devices are \nnot part of a zero trust model. Zero trust does not provide checklists for \nsecurity tasks.\nMore information:\nSY0-701, Objective 5.1 - Security Procedures https://professormesser.link/*********"}, {"question": "C38. A transportation company maintains a scheduling application and \na database in a virtualized cloud-based environment. Which of the \nfollowing would be the BEST way to backup these services?", "options": ["Journaling", "Snapshot", "RTOS", "Containerization"], "answer": "Snapshot", "explanation": "B. Snapshot\nVirtual machines (VMs) have a snapshot feature to capture both a full \nbackup of the virtual system and incremental changes that occur over time. \nIt’s common to take a snapshot of a VM for backup purposes or before \nmaking any significant changes to the VM. If the changes need to be \nrolled back, a previous snapshot can be selected and instantly applied to \nthe VM.\nThe incorrect answers:\nA. Journaling\nJournaling protects the integrity of a file system or database by writing \ninformation to a journal before making any changes to the primary data \nsource. This allows the system to recover from a potential fault and prevent \nfile corruption.\nC. RTOS\nAn RTOS (Real-Time Operating System) has a deterministic processing \nschedule and is often associated with time-sensitive applications. An \nRTOS is not a backup mechanism.\nD. Containerization\nContainerization describes an application deployment strategy where a \nsingle file or container includes everything required to run an application. \nContainerization itself is not a backup mechanism.\nMore information:\nSY0-701, Objective 3.4 - Backups https://professormesser.link/*********"}, {"question": "C39. In an environment using discretionary access controls, which of these \nwould control the rights and permissions associated with a file or \ndirectory?", "options": ["Administrator", "Owner", "Group", "System"], "answer": "Owner", "explanation": "<PERSON>. Owner\nThe owner of an object controls access in a discretionary access control \nmodel. The object and type of access is at the discretion of the owner, and \nthey can determine who can access the file and the type of access they \nwould have.\nThe incorrect answers:\nA. Administrator \nAdministrators generally label objects when using mandatory access \ncontrol, but they are not involved with discretionary access control.\nC. Group\nAssigning rights and permissions to a group and then assigning users to \nthe group are common when using role-based access control. \nD. System\nThe system does not determine individual user rights and permissions \nwhen using discretionary access control.\nMore information:\nSY0-701, Objective 4.6 - Access Controls https://professormesser.link/*********"}, {"question": "C40. A security administrator has installed a network-based DLP solution to \ndetermine if file transfers contain PII. Which of the following describes \nthe data during the file transfer?", "options": ["In-use", "In-transit", "Highly available", "At-rest"], "answer": "In-transit", "explanation": "B. In-transit\nData in-transit describes information actively moving across the network. \nAs the information passes through switches and routers, it is considered to \nbe in-transit.\nThe incorrect answers:\nA. In-use \nData in-use is in the memory of a system and is accessible to an \napplication.\nC. Highly available\nHigh availability (HA) is usually associated with redundancy or fault-\ntolerance. Data moving through the network would not be considered \nhighly available.\nD. At-rest\nData at-rest resides on a storage device.\nMore information:\nSY0-701, Objective 3.3 - States of Data https://professormesser.link/*********"}, {"question": "C41. A medical imaging company would like to connect all remote locations \ntogether with high speed network links. The network connections must \nmaintain high throughput rates and must always be available during \nworking hours. In which of the following should these requirements be \nenforced with the network provider?", "options": ["Service level agreement", "Memorandum of understanding", "Non-disclosure agreement", "Acceptable use policy"], "answer": "Service level agreement", "explanation": "A. Service level agreement\nA service level agreement (SLA) is used to contractually define the \nminimum terms for services. In this example, the medical imaging \ncompany would require an SLA from the network provider for the \nnecessary throughput and uptime metrics. \nThe incorrect answers:\nB. Memorandum of understanding\nA memorandum of understanding (MOU) is an informal letter of \nintent. The MOU is not a signed contract, and there are no contractual \nobligations associated with the content of an MOU.\nC. Non-disclosure agreement\nA non-disclosure agreement (NDA) is used between entities to prevent \nthe use and dissemination of confidential information.\nD. Acceptable use policy\nAn acceptable use policy (AUP) commonly details the rules of behavior \nfor employees using an organization’s network and computing resources.\nMore information:\nSY0-701, Objective 5.3 - Agreement Types https://professormesser.link/*********"}, {"question": "C42. A company is implementing a security awareness program for their user \ncommunity. Which of the following should be included for additional \nuser guidance and training?", "options": ["Daily firewall exception reporting", "Information on proper password management", "Periodic vulnerability scanning of external services", "Adjustments to annualized loss expectancy"], "answer": "Information on proper password management", "explanation": "B. Information on proper password management\nUser awareness programs focus on security fundamentals that everyone \nin the organization can use during their normal work day. Protecting and \nmanaging passwords is an important security consideration for all users in \nthe company.\nThe incorrect answers:\nA. Daily firewall exception reporting \nDaily security reports can provide important insight into the \norganization's security posture, but it doesn't provide security guidance for \nthe user community.\nC. Periodic vulnerability scanning of external services\nPeriodic audits and security scans can provide validation and identify \npotential issues, but the vulnerability scan results don't provide any help to \nthe user community with their ongoing security responsibilities.\nD. Adjustments to annualized loss expectancy\nAnnualized loss expectancy estimates can be important for budgeting and \nsecurity planning, but those expenses aren't related to user community \nguidance and training.\nMore information:\nSY0-701, Objective 5.6 - User Training https://professormesser.link/*********"}, {"question": "C43. A security administrator is preparing a phishing email as part of a \nperiodic employee security awareness campaign. The email is spoofed to \nappear as an unknown third-party and asks employees to immediately \nclick a link or their state licensing will be revoked. Which of the \nfollowing should be the expected response from the users?", "options": ["Delete the message", "Click the link and make a note of the URL", "Forward the message to others in the department", "Report the suspicious link to the help desk"], "answer": "Report the suspicious link to the help desk", "explanation": "D. Report the suspicious link to the help desk\nThe users should be trained to report anything suspicious, and unusual \nlinks in an email message would certainly be an important security \nconcern.\nThe incorrect answers:\n<PERSON><PERSON> Delete the message\nDeleting the email would avoid any interaction with the malicious \nlink, but it wouldn't provide any additional security for others in the \norganization. The contents of the email might also provide important \ninformation for removing similar messages and blocking future emails.\nB. Click the link and make a note of the URL\nThe links inside of email messages are inherently insecure, and a best \npractice is to never click unknown or unexpected links or attachments \ninside of email messages.\nC. Forward the message to others in the department\nForwarding a message with potentially malicious links would be a \nsignificant security concern, and it would be more secure to forward a copy \nto the IT security team.\nMore information:\nSY0-701, Objective 5.6 - Security Awareness https://professormesser.link/*********"}, {"question": "C44. A security administrator would like to minimize the number of certificate \nstatus checks made by web site clients to the certificate authority. Which \nof the following would be the BEST option for this requirement?", "options": ["OCSP stapling", "Self-signed certificates", "CRL", "Wildcards"], "answer": "OCSP stapling", "explanation": "A. OCSP stapling\nOCSP (Online Certificate Status Protocol) stapling allows the certificate \nholder verify their own certificate status. The OCSP status is commonly \n“stapled” into the SSL/TLS handshake process. Instead of contacting the \ncertificate authority to verify the certificate, the verification is included \nwith the initial network connection to the server.\nThe incorrect answers:\nB. Self-signed certificates\nSelf-signed certificates could be created for internal company use, but this \nwould not change the process for validating the status of a certificate.\nC. CRL\nA CRL (Certificate Revocation List) is a list of revoked certificates \nmaintained by the certificate authority. To view the CRL, an end-user \nclient would directly access the CA. \nD. Wildcards\nWildcards are added to certificates for use across multiple devices. \nWildcards would not decrease the number of certificate status checks for a \nparticular service.\nMore information:\nSY0-701, Objective 1.4 - Certificates https://professormesser.link/*********"}, {"question": "C45. A company is concerned their EDR solution will not be able to stop more \nadvanced ransomware variants. Technicians have created a backup and \nrestore utility to get most systems up and running less than an hour after \nan attack. What type of security control is associated with this restore \nprocess?", "options": ["Directive", "Compensating", "Preventive", "Detective"], "answer": "Compensating", "explanation": "B. Compensating\nInstead of preventing an attack, a compensating control is used to restore \nsystems using other means. A streamlined backup and restore process \ncompensates for the limited security features of the EDR (Endpoint \nDetection and Response) software.\nThe incorrect answers:\nA. Directive\nDirective controls define policies and processes, but directive controls \nwon't provide a method for recovering from a ransomware infection.\nC. Preventive\nA preventive control will block access. The EDR software on a workstation \nis an example of a preventive control.\nD. Detective\nA detective control may not be able to block an attack, but it can identify \nand alert if an attack is underway.\nMore information:\nSY0-701, Objective 1.1 - Security Controls https://professormesser.link/*********"}, {"question": "C46. To upgrade an internal application, the development team provides \nthe operations team with instructions for backing up, patching the \napplication, and reverting the patch if needed. The operations team \nschedules a date for the upgrade, informs the business divisions, and tests \nthe upgrade process after completion. Which of the following describes \nthis process?", "options": ["Code signing", "Continuity planning", "Usage auditing", "Change management"], "answer": "Change management", "explanation": "D. Change management\nChange management is the process for making any type of change, \nsuch as a software upgrade, a hardware replacement, or any other type \nof modification to the existing environment. Having a formal change \nmanagement process minimizes the risk of a change and makes everyone \naware of the changes as they occur.\nThe incorrect answers:\nA. Code signing\nApplication developers often digitally sign their software to ensure no \nmodifications are made before the software is installed. The code signing \nprocess does not provide any guidance for an organization's internal \nprocesses associated with installing updated software.\nB. Continuity planning\nContinuity planning focuses on keeping the business running when a \ndisruption occurs. Disaster recovery planning is a type of continuity plan. \nC. Usage auditing\nUsage auditing determines how resources are used. For example, a system \nadministrator may perform a usage audit to determine which resources are \nused with a particular application or service.\nMore information:\nSY0-701, Objective 1.3 - Change Management Process https://professormesser.link/*********"}, {"question": "C47. A company is implementing a public file-storage and cloud-based sharing \nservice, and would like users to authenticate with an existing account on a \ntrusted third-party web site. Which of the following should the company \nimplement?", "options": ["SSO", "Federation", "Least privilege", "Discretionary access controls"], "answer": "Federation", "explanation": "B. Federation\nFederation provides authentication and authorization between two entities \nusing a separate trusted authentication platform. For example, a web site \ncould allow authentication using an existing account on a third-party \nsocial media site.\nThe incorrect answers:\nA. SSO \nSSO (Single Sign-On) does not inherently require authentication to be \nprocessed by a third-party. SSO allows a user to authenticate one time \nto gain access to all assigned resources. No additional authentication is \nrequired after the initial SSO login process is complete.\nC. Least privilege\nLeast privilege ensures users only receive the permissions necessary \nto perform their assigned functions. Least privilege is not used to \nauthenticate users to a third-party site.\nD. Discretionary access controls\nDiscretionary access controls are used by a data owner to allow or \nprevent access to the data. Discretionary access controls are not used to \nauthenticate users to a third-party database.\nMore information:\nSY0-701, Objective 4.6 - Identity and Access Management https://professormesser.link/*********"}, {"question": "C48. A system administrator is viewing this output Microsoft's System File Checker :\n5:43:01 - Repairing corrupted file C:\\Windows\\System32\\kernel32.dll\n  15:43:03 - Repairing corrupted file C:\\Windows\\System32\netapi32.dll\n  15:43:07 - Repairing corrupted file C:\\Windows\\System32\\user32.dll\n  15:43:43 - Repair complete\n  \nWhich of the following malware types is the MOST likely cause of this \noutput?", "options": ["Ransomware", "Logic bomb", "Rootkit", "Keylogger"], "answer": "Rootkit", "explanation": "C. Rootkit\nA rootkit modifies operating system files to become part of the core OS. \nThe kernel, user, and networking libraries in Windows are core operating \nsystem files.\nThe incorrect answers:\nA. Ransomware \nRansomware commonly presents itself as a warning message on the \nuser's screen, and most aspects of the operating system would be disabled. \nRansomware also encrypts user documents and would not easily be \nrepaired by replacing system files.\nB. Logic bomb\nA logic bomb waits for a predefined event to begin operation. Logic \nbombs do not commonly modify core operating system files. \nD. Keylogger\nA keylogger does not commonly embed itself in core operating system \nfiles. Keyloggers often run as an independent process and compile logs and \nkeystrokes to send across the network to the attacker.\nMore information:\nSY0-701, Objective 2.4 - Other Malware Types https://professormesser.link/*********"}, {"question": "C49. What type of vulnerability would be associated with this log information?\nGET http://example.com/show.asp?view=../../Windows/ system.ini HTTP/1.1", "options": ["Buffer overflow", "Directory traversal", "DoS", "Cross-site scripting"], "answer": "Directory traversal", "explanation": "B. Directory traversal\nDirectory traversal attempts to read or access files outside the scope of \nthe web server's file directory. The pair of dots in a file path (..) refers to \nthe parent directory, so this example is attempt to move back two parent \ndirectories before proceeding into the /Windows directory. In a properly \nconfigured web server, this traversal should not be possible.\nThe incorrect answers:\n<PERSON><PERSON> overflow \nA buffer overflow would attempt to store information into an area of \nmemory that overflows the boundary of the buffer. The information in the \nlog does not show any overflow attempt.\nC. DoS\nA DoS (Denial of Service) is designed to make a system or service \nunavailable. Although running any unknown command can be \nunpredictable, it would be unusual for these commands to cause any \ndowntime. \nD. Cross-site scripting\nA cross-site scripting attack would normally include a script referencing \nanother site trusted by the browser. In this example, the commands appear \nto be related to the existing URL and not a third-party site.\nMore information:\nSY0-701, Objective 2.4 - Application Attacks https://professormesser.link/701020412"}, {"question": "C50. A developer has created an application to store password information in \na database. Which of the following BEST describes a way of protecting \nthese credentials by adding random data to the password?", "options": ["Hashing", "Data masking", "Salting", "Asymmetric encryption"], "answer": "Salting", "explanation": "C. Salting\nPasswords are often stored as hashes, but the hashes themselves are often \nsubject to brute force or rainbow table attacks. It’s common to add some \nadditional random data (a salt) to a password before the hashing process. \nThis ensures that each password is truly random when stored, and it makes \nit more difficult for an attacker to discover all of the stored passwords.\nThe incorrect answers:\nA. Hashing \nHashing is a one-way cryptographic function which takes an input, such \nas a password, and creates a fixed size string of random information. The \nprocess of adding additional information to the original data before the \nhashing process is called salting.\nB. Data masking\nData masking hides data from human eyes. For example, instead of \nshowing a credit card number, the data mask will show asterisks in all but \nthe last four digits.\nD. Asymmetric encryption\nAsymmetric encryption is an encryption method which uses one key for \nencryption and a different key for decryption. Asymmetric encryption \ndoes not add additional random information to a hash.\nMore information:\nSY0-701, Objective 3.3 - Protecting Data https://professormesser.link/*********"}, {"question": "C51. Which of the following processes provides ongoing building and testing \nof newly written code?", "options": ["Continuous integration", "Continuity of operations", "Version control", "Race condition"], "answer": "Continuous integration", "explanation": "A. Continuous integration\nWith continuous integration, code can be constantly written and merged \ninto the central repository many times each day.\nThe incorrect answers:\nB. Continuity of operations\nContinuity of operations is used during disaster recovery or incident \nrecovery. This process provides options for keeping the business processes \navailable during or after the incident.\nC. Version control\nVersion control is used to track changes to a file or configuration \ninformation over time. This allows changes to be applied and, if necessary, \neasily reverted to a previous version.\nD. Race condition\nA race condition is caused when two related processes occur \nsimultaneously without knowledge of each other. A race condition is not \nrelated the process of building or testing code.\nMore information:\nSY0-701, Objective 4.7 - Scripting and Automation https://professormesser.link/*********"}, {"question": "C52. Which of the following BEST describes a responsibility matrix?", "options": ["A visual summary of cloud provider accountability", "Identification of tasks at each step of a project plan", "A list of cybersecurity requirements based on the identified risks", "Ongoing group discussions regarding cybersecurity"], "answer": "A visual summary of cloud provider accountability", "explanation": "A. A visual summary of cloud provider accountability\nA cloud provider commonly creates a responsibility matrix to document \nthe service coverage between the cloud provider and the customer. For \nexample, a cloud responsibility matrix may show the cloud provider \nresponsible for network controls and the customer responsible for all \nstored data.\nThe incorrect answers:\nB. Identification of tasks at each step of a project plan \nA project plan will include many tasks, and the list of tasks is often shown \nas part of the overall project plan or in a summarized chart.\nC. A list of cybersecurity requirements based on the identified risks\nRisk assessment provides a security administrator with the information \nneeded to build proper security controls for the documented risks. \nD. Ongoing group discussions regarding cybersecurity\nRisk assessment can involve constant monitoring and analysis of current \ntrends, risks, and response options. This information can be gathered from \ngroup discussions, expert presentations, and security conferences and \nprograms.\nMore information:\nSY0-701, Objective 3.1 - Cloud Infrastructures https://professormesser.link/*********"}, {"question": "C53. A security administrator is implementing an authentication system \nfor the company. Which of the following would be the best choice \nfor validating login credentials for all usernames and passwords in the \nauthentication system?", "options": ["CA", "SIEM", "LDAP", "WAF"], "answer": "LDAP", "explanation": "C. LDAP\nLDAP (Lightweight Directory Access Protocol) is a common standard \nfor authentication. LDAP is an open standard and is available across many \ndifferent operating systems and devices.\nThe incorrect answers:\nA. CA\nA CA (Certificate Authority) is a trusted service for certificate creation \nand management. The CA itself is not responsible for validating login \ncredentials.\nB. SIEM\nA SIEM (Security and Information Management) service consolidates log \nfiles from diverse systems and can create reports based on the correlation \nof this data. A SIEM is not part of the authentication process.\nD. WAF\nA WAF (Web Application Firewall) is used to protect a web-based \napplication from exploits and other attacks. A WAF is not used to validate \nlogin credentials.\nMore information:\nSY0-701, Objective 4.6 - Identity and Access Management https://professormesser.link/*********"}, {"question": "C55. A company has contracted with a third-party to provide penetration  testing services. The service includes a port scan of each externally-facing  device. This is an example of:", "options": ["Initial exploitation", "Privilege escalation", "Known environment", "Active reconnaissance"], "answer": "Active reconnaissance", "explanation": " The Answer: D. Active reconnaissance\n  Active reconnaissance sends traffic across the network, and this traffic can\n  be viewed and logged. Performing a port scan will send network traffic to a\n  server, and most port scan attempts can be identified and logged by an IPS\n  (Intrusion Prevention System).\n  The incorrect answers:\n  A. Initial exploitation\n  An exploit attempt is common when performing a penetration test, but a\n  port scan is not exploiting any vulnerabilities.\n  B. Privilege escalation\n  If a penetration test is able to exploit a system and obtain a higher level of\n  rights and permissions, then the test is successful at escalating the access\n  privileges. A port scan does not gain access to a system, and it will not\n  provide any privilege escalation.\n  C. Known environment\n  A known environment fully documents the network and systems within\n  the scope of a penetration test. In this example, there's no mention of\n  testing environment documentation provided to the penetration testers.\nMore information:  SY0-701, Objective 5.5 - Penetration Tests  https://professormesser.link/*********"}, {"question": "C54. A technician is reviewing this information from an IPS log:\nMAIN_IPS: 22June2023 09:02:50 reject **********\n  Alert: HTTP Suspicious Webdav OPTIONS Method Request; Host: Server\n  Severity: medium; Performance Impact:3;\n  Category: info-leak; Packet capture; disable\n  Proto:tcp; dst:************; src:**********\n  \nWhich of the following can be associated with this log information? \n(doua)\n", "multiple answers": 2, "options": ["The attacker sent a non-authenticated BGP packet to trigger the IPS ", "The source of the attack is ************", "The event was logged but no packets were dropped", "The source of the attack is **********", "The attacker sent an unusual HTTP packet to trigger the IPS"], "answer": ["The source of the attack is **********", "The attacker sent an unusual HTTP packet to trigger the IPS"], "explanation": " The Answer: D. The source of the attack is ********** and \nE.The attacker sent an unusual HTTP packet to trigger the IPS\nThe second line of the IPS log shows the type of alert, and this record indicates a suspicious HTTP packet was sent. The last line of the IPS log shows the protocol, destination, and source IP address information. The source IP address is **********. \nThe incorrect answers:\nA. The attacker sent a non-authenticated BGP packet to trigger the IPS \nThe alert for this IPS log does not indicate any non-authenticated packets or BGP packets.\nB. The source of the attack is ************ \nThe last line of the log identifies the protocol and IP addresses. The “src” address is the source of the packet and is identified as **********. \nC. The event was logged but no packets were dropped \nThe first line of the log shows the name of the IPS which identified the issue, the date and time, and disposition. In this log entry, the packet was rejected from IP address **********. \nMore information:\nSY0-701, Objective 4.9 - Log Data\nhttps://professormesser.link/*********"}, {"question": "C56. An access point in a corporate heardquarters office has the following configuration:\nIP address: *********\n  Subnet mask: *************\n  DHCPv4 Server: Enabled\n  SSID: Wireless\n  Wireless Mode: 802.11n\n  Security Mode: WEP-PSK\n  Frequency band: 2.4 GHz\n  Software revision: 2.1\n  MAC Address: 60:3D:26:71:FF:AA\n  IPv4 Firewall: Enabled\nWhich of the following would apply to this configuration?", "options": ["Invalid frequency band", "Weak encryption", "Incorrect IP address and subnet mask", "Invalid software version"], "answer": "Weak encryption", "explanation": "B. Weak encryption\nA common issue is weak or outdated security configurations. Older \nencryptions such as DES and WEP should be updated to use newer and \nstronger encryption technologies.\nThe incorrect answers:\nA. Invalid frequency band \nThe 2.4 GHz frequency band is a valid frequency range for 802.11n \nnetworks.\nC. Incorrect IP address and subnet mask\nNone of the listed configuration settings show any issues with the IP \naddress or subnet mask. \nD. Invalid software version\nThe software version of the access point does not have any configuration \noptions and would not be considered invalid.\nMore information:\nSY0-701, Objective 2.2 - Common Threat Vectors https://professormesser.link/*********"}, {"question": "C57. An attacker has gained access to an application through the use of packet \ncaptures. Which of the following would be MOST likely used by the \nattacker?", "options": ["Overflow", "Forgery", "Replay", "Injection"], "answer": "Replay", "explanation": "C. Replay\nA replay attack uses previously transmitted information to gain access \nto an application or service. This information is commonly captured in \nnetwork packets and replayed to the service.\nThe incorrect answers:\nA. Overflow \nA buffer overflow attack attempts to store a large number into a smaller \nsized memory space. This can sometimes improperly change the value of \nmemory areas that are outside of the smaller space.\nB. Forgery\nA cross-site request forgery commonly uses malicious links to take \nadvantage of the trust a site might have for a user's browser. Packet \ncaptures are not necessary to perform a forgery attack.\nD. Injection\nThe unwanted injection of data into a database, library, or any other data \nflow is an injection attack. The information contained in a packet capture \nis not commonly used during an injection attack.\nMore information:\nSY0-701, Objective 2.4 - Replay Attacks https://professormesser.link/*********"}, {"question": "C58. A company is receiving complaints of slowness and disconnections to \ntheir Internet-facing web server. A network administrator monitors the \nInternet link and finds excessive bandwidth utilization from thousands \nof different IP addresses. Which of the following would be the MOST \nlikely reason for these performance issues?", "options": ["DDoS", "DNS spoofing", "RFID cloning", "Wireless jamming"], "answer": "DDoS", "explanation": "A. DDoS\nA DDoS (Distributed Denial of Service) is the failure of a service caused \nby many different remote devices. In this example, the DDoS is related to \na bandwidth utilization exhaustion caused by excessive server requests.\nThe incorrect answers:\nB. DNS spoofing\nDNS (Domain Name System) spoofing modifies DNS information on \na DNS server or a client to direct users to an unauthorized site. DNS \nspoofing would not be the cause for these performance issues.\nC. RFID cloning\nRFID (Radio Frequency Identification) cloning is used to duplicate an \nexisting RFID device. These devices are not commonly associated with \nnetwork communication to a public web server.\nD. Wireless jamming\nWireless jamming disrupts wireless networks and prevents any type of \ncommunication. The communication issues to a public web server would \nnot be associated with wireless networking.\nMore information:\nSY0-701, Objective 2.4 - Denial of Service https://professormesser.link/*********"}, {"question": "C59. A company has created an itemized list of tasks to be completed by \na third-party service provider. After the services are complete, this \ndocument will be used to validate the completion of the services. Which \nof the following would describe this agreement type?", "options": ["SLA", "SOW", "NDA", "BPA"], "answer": "SOW", "explanation": "B. SOW\nA SOW (Statement of Work) is a detailed list of tasks, items, or processes \nto be completed by a third-party. The SOW lists the job scope, location, \ndeliverables, and any other specifics associated with the agreement. The \nSOW is also used as a checklist to verify the job was completed properly \nby the service provider.\nThe incorrect answers:\nA. SLA\nAn SLA (Service Level Agreement) sets the minimum terms of service \nbetween a customer and a service provider. This agreement often contains \nterms for expected uptime, response time requirements, and other \nminimum service levels required by the customer.\nC. NDA\nAn NDA (Non-Disclosure Agreement) is a confidentiality agreement \nbetween parties. The agreement is designed to protect information such as \ntrade secrets, business activities, or anything else included in the NDA. An \nNDA does not generally contain an itemized list of service requests.\nD. BPA\nA BPA (Business Partners Agreement) is used between entities going into \nbusiness together. A list of itemized service requests would not be part of a \nBPA.\nMore information:\nSY0-701, Objective 5.3 - Agreement Types https://professormesser.link/*********"}, {"question": "C60. A company is deploying a series of internal applications to different cloud \nproviders. Which of the following connection types should be deployed \nfor this configuration?", "options": ["Air-gapped", "802.1X", "Site-to-site IPsec VPN", "Jump server", "SD-WAN"], "answer": "SD-WAN", "explanation": "E. SD-WAN\nAn SD-WAN (Software Defined Networking in a Wide Area Network) \nnetwork allows users to efficiently communicate directly to cloud-based \napplications.\nThe incorrect answers:\nA. Air-gapped\nAn air-gapped network would be physically isolated from other networks. \nIn this question, connectivity is required between the users and the various \ncloud providers.\nB. 802.1X\n802.1X is a standard for port-based network access control and would \nmost likely be used when a user first connects to the network and before a \nuser would connect to the Internet.\nC. Site-to-site IPsec VPN\nAlthough it's physically possible to connect every site to every other \nsite (or to cloud providers), it's difficult to scale this design to larger \nenvironments. This connectivity also becomes difficult to manage as \napplications move from one cloud provider site to another.\nD. Jump server\nA jump server is often used to allow external access to internal devices, \ncommonly for maintenance or administrative tasks. A jump server is \nnot a router and does not forward traffic between users and cloud-based \napplications.\nMore information:\nSY0-701, Objective 3.2 - Secure Communication https://professormesser.link/*********"}, {"question": "C61. A company is updating components within the control plane of their \nzero-trust implementation. Which of the following would be part of this \nupdate?", "options": ["Policy engine", "Subjects", "Policy enforcement point", "Zone configurations"], "answer": "Policy engine", "explanation": "A. Policy engine\nThe policy engine is located in the control plane and evaluates each access \ndecision based on security policy and other information sources. The policy \nengine determines if access should be granted, denied, or revoked.\nThe incorrect answers:\nB. Subjects\nSubjects use the zero-trust data plane, and are often end-users, \napplications, or other non-human entities.\nC. Policy enforcement point\nA policy enforcement point resides in the data plane and is the gatekeeper \nfor allowing, monitoring, and terminating connections.\nD. Zone configurations\nZero-trust uses security zones to easily apply access policies, and these \nzones operate in the data plane.\nMore information:\nSY0-701, Objective 1.2 - Zero Trust https://professormesser.link/*********"}, {"question": "C62. Which of the following malware types would cause a workstation to \nparticipate in a DDoS?", "options": ["Bot", "Logic bomb", "Ransomware", "Keylogger"], "answer": "Bot", "explanation": "A. Bot\nA bot (robot) is malware that installs itself on a system and then waits for \ninstructions. It’s common for botnets to use thousands of bots to perform \nDDoS (Distributed Denial of Service) attacks.\nThe incorrect answers:\nB. Logic bomb \nA logic bomb waits for a predefined event to occur. The scope of devices \ninfected with a logic bomb are relatively small and localized as compared \nto a botnet.\nC. Ransomware\nRansomware locks a system and prevents it from operating. The locked \ndevice does not commonly participate in a DDoS. \nD. Keylogger\nA keylogger will silently capture keystrokes and transmit an archive \nof those keystrokes to a third-party. A keylogger does not commonly \nparticipate in a DDoS.\nMore information:\nSY0-701, Objective 2.4 - Denial of Service https://professormesser.link/*********"}, {"question": "C63. Which of these are used to force the preservation of data for later  \nuse in court?", "options": ["Chain of custody", "Data loss prevention", "Legal hold", "E-discovery"], "answer": "Legal hold", "explanation": "C. Legal hold\nA legal hold is a legal technique to preserve relevant information. This \nprocess will ensure the data remains accessible for any legal preparation \nprior to litigation.\nThe incorrect answers:\nA. Chain of custody \nChain of custody ensures the integrity of evidence is maintained. The \ncontents of the evidence are documented, and each person who contacts \nthe evidence is required to document their activity.\nB. Data loss prevention\nData loss prevention (DLP) is a technique for identifying sensitive \ninformation transmitted across the network, such as Social Security \nnumbers, credit card numbers, and other PII (Personally Identifiable \nInformation). DLP is not a legal technique. \nD. E-discovery\nE-discovery describes the process of identifying and collecting electronic \ndocuments and media. The e-discovery process itself does not force the \npreservation of data.\nMore information:\nSY0-701, Objective 4.8 - Digital Forensics https://professormesser.link/*********"}, {"question": "C64. A company would like to automatically monitor and report on any \nmovement occurring in an open field at the data center. Which of the \nfollowing would be the BEST choice for this task?", "options": ["<PERSON><PERSON><PERSON>", "Microwave sensor", "Access control vestibule", "Fencing"], "answer": "Microwave sensor", "explanation": "B. Microwave sensor\nMicrowave sensors can detect movement across large areas such as  \nopen fields.\nThe incorrect answers:\n<PERSON><PERSON>\nA bollard is a barricade used to prevent access. Bollards often allow people \nto pass through a specific access point, but limit access for cars and other \nvehicles.\nC. Access control vestibule\nAn access control vestibule is a room designed to manage the flow of \npeople through the area. It's common to see access control vestibules used \nas an entry point to a data center or secure facility.\nD. Fencing\nFencing can create a perimeter to prevent access to a large open field, but \nit wouldn't detect or alert on any type of movement.\nMore information:\nSY0-701, Objective 1.2 - Physical Security https://professormesser.link/*********"}, {"question": "C65. A company is releasing a new product, and part of the release includes  \nthe installation of load balancers to the public web site. Which of the \nfollowing would best describe this process?", "options": ["Platform diversity", "Capacity planning", "Multi-cloud systems", "Permission restrictions"], "answer": "Capacity planning", "explanation": "B. Capacity planning\nCapacity planning describes the process of matching the supply of a \nresource to the demand. In this example, the company is planning for an \nincreased interest in their products and are increasing the overall capacity \nof their web server resources.\nThe incorrect answers:\nA. Platform diversity\nPlatform diversity describes the use of different platforms to provide a \nsimilar service. For example, a company may decide to use both Linux and \nWindows platforms for their web services. In this question, the platform \nused by the web services is not mentioned.\nC. Multi-cloud systems\nMulti-cloud systems will use more than a single cloud provider to provide \na service. In this question, there were no specific references to cloud \nproviders.\nD. Permission restrictions\nPermission restrictions would limit access to data or resources, and the \naddition of multiple identical servers would not indicate a change to the \nexisting permissions.\nMore information:\nSY0-701, Objective 3.4 - Capacity Planning https://professormesser.link/*********"}, {"question": "C66. A system administrator would like to prove an email message was sent by \na specific person. Which of the following describes the verification of this \nmessage source?", "options": ["Non-repudiation", "Key escrow", "Asymmetric encryption", "Steganography"], "answer": "Non-repudiation", "explanation": "A. Non-repudiation\nNon-repudiation is used to verify the source of data or a message. Digital \nsignatures are commonly used for non-repudiation.\nThe incorrect answers:\nB. Key escrow\nKey escrow describes a third-party responsible for holding or managing \nkeys or certificates. Key escrow does not provide verification of a data \nsource.\nC. Asymmetric encryption\nAsymmetric encryption describes data encryption using one key and \nthe decryption of this data with a different key. The use of asymmetric \nencryption by itself does not provide proof of origin.\nD. Steganography\nSteganography describes hiding one type of data within another media \ntype. For example, hiding encrypted data within an image is a form of \nsteganography. Steganography does not provide proof of origin.\nMore information:\nSY0-701, Objective 1.2 - Non-repudiation https://professormesser.link/701010202"}, {"question": "C67. A security administrator has created a policy to alert if a user modifies \nthe hosts file on their system. Which of the following behaviors does this \npolicy address?", "options": ["Unexpected", "Self-assessment", "Unintentional", "Risky"], "answer": "Risky", "explanation": "<PERSON><PERSON> Risky\nMaking a change to the hosts file can be a security concern, and many \nsystems will prevent this change without elevated permissions. Modifying \nthe hosts file would be categorized as risky behavior.\nThe incorrect answers:\nA. Unexpected\nEditing a hosts file is a specific task with an intentional result. The user \nmodification of the hosts file would not generally be considered an \nunexpected event.\nB. Self-assessment\nA self-assessment is often used in internal audits to informally gather \ninformation about potential security risks. A self-assessment is not part of \na user's intentional file edits.\nC. Unintentional\nA user editing a file is an active process and is often associated with \nperforming a specific configuration change or task.\nMore information:\nSY0-701, Objective 5.6 - Security Awareness https://professormesser.link/*********"}, {"question": "C68. A company has identified a web server data breach resulting in the theft \nof financial records from 150 million customers. A security update to the \ncompany’s web server software was available for two months prior to the \nbreach. Which of the following would have prevented this breach from \noccurring?", "options": ["Patch management", "Full disk encryption", "Disabling unnecessary services", "Application allow lists"], "answer": "Patch management", "explanation": "A. Patch management\nThis question describes an actual breach which occurred in 2017 to web \nservers at a large credit bureau. This breach resulted in the release of almost \n150 million customer names, Social Security numbers, addresses, and \nbirth dates. A web server vulnerability announced in March of 2017 was \nleft unpatched, and attackers exploited the vulnerability two months later. \nThe attackers were in the credit bureau network for 76 days before they \nwere discovered. A formal patch management process would have clearly \nidentified this vulnerability and would have given the credit bureau the \nopportunity to mitigate or patch the vulnerability well before it would \nhave been exploited.\nThe incorrect answers:\nB. Full disk encryption \nFull disk encryption (FDE) would prevent unauthenticated access to the \ndata, but the web server would be an authorized user and would have \nnormal access to the areas of the operating system necessary for normal \noperation. Enabling FDE would not provide any additional security \nagainst a data breach.\nC. Disable unnecessary services\nIt’s always a good best practice to disable unnecessary services, but this \nbreach attacked a very necessary web service. \nD. Application allow lists\nApplication allow lists would prevent unauthorized applications from \nrunning, but it would not prevent an attack to the web service application.\nMore information:\nSY0-701, Objective 2.5 - Mitigation Techniques https://professormesser.link/*********"}, {"question": "C69. During the onboarding process, the IT department requires a list of \nsoftware applications associated with the new employee's job functions. \nWhich of the following would describe the use of this information?", "options": ["Access control configuration", "Encryption settings", "Physical security requirements", "Change management"], "answer": "Access control configuration", "explanation": "A. Access control configuration\nThe onboarding team needs to assign the proper access controls to new \nemployees, and the list of applications provides additional details regarding \napplication and data access.\nThe incorrect answers:\nB. Encryption settings\nA list of applications required by a new employee does not generally have \nany impact on the encryption settings used by these applications.\nC. Physical security requirements\nPhysical security requirements would not generally be based on the list \nof required applications for a new employee. Most physical security \nrequirements are determined by the organization's IT security team.\nD. Change management\nAdding rights and permissions for a new user would be a normal \nprocedure and would not require a formal change management process.\nMore information:\nSY0-701, Objective 5.1 - Security Standards https://professormesser.link/*********"}, {"question": "C70. A system administrator has identified an unexpected username on a \ndatabase server, and the user has been transferring database files to an \nexternal server over the company’s Internet connection. The administrator \nthen performed these tasks:\n• Physically disconnected the Ethernet cable on the database server\n• Disabled the unknown account\n• Configured a firewall rule to prevent file transfers from the server\nWhich of the following would BEST describe this part of the incident \nresponse process?", "options": ["Eradication", "Containment", "Lessons learned", "Preparation"], "answer": "Containment", "explanation": "B. Containment\nThe containment phase isolates events which can quickly spread and get \nout of hand. A file transfer from a database server can quickly be contained \nby disabling any ability to continue the file transfer.\nThe incorrect answers:\nA. Eradication\nEradication focuses on removing the cause of the event and restoring the \nsystems back to their non-compromised state.\nC. Lessons learned\nAfter the event is over, the lessons learned phase helps everyone learn and \nimprove the process for the next event. \nD. Preparation\nBefore an event occurs, it’s important to have the contact numbers, tools, \nand processes ready to go.\nMore information:\nSY0-701, Objective 4.8 - Incident Response https://professormesser.link/*********"}, {"question": "C71. Which of the following would be the MOST effective use of  \nasymmetric encryption?", "options": ["Real-time video encryption", "Securely store passwords", "Protect data on mobile devices", "Create a shared session key"], "answer": "Create a shared session key", "explanation": "<PERSON><PERSON> <PERSON>reate a shared session key\nThe <PERSON><PERSON><PERSON><PERSON> algorithm can combine public and private keys to \nderive the same session key. This allows two devices to create and use this \nshared session key without sending the key across the network.\nThe incorrect answers:\nA. Real-time video encryption\nThe high speeds required for real-time video encryption and decryption \nwould not be an efficient use case for asymmetric encryption. High-speed \nor large-scale encryption commonly uses a faster method of encryption \nand decryption.\nB. Securely store passwords\nThe best practice for password storage is to use hashes instead of \nencryption. Hashes ensure a stored password can't be reverse engineered to \nproduce the original password.\nC. Protect data on mobile devices\nThe limited CPU and power available on a mobile device requires a more \nefficient form of confidentiality than asymmetric encryption. For example, \nit's common for mobile devices to use elliptic curve cryptography (ECC).\nMore information:\nSY0-701, Objective 1.4 - Key Exchange https://professormesser.link/*********"}, {"question": "C72. Each salesperson in a company receives a laptop with applications and \ndata to support their sales efforts. The IT manager would like to prevent \nthird-parties from gaining access to this information if the laptop is \nstolen. Which of the following would be the BEST way to protect  \nthis data?", "options": ["Remote wipe", "Full disk encryption", "Biometrics", "VPN"], "answer": "Full disk encryption", "explanation": "B. Full disk encryption\nWith full disk encryption, everything written to the laptop’s local drive is \nstored as encrypted data. If the laptop was stolen, the thief would not have \nthe credentials to decrypt the drive data.\nThe incorrect answers:\nA. Remote wipe \nAlthough a remote wipe function is useful, it’s a reactive response and does \nnot provide any data protection prior to erasing the data.\nC. Biometrics\nBiometric authentication can limit access to the operating system, but \nthe laptop’s storage drive can still be removed and accessed from another \ncomputer. \nD. VPN\nA VPN (Virtual Private Network) would encrypt all data transferred over \nthe network, but it would not protect any stored data if the laptop was \nstolen.\nMore information:\nSY0-701, Objective 1.4 - Encrypting Data https://professormesser.link/*********"}, {"question": "C73. A security administrator has compiled a list of all information stored and \nmanaged by an organization. Which of the following would best describe \nthis list?", "options": ["Sanitization", "<PERSON><PERSON><PERSON>", "Known environment", "Data inventory"], "answer": "Data inventory", "explanation": "D. Data inventory\nA data inventory describes a list of all data managed by an organization. \nThis inventory includes the owner, update frequency, and format of the \ndata.\nThe incorrect answers:\nA. Sanitization\nData sanitization involves the complete removal of data without any \nmethod of recovery. Data sanitization is often used when clearing storage \nmedia for reuse or disposal.\nB. Metadata\nMetadata is data which describes other data sources. Email header \ninformation, network headers, and file characteristics are common \nexamples of metadata. \nC. Known environment\nA known environment commonly describes the information provided to a \npenetration tester. A known environment provides a complete overview of \nthe in-scope devices associated with a penetration test.\nMore information:\nSY0-701, Objective 5.4 - Privacy https://professormesser.link/*********"}, {"question": "C74. A security administrator would like to monitor all outbound Internet \nconnections for malicious software. Which of the following would \nprovide this functionality?", "options": ["Jump server", "IPsec tunnel", "Forward proxy", "Load balancer"], "answer": "Forward proxy", "explanation": "C. Forward proxy\nA proxy server can be used to monitor incoming and outgoing network \ncommunication. Proxy servers can be used to identify malicious software, \nfilter content, or increase performance through file caching.\nThe incorrect answers:\nA. Jump server\nA jump server is commonly used to provide administrative access to a \nsecure network connection. Jump servers are not used to monitor or filter \nInternet connections.\nB. IPsec tunnel\nAn IPsec tunnel is associated with an encrypted connection between \ndevices or sites. An IPsec tunnel would not be used to monitor or manage \nnetwork content or Internet connections.\nD. Load balancer\nLoad balancers are used to increase capacity by separating the processing \nload between multiple servers. Load balancers are not used for network \nmonitoring or security filtering.\nMore information:\nSY0-701, Objective 3.2 - Network Appliances https://professormesser.link/*********"}, {"question": "C75. What type of security control would be associated with corporate security \npolicies?", "options": ["Technical", "Operational", "Managerial", "Physical"], "answer": "Managerial", "explanation": "C. Managerial\nA managerial control type is associated with security design and \nimplementation. Security policies and standard operating procedures are \ncommon examples of a managerial control type.\nThe incorrect answers:\nA. Technical\nTechnical security controls are implemented using systems, such as \noperating system controls, firewalls, or anti-virus software.\nB. Operational\nOperational controls are implemented by people instead of systems. An \nexample of an operational security control type would be security guards or \nawareness programs.\nD. Physical\nA physical control type would limit physical access. For example, a door \nlock or badge reader would be a physical control.\nMore information:\nSY0-701, Objective 1.1 - Security Controls https://professormesser.link/*********"}, {"question": "C76. Which of the following would be the MOST significant security concern \nwhen protecting against organized crime?", "options": ["Prevent users from posting passwords near their workstations", "Require identification cards for all employees and guests", "Maintain reliable backup data", "Use access control vestibules at all data center locations"], "answer": "Maintain reliable backup data", "explanation": "C. Maintain reliable backup data\nA common objective for organized crime is an organization's data, and \nattacks from organized crime can sometimes encrypt or delete data. A \ngood set of backups can often resolve these issues quickly and without any \nransomware payments to an organized crime syndicate.\nThe incorrect answers:\nA. Prevent users from posting passwords near their workstations \nOrganized crime members usually access systems remotely. Although \nit’s important for users to protect their passwords, the organized crime \nmembers aren’t generally in a position to view information on a person's \ndesk.\nB. Require identification cards for all employees and guests\nSince the criminal syndicate members rarely visit a site, having \nidentification for employees and visitors isn’t the most significant concern \nassociated with this threat actor.\nD. Use access control vestibules at all data center locations\nAccess control vestibules control the flow of people through an area. \nOrganized crime members aren’t usually visiting a company's data center.\nMore information:\nSY0-701, Objective 2.1 - Threat Actors https://professormesser.link/*********"}, {"question": "C77. An application team has been provided with a hardened version of Linux \nto use with a new application installation, and this includes installing \na web service and the application code on the server. Which of the \nfollowing would BEST protect the application from attacks?", "options": ["Build a backup server for the application", "Run the application in a cloud-based environment", "Implement a secure configuration of the web service", "Send application logs to the SIEM via syslog"], "answer": "Implement a secure configuration of the web service", "explanation": "C. Implement a secure configuration of the web service\nThe tech support resources for many services will include a list of \nhardening recommendations. This hardening may include account \nrestrictions, file permission settings, internal service configuration options, \nand other settings to ensure that the service is as secure as possible.\nThe incorrect answers:\nA. Build a backup server for the application \nOf course, you should always have a backup. Although the backup may \nhelp recover quickly from an attack, the backup itself won’t protect the \napplication from attacks.\nB. Run the application in a cloud-based environment\nThe location of the application service won’t provide any significant \nprotection against attacks. Given the options available, running the \napplication in the cloud would not be the best option available. \nD. Send application logs to the SIEM via syslog\nIt’s always useful to have a consolidated set of logs, but the logs on the \nSIEM (Security Information and Event Management) server won’t \nprotect the application from attacks.\nMore information:\nSY0-701, Objective 2.5 - Hardening Techniques https://professormesser.link/*********"}, {"question": "C78. A system administrator has configured MAC filtering on their corporate \naccess point, but access logs show unauthorized users accessing the \nnetwork. Which of the following should the administrator configure to \nprevent future unauthorized use?", "options": ["Enable WPA3 encryption", "Remove unauthorized MAC addresses from the filter", "Modify the SSID name", "Modify the channel frequencies"], "answer": "Enable WPA3 encryption", "explanation": "A. Enable WPA3 encryption\nA MAC (Media Access Control) address can be spoofed on a remote \ndevice, which means anyone within the vicinity of the access point can \nview and use legitimate MAC addresses. To ensure proper authentication, \nthe system administrator can enable WPA3 (Wi-Fi Protected Access \nversion 3) with a pre-shared key or 802.1X can be used to integrate with \nan existing authentication database.\nThe incorrect answers:\nB. Remove unauthorized MAC addresses from the filter \nSince MAC addresses are visible when capturing packets, any \nunauthorized users affected by the removal of a MAC address would \nsimply view the remaining MAC addresses in use and spoof those \naddresses to gain access.\nC. Modify the SSID name\nThe SSID (Service Set Identifier) is the name associated with the wireless \nnetwork. The name of the access point is not a security feature, so changing \nthe name would not provide any additional access control. \n<PERSON>. Modify the channel frequencies\nThe frequencies used by the access point are chosen to minimize \ninterference with other nearby wireless devices. These wireless channels \nare not security features and changing the frequency would not limit \nunauthorized access.\nMore information:\nSY0-701, Objective 4.1 - Wireless Security Settings https://professormesser.link/*********"}, {"question": "C79. A system administrator has been tasked with performing an application \nupgrade, but the upgrade has been delayed due to a different scheduled \ninstallation of an outdated device driver. Which of the following issues \nwould best describe this change management delay?", "options": ["Deny list", "Legacy application", "Dependency", "Restricted activity"], "answer": "Dependency", "explanation": "C. Dependency\nModifying one part of a system may first require changes to other \ncomponents. In this example, the application upgrade is dependent on an \nupdated version of a device driver.\nThe incorrect answers:\n<PERSON><PERSON> list\nA deny list would prevent an application from executing. In this question, \nan older version of the application is currently working, and there's no \nmention of preventing a newer version of the application from also \nworking properly.\nB. Legacy application\nA legacy application is usually not supported by the developer, and it \nwould be unusual for a legacy application to release an updated version of \nsoftware.\nD. Restricted activity\nMost change control processes have a limited scope, and a technician \nwould be restricted from making changes outside of that scope. In this \nexample, the device driver and the application are both part of the change \ncontrol process, but one of the changes must occur before the other change \ncan be made.\nMore information:\nSY0-701, Objective 1.3 - Technical Change Management https://professormesser.link/701010302"}, {"question": "C80. During an initial network connection, a supplicant communicates to an \nauthenticator, which then sends an authentication request to an Active \nDirectory database. Which of the following would BEST describe this \nauthentication technology?", "options": ["Federation", "UTM", "802.1X", "PKI"], "answer": "802.1X", "explanation": "C. 802.1X\nIEEE 802.1X is a standard for port-based network access control (NAC). \nWhen 802.1X is enabled, devices connecting to the network do not \ngain access until they provide the correct authentication credentials. \nThis 802.1X standard refers to the client as the supplicant, the switch \nis commonly configured as the authenticator, and the back-end \nauthentication server is often a centralized user database.\nThe incorrect answers:\nA. Federation \nFederation would allow members of one organization to authenticate to \nthe network of another organization using their normal credentials.\nB. UTM\nA UTM (Unified Threat Management) system is a legacy all-in-one \nsecurity device which combines a firewall, anti-virus, content filtering, and \nother security features into a single system.\nD. PKI\nPKI (Public Key Infrastructure) is a method of describing the public-key \nencryption technologies and its supporting policies and procedures. PKI \ndoes not require the use of supplicants, authenticators, or authentication \nservers.\nMore information:\nSY0-701, Objective 3.2 - Port Security https://professormesser.link/701030204"}, {"question": "C81. A security researcher has been notified of a potential hardware \nvulnerability. Which of the following should the researcher evaluate as a \npotential security issue?", "options": ["Firmware versions", "Firewall configuration", "SQL requests", "XSS attachments"], "answer": "Firmware versions", "explanation": "A. Firmware versions\nFirmware describes the software inside of a hardware device and is often \nused as the operating system of the hardware. Issues with hardware \nvulnerabilities are usually resolved by updating firmware in the vulnerable \nsystem.\nThe incorrect answers:\nB. Firewall configuration \nMisconfigured firewall software could certainly be a security issue, but the \nproblem reported in this question is specific to a hardware vulnerability.\nC. SQL requests\nA SQL (Structured Query Language) request is commonly associated \nwith a database-related process. SQL requests are software-related and are \nnot related to a hardware vulnerability.\nD. XSS attachments\nXSS (Cross-site Scripting) is an exploit which uses the trust in a browser \nto gain access to a web site. An XSS attachment describes a malicious \nscript included in an email or similar delivery mechanism. Cross-site \nscripting is a software exploit and is not associated with a hardware \nvulnerability.\nMore information:\nSY0-701, Objective 2.3 - Hardware Vulnerabilities https://professormesser.link/*********"}, {"question": "C82. Visitors to a corporate data center must enter through the main doors \nof the building. Which of the following security controls would be the \nBEST choice to successfully guide people to the front door? \n(doua)", "multiple answers": 2, "options": ["Infrared sensors", "<PERSON><PERSON><PERSON>", "Biometrics", "Fencing", "Access badges", "Video surveillance"], "answer": ["<PERSON><PERSON><PERSON>", "Fencing"], "explanation": "The Answers\n B \n Bollards and D \n Fencing Both bollards and fencing provide physical security controls to direct people to an area by limiting their access to other areas \n The incorrect answers \n A \n Infrared sensors Infrared sensors are able to detect infrared radiation in both dark and light environments \n Infrared sensors are often included with surveillance video systems, but they wouldn't be used to direct individuals to the main doors of a building \n C \n Biometrics Biometrics provide a unique authentication factor, but they aren't commonly used to direct people to a particular building entrance \n E \n Access Badges Access badges are often used as both identification and access cards to secure areas of a facility \n An access badge would not direct individuals to the main doors of a building \n F \n Video surveillance Video surveillance would make it easy to monitor and view visitors approaching the building, but it would not provide any directions to the front doors \n More information \n SY0-701, Objective 1.2 - Physical Security https://professormesser.link/*********"}, {"question": "C83. A company's employees are required to authenticate each time a file share, printer, or SAN imaging system is accessed. Which of the following should be used to minimize the number of employee authentication requests?", "options": ["SSO", "OSINT", "MFA", "SCAP"], "answer": "SSO", "explanation": "A. SSO\nSSO (Single Sign-On) accepts valid authentication requests and allows \nusers to access multiple resources without requiring additional user \nauthentications.\nThe incorrect answers:\nB. OSINT \nOSINT (Open Source Intelligence) is information gathered from publicly \navailable sources such as social media sites, online forums, and other data \nsources. OSINT is not associated with user authentication.\nC. MFA\nMFA (Multi-factor authentication) is used to provide additional proof \nof a user's identity during the authentication process. MFA is not used to \nminimize the number of authentication requests required by a system.\nD. SCAP\nSCAP (Security Content Automation Protocol) is a standard method used \nby security tools to identify and act on the same criteria. SCAP is not used \nto minimize the number of required authentications.\nMore information:\nSY0-701, Objective 4.6 - Identity and Access Management https://professormesser.link/*********"}, {"question": "C84. A company has recently moved from one accounting system to another, \nand the new system includes integration with many other divisions of the \norganization. Which of the following would ensure that the correct access \nhas been provided to the proper employees in each division?", "options": ["Geolocation", "Onboarding process", "Account de-provisioning", "Internal self-assessment"], "answer": "Internal self-assessment", "explanation": "D. Internal self-assessment\nAn internal self-assessment with audit can verify users have the correct \npermissions and all users meet the practice of least privilege.\nThe incorrect answers:\nA. Geolocation \nGeolocation would allow the system to assign rights and permissions \nbased on physical location. In this question, there's no documentation on \nwhere users are located and how those locations could be used for access \ncontrol.\nB. Onboarding process\nThe onboarding process is used when a new person is hired or transferred \ninto the organization. In this example, none of the users were identified as \nnew employees.\nC. Account de-provisioning\nAccount de-provisioning is the disabling of an account and archiving of \nuser information. This process usually occurs when an employee has left \nthe organization.\nMore information:\nSY0-701, Objective 5.5 - Audits and Assessments https://professormesser.link/*********"}, {"question": "C85. An attacker has circumvented a web-based application to send commands \ndirectly to a database. Which of the following would describe this attack \ntype?", "options": ["Downgrade", "SQL injection", "Cross-site scripting", "On-path"], "answer": "SQL injection", "explanation": "B. SQL injection\nA SQL (Structured Query Language) injection takes advantage of poorly \nwritten web applications. These web applications do not properly restrict \nthe user input, and the resulting attack bypasses the application and \n“injects” SQL commands directly into the database itself.\nThe incorrect answers:\nA. Downgrade\nA downgrade attack commonly takes advantage of a poorly implemented \ncryptographic functions to force an application to use sub-optimal or non-\nexistent security features.\nC. Cross-site scripting\nA cross-site scripting attack commonly uses scripts to execute commands \non a third-party website. These types of attacks take advantage of the \ntrust of a local browser, but they don’t commonly have direct access to a \ndatabase.\nD. On-path\nAn on-path attack is often used to capture, monitor, or inject information \ninto an existing data flow. An on-path attack is not commonly used for \nSQL injection attacks.\nMore information:\nSY0-701, Objective 2.3 - SQL Injection https://professormesser.link/*********"}, {"question": "C86. A group of business partners is using blockchain technology to monitor \nand track raw materials and parts as they are transferred between \ncompanies. Where would a partner find these tracking details?", "options": ["Ledger", "HSM", "SIEM", "HIPS"], "answer": "Ledger", "explanation": "<PERSON><PERSON> Ledger\nThe ledger is a shared document with a list of all blockchain transactions. \nThe ledger is shared among everyone in the blockchain, and all \ntransactions are available to view on this central ledger.\nThe incorrect answers:\nB. HSM\nAn HSM (Hardware Security Module) provides secure key storage and \ncryptographic functions for servers and applications. An HSM does not \nprovide tracking services.\nC. SIEM\nA SIEM (Security Information and Event Manager) is commonly used \nto consolidate log files and create reports. A SIEM is not used to monitor \nblockchain transactions.\nD. HIPS\nA HIPS (Host-based Intrusion Prevention System) is used to identify \nexploit attempts on a device. A host-based IPS is not used to monitor data \nin a blockchain.\nMore information:\nSY0-601, Objective 1.4 - Blockchain Technology https://professormesser.link/*********"}, {"question": "C87. A network technician at a bank has noticed a significant decrease in \ntraffic to the bank's public website. After additional investigation, the \ntechnician finds that users are being directed to a web site which looks \nsimilar to the bank's site but is not under the bank's control. Flushing the \nlocal DNS cache and changing the DNS entry does not have any effect. \nWhich of the following has most likely occurred?", "options": ["DDoS", "Disassociation attack", "Buffer overflow", "Domain hijacking"], "answer": "Domain hijacking", "explanation": "D. Domain hijacking\nDomain hijacking will modify the primary DNS (Domain Name System) \nsettings for a domain and allow an attacker to direct users to an IP address \ncontrolled by the attacker.\nThe incorrect answers:\nA. DDoS \nA DDoS (Distributed Denial of Service) would prevent users from \naccessing a service. In this example, users were accessing an unauthorized \nservice.\nB. Disassociation attack\nA disassociation attack is a wireless vulnerability which can remove devices \nfrom a wireless network.\nC. Buffer overflow\nA buffer overflow is an application attack where an input of data can \noverwrite a buffer of memory space. A buffer overflow would not be used \nto redirect users to a different web page.\nMore information:\nSY0-701, Objective 2.4 - DNS Attacks https://professormesser.link/*********"}, {"question": "C88. A company runs two separate applications in their data center. The \nsecurity administrator has been tasked with preventing all communication \nbetween these applications. Which of the following would be the BEST \nway to implement this security requirement?", "options": ["Firewall", "SDN", "Air gap", "VLANs"], "answer": "Air gap", "explanation": "C. Air gap\nAn air gap is a physical separation between networks. Air gapped networks \nare commonly used to separate networks that must never communicate to \neach other.\nThe incorrect answers:\nA. Firewall \nA firewall would provide a method of filtering traffic between networks, \nbut firewalls can often be misconfigured and inadvertently allow some \ntraffic to pass. Although this is one option, it’s not the best option given \nthe option of an air gap.\nB. SDN\nSDN (Software Defined Networking) splits the functions of a networking \ndevice into separate logical units. SDN does not describe a security filter \nor firewall between applications in a data center.\nD. VLANs\nA VLAN (Virtual Local Area Network) is a logical method of segmenting \ntraffic within network switches. Although this segmentation is effective, \nit’s not as secure as an air gap.\nMore information:\nSY0-701, Objective 3.1 - Network Infrastructure Concepts https://professormesser.link/*********"}, {"question": "C89. A receptionist at a manufacturing company recently received an email \nfrom the CEO asking for a copy of the internal corporate employee \ndirectory. It was later determined that the email address was not sent \nfrom the CEO and the domain associated with the email address was not \na corporate domain name. What type of training could help identify this \ntype of attack in the future?", "options": ["Recognizing social engineering", "Proper password management", "Securing remote work environments", "Understanding insider threats"], "answer": "Recognizing social engineering", "explanation": "<PERSON>. Recognizing social engineering\nImpersonating the CEO is a common social engineering technique. There \nare many ways to recognize a social engineering attack, and it’s important \nto train everyone to spot these situations when they are occurring.\nThe incorrect answers:\nB. Proper password management\nProper password management focuses on protecting passwords through \nthe use of standard policies. These policies focus on topics such as \npassword length, complexity, or password reuse.\nC. Securing remote work environments\nA remote work environment is very different to secure than a traditional \nwork environment, but those concerns would not help to identify this type \nof social engineering attack.\nD. Understanding insider threats\nAlthough the attacker wasn’t identified, we could assume that an employee \nwould already have access to the internal corporate employee directory.\nMore information:\nSY0-701, Objective 5.6 - User Training https://professormesser.link/*********"}, {"question": "C90. Which of the following deployment models would a company follow if \nthey require individuals to use their personal phones for work purposes?", "options": ["CYOD", "MDM", "BYOD", "COPE"], "answer": "BYOD", "explanation": "C. BYOD\nBYOD (Bring Your Own Device) is a model where the employee owns \nthe mobile device but can also use the same device for work.\nThe incorrect answers:\nA. CYOD\nThe CYOD (Choose Your Own Device) model requires the corporation to \npurchase and own the device, but the user can select the device they would \nprefer to use.\nB. MDM\nAn MDM (Mobile Device Manager) is used to manage company-owned \nand user-owned mobile devices.\nD. COPE\nCOPE (Corporately Owned, Personally Enabled) devices are purchased \nby the company and deployed to the users. The organization keeps full \ncontrol of the device and may allow the recipient to use the device for both \nbusiness and personal use.\nMore information:\nSY0-701, Objective 4.1 - Securing Wireless and Mobile https://professormesser.link/*********"}, {"type": "matching", "questionText": "A2. The security team at a manufacturing company is creating a set of security standards for employees and visitors. Select the BEST security control for each location. All of the available security controls will be used once.", "availableControls": ["Access badge", "Fencing", "Access control vestibule", "Security guard", "Authentication token", "Biometrics", "Lighting"], "locations": [{"id": "outside", "name": "Outside Building", "description": "Parking and Visitor drop-off", "icon": "building"}, {"id": "reception", "name": "Reception", "description": "Building lobby", "icon": "reception"}, {"id": "data_center", "name": "Data Center Door", "description": "Entrance from inside building", "icon": "door"}, {"id": "server_admin", "name": "Server Administration", "description": "Authentication to server console in the data center", "icon": "server"}], "correctMatches": {"outside": ["Fencing", "Lighting"], "reception": ["Security guard", "Access badge"], "data_center": ["Access control vestibule", "Biometrics"], "server_admin": ["Authentication token"]}}]