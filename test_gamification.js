// Test script for gamification features

// Function to test gamification features
function testGamification() {
    console.log("Testing gamification features...");

    // Test user data
    const testUser = {
        gamification: {
            xp: 0,
            level: 1,
            achievements: {},
            streak: {
                current: 0,
                lastQuizDate: null
            }
        },
        stats: {
            overall: { totalQuestions: 0, correctAnswers: 0, totalTime: 0, quizzesTaken: 0 },
            A: { totalQuestions: 0, correctAnswers: 0, totalTime: 0 },
            B: { totalQuestions: 0, correctAnswers: 0, totalTime: 0 },
            C: { totalQuestions: 0, correctAnswers: 0, totalTime: 0 }
        },
        quizHistory: []
    };

    // Test XP awards
    console.log("Testing XP awards...");
    const xpResult = window.gamification.awardXP(window.gamification.XP_REWARDS.CORRECT_ANSWER, testUser);
    console.log("XP award result:", xpResult);
    console.log("User XP after award:", testUser.gamification.xp);

    // Test streak updates
    console.log("Testing streak updates...");
    const streakResult = window.gamification.updateStreak(testUser);
    console.log("Streak update result:", streakResult);
    console.log("User streak after update:", testUser.gamification.streak);

    // Test achievement checks
    console.log("Testing achievement checks...");
    const achievementResult = window.gamification.checkAchievements(testUser);
    console.log("Achievement check result:", achievementResult);

    console.log("Gamification tests complete!");
}

// Function to test theme switching
function testThemes() {
    console.log("Testing theme switching...");

    // Get all theme options
    const themes = ['standard', 'cyberpunk', 'nature', 'space', 'retro'];

    // Test each theme
    themes.forEach(theme => {
        console.log(`Setting theme to ${theme}...`);
        setTheme(theme);
        console.log(`Theme set to ${theme}`);
    });

    console.log("Theme tests complete!");
}

// Function to test focus tools
function testFocusTools() {
    console.log("Testing focus tools...");

    // Test Pomodoro timer
    console.log("Testing Pomodoro timer...");
    window.focusTools.initPomodoro();
    window.focusTools.startPomodoroTimer();
    console.log("Pomodoro timer started");

    // Test focus mode
    console.log("Testing focus mode...");
    window.focusTools.toggleFocusMode();
    console.log("Focus mode toggled");

    // Test ambient sounds
    console.log("Testing ambient sounds...");
    window.focusTools.initAmbientSounds();
    window.focusTools.playAmbientSound('rain', 0.5);
    console.log("Ambient sound playing");

    console.log("Focus tools tests complete!");
}

// Function to run all tests
function runAllTests() {
    console.log("Running all gamification and enhancement tests...");

    // Test gamification features
    testGamification();

    // Test theme switching
    testThemes();

    // Test focus tools
    testFocusTools();

    console.log("All tests complete!");
}

// Add a button to run tests
function addTestButton() {
    const container = document.querySelector('.main-content');
    if (!container) return;

    const testButton = document.createElement('button');
    testButton.id = 'testGamificationBtn';
    testButton.className = 'button';
    testButton.textContent = 'Test Gamification';
    testButton.style.position = 'fixed';
    testButton.style.bottom = '10px';
    testButton.style.right = '10px';
    testButton.style.zIndex = '1000';
    testButton.onclick = runAllTests;

    container.appendChild(testButton);
}

// Add the test button when the page loads
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit to ensure all other scripts are loaded
    setTimeout(addTestButton, 1000);
});
