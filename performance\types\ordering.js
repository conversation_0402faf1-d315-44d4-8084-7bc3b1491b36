/**
 * ordering.js
 * Ordering question type implementation
 */

const OrderingType = (function() {
  /**
   * Render an ordering question
   * @param {Object} question - Question object
   * @param {HTMLElement} container - Container element
   */
  function render(question, container) {
    if (!question || !question.orderingData || !container) {
      console.error('Invalid question or container');
      return;
    }
    
    const { orderingData } = question;
    
    // Create container elements
    const orderingContainer = document.createElement('div');
    orderingContainer.className = 'ordering-container';
    
    // Create items container
    const itemsContainer = document.createElement('div');
    itemsContainer.className = 'ordering-items';
    
    // Create target list container
    const targetListContainer = document.createElement('div');
    targetListContainer.className = 'ordering-target-list';
    
    // Add title to target list
    const titleElement = document.createElement('h4');
    titleElement.textContent = 'Correct Order:';
    targetListContainer.appendChild(titleElement);
    
    // Render items (shuffled)
    const shuffledItems = PerformanceUtils.shuffleArray([...orderingData.items]);
    shuffledItems.forEach(item => {
      const itemElement = document.createElement('div');
      itemElement.className = 'ordering-item';
      itemElement.dataset.itemId = item.id;
      itemElement.draggable = true;
      
      // Add icon if available
      if (item.icon) {
        const iconElement = document.createElement('i');
        iconElement.className = item.icon;
        itemElement.appendChild(iconElement);
      }
      
      // Add text
      const textElement = document.createElement('span');
      textElement.textContent = item.text;
      itemElement.appendChild(textElement);
      
      // Add drag events
      itemElement.addEventListener('dragstart', handleDragStart);
      itemElement.addEventListener('dragend', handleDragEnd);
      
      itemsContainer.appendChild(itemElement);
    });
    
    // Create drop zones for ordering
    for (let i = 0; i < orderingData.items.length; i++) {
      const positionElement = document.createElement('div');
      positionElement.className = 'ordering-position';
      
      // Add position number
      const positionNumber = document.createElement('div');
      positionNumber.className = 'ordering-position-number';
      positionNumber.textContent = i + 1;
      positionElement.appendChild(positionNumber);
      
      // Add drop zone
      const dropZone = document.createElement('div');
      dropZone.className = 'ordering-drop-zone';
      dropZone.dataset.position = i;
      dropZone.addEventListener('dragover', handleDragOver);
      dropZone.addEventListener('dragleave', handleDragLeave);
      dropZone.addEventListener('drop', handleDrop);
      positionElement.appendChild(dropZone);
      
      targetListContainer.appendChild(positionElement);
    }
    
    // Assemble the question
    orderingContainer.appendChild(itemsContainer);
    orderingContainer.appendChild(targetListContainer);
    container.appendChild(orderingContainer);
  }
  
  /**
   * Validate an ordering answer
   * @param {Object} question - Question object
   * @param {Array} answer - User's answer (array of item IDs)
   * @returns {Object} - Validation result
   */
  function validateAnswer(question, answer) {
    if (!question || !question.orderingData || !question.orderingData.correctOrder) {
      return { correct: false, score: 0, details: { error: 'Invalid question' } };
    }
    
    if (!Array.isArray(answer)) {
      return { correct: false, score: 0, details: { error: 'Invalid answer' } };
    }
    
    const { correctOrder } = question.orderingData;
    const allowPartialCredit = question.orderingData.allowPartialCredit !== false;
    
    // Check if arrays match exactly
    const isExactMatch = correctOrder.length === answer.length && 
                         correctOrder.every((item, index) => item === answer[index]);
    
    // Calculate score based on longest common subsequence
    let score = 0;
    let lcsLength = 0;
    
    if (allowPartialCredit) {
      lcsLength = PerformanceUtils.longestCommonSubsequence(correctOrder, answer);
      score = (lcsLength / correctOrder.length) * 100;
    } else {
      score = isExactMatch ? 100 : 0;
    }
    
    // Calculate position-by-position correctness
    const positionDetails = [];
    
    for (let i = 0; i < Math.max(correctOrder.length, answer.length); i++) {
      const correctItem = i < correctOrder.length ? correctOrder[i] : null;
      const userItem = i < answer.length ? answer[i] : null;
      
      positionDetails.push({
        position: i + 1,
        correctItem,
        userItem,
        isCorrect: correctItem === userItem
      });
    }
    
    return {
      correct: isExactMatch,
      score,
      details: {
        totalItems: correctOrder.length,
        correctItems: isExactMatch ? correctOrder.length : lcsLength,
        positionDetails
      }
    };
  }
  
  /**
   * Get the current answer from the UI
   * @param {HTMLElement} container - Container element
   * @returns {Array} - Current answer (array of item IDs)
   */
  function getCurrentAnswer(container) {
    if (!container) return [];
    
    const answer = [];
    const dropZones = container.querySelectorAll('.ordering-drop-zone');
    
    // Process drop zones in order
    dropZones.forEach(dropZone => {
      const item = dropZone.querySelector('.ordering-item');
      if (item) {
        answer.push(item.dataset.itemId);
      }
    });
    
    return answer;
  }
  
  /**
   * Show the correct answer in the UI
   * @param {Object} question - Question object
   * @param {HTMLElement} container - Container element
   */
  function showCorrectAnswer(question, container) {
    if (!question || !question.orderingData || !container) return;
    
    const { correctOrder } = question.orderingData;
    
    // Get all drop zones
    const dropZones = container.querySelectorAll('.ordering-drop-zone');
    
    // Check each drop zone
    dropZones.forEach((dropZone, index) => {
      if (index >= correctOrder.length) return;
      
      const correctItemId = correctOrder[index];
      
      // Get current item in the drop zone
      const currentItem = dropZone.querySelector('.ordering-item');
      const currentItemId = currentItem ? currentItem.dataset.itemId : null;
      
      // Check if the item is correct
      const isCorrect = currentItemId === correctItemId;
      
      // Mark the drop zone as correct or incorrect
      if (isCorrect) {
        dropZone.classList.add('correct-answer');
      } else {
        dropZone.classList.add('incorrect-answer');
        
        // Show correct item
        const correctItem = question.orderingData.items.find(i => i.id === correctItemId);
        
        if (correctItem) {
          const correctLabel = document.createElement('div');
          correctLabel.className = 'correct-answer-label';
          correctLabel.innerHTML = `<strong>Correct:</strong> ${correctItem.text}`;
          dropZone.appendChild(correctLabel);
        }
      }
    });
  }
  
  /**
   * Handle drag start event
   * @param {Event} event - Drag event
   */
  function handleDragStart(event) {
    event.dataTransfer.setData('text/plain', event.target.dataset.itemId);
    event.target.classList.add('dragging');
  }
  
  /**
   * Handle drag end event
   * @param {Event} event - Drag event
   */
  function handleDragEnd(event) {
    event.target.classList.remove('dragging');
    
    // Remove drag-over class from all drop zones
    document.querySelectorAll('.drag-over').forEach(el => {
      el.classList.remove('drag-over');
    });
  }
  
  /**
   * Handle drag over event
   * @param {Event} event - Drag event
   */
  function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('drag-over');
  }
  
  /**
   * Handle drag leave event
   * @param {Event} event - Drag event
   */
  function handleDragLeave(event) {
    event.currentTarget.classList.remove('drag-over');
  }
  
  /**
   * Handle drop event
   * @param {Event} event - Drag event
   */
  function handleDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('drag-over');
    
    const itemId = event.dataTransfer.getData('text/plain');
    const item = document.querySelector(`.ordering-item[data-item-id="${itemId}"]`);
    
    if (item) {
      // Check if the drop zone already has an item
      const existingItem = event.currentTarget.querySelector('.ordering-item');
      
      if (existingItem) {
        // Swap items
        const originalDropZone = item.parentElement;
        if (originalDropZone && originalDropZone.classList.contains('ordering-drop-zone')) {
          originalDropZone.appendChild(existingItem);
          originalDropZone.classList.add('filled');
        } else {
          // Return to items container
          const itemsContainer = document.querySelector('.ordering-items');
          if (itemsContainer) {
            itemsContainer.appendChild(existingItem);
          }
        }
      }
      
      // Move the item to the drop zone
      event.currentTarget.appendChild(item);
      event.currentTarget.classList.add('filled');
    }
  }
  
  // Public API
  return {
    render,
    validateAnswer,
    getCurrentAnswer,
    showCorrectAnswer
  };
})();

// Register the type
if (!window.PerformanceTypes) window.PerformanceTypes = {};
window.PerformanceTypes.ordering = OrderingType;
