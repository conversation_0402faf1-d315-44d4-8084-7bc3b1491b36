/**
 * performance-utils.js
 * Utility functions for the performance-based question system
 */

const PerformanceUtils = (function() {
  /**
   * Shuffles an array using the Fisher<PERSON>Yates algorithm
   * @param {Array} array - The array to shuffle
   * @returns {Array} - A new shuffled array
   */
  function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  }

  /**
   * Generates a unique ID
   * @param {string} prefix - Optional prefix for the ID
   * @returns {string} - A unique ID
   */
  function generateId(prefix = 'perf') {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Formats a timestamp in seconds to a readable time string (MM:SS)
   * @param {number} seconds - Time in seconds
   * @returns {string} - Formatted time string
   */
  function formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  /**
   * Debounces a function
   * @param {Function} func - The function to debounce
   * @param {number} wait - The debounce wait time in milliseconds
   * @returns {Function} - The debounced function
   */
  function debounce(func, wait) {
    let timeout;
    return function(...args) {
      const context = this;
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(context, args), wait);
    };
  }

  /**
   * Throttles a function
   * @param {Function} func - The function to throttle
   * @param {number} limit - The throttle limit in milliseconds
   * @returns {Function} - The throttled function
   */
  function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  /**
   * Deep clones an object
   * @param {Object} obj - The object to clone
   * @returns {Object} - A deep clone of the object
   */
  function deepClone(obj) {
    return JSON.parse(JSON.stringify(obj));
  }

  /**
   * Validates a question object against the expected schema
   * @param {Object} question - The question object to validate
   * @param {string} type - The expected question type
   * @returns {boolean} - Whether the question is valid
   */
  function validateQuestion(question, type) {
    if (!question || typeof question !== 'object') return false;
    if (!question.id || !question.type || !question.questionText) return false;
    if (type && question.type !== type) return false;
    
    // Type-specific validation
    switch (question.type) {
      case 'matching':
        return validateMatchingQuestion(question);
      case 'ordering':
        return validateOrderingQuestion(question);
      case 'simulation':
        return validateSimulationQuestion(question);
      default:
        return true;
    }
  }

  /**
   * Validates a matching question
   * @param {Object} question - The matching question to validate
   * @returns {boolean} - Whether the question is valid
   */
  function validateMatchingQuestion(question) {
    if (!question.matchingData) return false;
    if (!Array.isArray(question.matchingData.items) || question.matchingData.items.length === 0) return false;
    if (!Array.isArray(question.matchingData.targets) || question.matchingData.targets.length === 0) return false;
    if (!question.matchingData.correctMatches || typeof question.matchingData.correctMatches !== 'object') return false;
    
    return true;
  }

  /**
   * Validates an ordering question
   * @param {Object} question - The ordering question to validate
   * @returns {boolean} - Whether the question is valid
   */
  function validateOrderingQuestion(question) {
    if (!question.orderingData) return false;
    if (!Array.isArray(question.orderingData.items) || question.orderingData.items.length === 0) return false;
    if (!Array.isArray(question.orderingData.correctOrder) || question.orderingData.correctOrder.length === 0) return false;
    
    return true;
  }

  /**
   * Validates a simulation question
   * @param {Object} question - The simulation question to validate
   * @returns {boolean} - Whether the question is valid
   */
  function validateSimulationQuestion(question) {
    if (!question.simulationData) return false;
    if (!question.simulationData.scenario) return false;
    if (!question.simulationData.environment || typeof question.simulationData.environment !== 'object') return false;
    if (!Array.isArray(question.simulationData.tasks) || question.simulationData.tasks.length === 0) return false;
    
    return true;
  }

  /**
   * Calculates the longest common subsequence between two arrays
   * Used for partial credit in ordering questions
   * @param {Array} arr1 - First array
   * @param {Array} arr2 - Second array
   * @returns {number} - Length of the longest common subsequence
   */
  function longestCommonSubsequence(arr1, arr2) {
    const m = arr1.length;
    const n = arr2.length;
    const dp = Array(m + 1).fill().map(() => Array(n + 1).fill(0));
    
    for (let i = 1; i <= m; i++) {
      for (let j = 1; j <= n; j++) {
        if (arr1[i - 1] === arr2[j - 1]) {
          dp[i][j] = dp[i - 1][j - 1] + 1;
        } else {
          dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
        }
      }
    }
    
    return dp[m][n];
  }

  // Public API
  return {
    shuffleArray,
    generateId,
    formatTime,
    debounce,
    throttle,
    deepClone,
    validateQuestion,
    longestCommonSubsequence
  };
})();

// Export for use in other modules
window.PerformanceUtils = PerformanceUtils;
