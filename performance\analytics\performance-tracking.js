/**
 * performance-tracking.js
 * Analytics tracking for the performance-based question system
 */

const PerformanceAnalytics = (function() {
  // Private variables
  let _initialized = false;
  let _sessionStartTime = null;
  let _currentQuestionStartTime = null;
  let _sessionData = {
    questions: [],
    interactions: [],
    totalTime: 0,
    correctAnswers: 0,
    incorrectAnswers: 0,
    partiallyCorrectAnswers: 0
  };
  
  /**
   * Initialize the analytics module
   * @param {Object} options - Configuration options
   * @returns {Object} - The PerformanceAnalytics instance
   */
  function init(options = {}) {
    if (_initialized) {
      console.warn('PerformanceAnalytics already initialized');
      return this;
    }
    
    // Subscribe to state changes
    PerformanceState.subscribe('question:change', _handleQuestionChange);
    PerformanceState.subscribe('answer:submit', _handleAnswerSubmit);
    
    // Start session
    _startSession();
    
    // Set initialized flag
    _initialized = true;
    console.log('PerformanceAnalytics initialized');
    
    return this;
  }
  
  /**
   * Track an answer submission
   * @param {Object} question - Question object
   * @param {any} answer - User's answer
   * @param {Object} result - Validation result
   */
  function trackAnswer(question, answer, result) {
    if (!_initialized) {
      console.warn('PerformanceAnalytics not initialized');
      return;
    }
    
    const questionEndTime = Date.now();
    const timeSpent = _currentQuestionStartTime ? questionEndTime - _currentQuestionStartTime : 0;
    
    // Record answer data
    const answerData = {
      questionId: question.id,
      questionType: question.type,
      category: question.category,
      difficulty: question.difficulty,
      timeSpent,
      timestamp: new Date().toISOString(),
      answer,
      result: {
        correct: result.correct,
        score: result.score
      }
    };
    
    // Update session data
    _sessionData.questions.push(answerData);
    _sessionData.totalTime += timeSpent;
    
    if (result.correct) {
      _sessionData.correctAnswers++;
    } else if (result.score > 0) {
      _sessionData.partiallyCorrectAnswers++;
    } else {
      _sessionData.incorrectAnswers++;
    }
    
    // Save to user data
    _saveToUserData(question, answer, result, timeSpent);
    
    // Reset question start time
    _currentQuestionStartTime = null;
  }
  
  /**
   * Track a user interaction
   * @param {string} interactionType - Type of interaction
   * @param {Object} data - Interaction data
   */
  function trackInteraction(interactionType, data = {}) {
    if (!_initialized) {
      console.warn('PerformanceAnalytics not initialized');
      return;
    }
    
    // Record interaction data
    const interactionData = {
      type: interactionType,
      timestamp: new Date().toISOString(),
      data
    };
    
    // Add to session data
    _sessionData.interactions.push(interactionData);
  }
  
  /**
   * Get session statistics
   * @returns {Object} - Session statistics
   */
  function getSessionStats() {
    if (!_initialized) {
      console.warn('PerformanceAnalytics not initialized');
      return {};
    }
    
    const totalQuestions = _sessionData.questions.length;
    const correctAnswers = _sessionData.correctAnswers;
    const incorrectAnswers = _sessionData.incorrectAnswers;
    const partiallyCorrectAnswers = _sessionData.partiallyCorrectAnswers;
    
    // Calculate statistics
    const stats = {
      totalQuestions,
      correctAnswers,
      incorrectAnswers,
      partiallyCorrectAnswers,
      accuracy: totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0,
      partialAccuracy: totalQuestions > 0 ? ((correctAnswers + partiallyCorrectAnswers) / totalQuestions) * 100 : 0,
      averageTime: totalQuestions > 0 ? _sessionData.totalTime / totalQuestions : 0,
      totalTime: _sessionData.totalTime,
      sessionDuration: _sessionStartTime ? Date.now() - _sessionStartTime : 0
    };
    
    // Add type-specific statistics
    const questionTypes = [...new Set(_sessionData.questions.map(q => q.questionType))];
    stats.byType = {};
    
    questionTypes.forEach(type => {
      const typeQuestions = _sessionData.questions.filter(q => q.questionType === type);
      const typeCorrect = typeQuestions.filter(q => q.result.correct).length;
      const typePartial = typeQuestions.filter(q => !q.result.correct && q.result.score > 0).length;
      const typeTime = typeQuestions.reduce((sum, q) => sum + q.timeSpent, 0);
      
      stats.byType[type] = {
        totalQuestions: typeQuestions.length,
        correctAnswers: typeCorrect,
        partiallyCorrectAnswers: typePartial,
        accuracy: typeQuestions.length > 0 ? (typeCorrect / typeQuestions.length) * 100 : 0,
        partialAccuracy: typeQuestions.length > 0 ? ((typeCorrect + typePartial) / typeQuestions.length) * 100 : 0,
        averageTime: typeQuestions.length > 0 ? typeTime / typeQuestions.length : 0,
        totalTime: typeTime
      };
    });
    
    return stats;
  }
  
  /**
   * End the current session
   * @returns {Object} - Session statistics
   */
  function endSession() {
    if (!_initialized) {
      console.warn('PerformanceAnalytics not initialized');
      return {};
    }
    
    // Calculate final statistics
    const stats = getSessionStats();
    
    // Reset session data
    _sessionData = {
      questions: [],
      interactions: [],
      totalTime: 0,
      correctAnswers: 0,
      incorrectAnswers: 0,
      partiallyCorrectAnswers: 0
    };
    
    // Reset session start time
    _sessionStartTime = null;
    
    return stats;
  }
  
  /**
   * Start a new session
   * @private
   */
  function _startSession() {
    _sessionStartTime = Date.now();
    _sessionData = {
      questions: [],
      interactions: [],
      totalTime: 0,
      correctAnswers: 0,
      incorrectAnswers: 0,
      partiallyCorrectAnswers: 0
    };
  }
  
  /**
   * Handle question change event
   * @param {Object} data - Event data
   * @private
   */
  function _handleQuestionChange(data) {
    const { question } = data;
    
    if (question) {
      // Record start time for the new question
      _currentQuestionStartTime = Date.now();
      
      // Track interaction
      trackInteraction('questionView', {
        questionId: question.id,
        questionType: question.type,
        category: question.category,
        difficulty: question.difficulty
      });
    }
  }
  
  /**
   * Handle answer submission event
   * @param {Object} data - Event data
   * @private
   */
  function _handleAnswerSubmit(data) {
    const { questionId, answer, result } = data;
    
    // Track interaction
    trackInteraction('answerSubmit', {
      questionId,
      result: result ? {
        correct: result.correct,
        score: result.score
      } : null
    });
  }
  
  /**
   * Save answer data to user data
   * @param {Object} question - Question object
   * @param {any} answer - User's answer
   * @param {Object} result - Validation result
   * @param {number} timeSpent - Time spent on the question
   * @private
   */
  function _saveToUserData(question, answer, result, timeSpent) {
    // Check if userData is available in the global scope
    if (typeof userData === 'undefined' || !userData) {
      console.warn('userData not available, cannot save performance data');
      return;
    }
    
    // Ensure performance data structure exists
    if (!userData.performance) {
      userData.performance = {
        completedQuestions: [],
        correctQuestions: [],
        incorrectQuestions: [],
        partiallyCorrectQuestions: [],
        questionHistory: {},
        skillMastery: {
          matching: 0,
          ordering: 0,
          simulation: 0
        }
      };
    }
    
    // Update question lists
    if (!userData.performance.completedQuestions.includes(question.id)) {
      userData.performance.completedQuestions.push(question.id);
    }
    
    // Remove from all lists first to avoid duplicates
    userData.performance.correctQuestions = userData.performance.correctQuestions.filter(id => id !== question.id);
    userData.performance.incorrectQuestions = userData.performance.incorrectQuestions.filter(id => id !== question.id);
    userData.performance.partiallyCorrectQuestions = userData.performance.partiallyCorrectQuestions.filter(id => id !== question.id);
    
    // Add to appropriate list
    if (result.correct) {
      userData.performance.correctQuestions.push(question.id);
    } else if (result.score > 0) {
      userData.performance.partiallyCorrectQuestions.push(question.id);
    } else {
      userData.performance.incorrectQuestions.push(question.id);
    }
    
    // Save question history
    userData.performance.questionHistory[question.id] = {
      type: question.type,
      category: question.category,
      difficulty: question.difficulty,
      lastAttempt: new Date().toISOString(),
      attempts: (userData.performance.questionHistory[question.id]?.attempts || 0) + 1,
      lastResult: {
        correct: result.correct,
        score: result.score,
        timeSpent
      },
      answer
    };
    
    // Update skill mastery
    if (question.type && userData.performance.skillMastery[question.type] !== undefined) {
      // Calculate new mastery level (weighted average of previous and current)
      const previousMastery = userData.performance.skillMastery[question.type];
      const currentMastery = result.score / 100;
      const weight = 0.3; // Weight for the new result
      
      userData.performance.skillMastery[question.type] = (previousMastery * (1 - weight)) + (currentMastery * weight);
    }
    
    // Save user data if saveUserData function is available
    if (typeof saveUserData === 'function' && typeof getCurrentNickname === 'function') {
      const nickname = getCurrentNickname();
      if (nickname) {
        saveUserData(nickname);
      }
    }
  }
  
  // Public API
  return {
    init,
    trackAnswer,
    trackInteraction,
    getSessionStats,
    endSession
  };
})();

// Export for use in other modules
window.PerformanceAnalytics = PerformanceAnalytics;
