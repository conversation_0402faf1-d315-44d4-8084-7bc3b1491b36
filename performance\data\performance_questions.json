[{"id": "match_security_locations", "type": "matching", "difficulty": "medium", "category": "A", "tags": ["security controls", "physical security", "access control"], "questionText": "The security team at a manufacturing company is creating a set of security standards for employees and visitors. Select the BEST security control for each location. All of the available security controls will be used once.", "timeLimit": 180, "points": 10, "explanation": "Security outside of the building is focused on the safety of employees and visitors as they park their vehicles or are dropped off at the entrance. The parking lot and exterior building areas should be surrounded by fencing to control access and the parking lot should be well-lit at all times. The reception area is the first interaction with employees or visitors. Security guards should be available to check the authorization of anyone entering the building, and the use of an access control vestibule can help manage the flow of individuals through this checkpoint. Once inside, many areas of the building are readily available to employees and visitors. However, some areas of the building containing sensitive information may require additional authorization. To gain access to the data center from inside of the building, an individual would need to provide a valid access badge and perform a biometric check of their fingerprint, handprint, or a similar type of authentication factor. Gaining access through the door of the data center doesn't provide any access to the server data. If a technician needs console access to a server, they'll need to provide the proper username, password, and authentication token. This multi-factor authentication ensures only authorized users are able to gain access to the information contained on the server.", "hint": "Consider the security requirements and sensitivity of each location.", "tracking": {"completionStatus": "incomplete", "attempts": 0, "lastAttempt": null, "bestScore": 0}, "matchingData": {"items": [{"id": "access_badge", "text": "Access badge", "icon": "fa-solid fa-id-card"}, {"id": "fencing", "text": "Fencing", "icon": "fa-solid fa-fence"}, {"id": "vestibule", "text": "Access control vestibule", "icon": "fa-solid fa-door-closed"}, {"id": "guard", "text": "Security guard", "icon": "fa-solid fa-user-shield"}, {"id": "token", "text": "Authentication token", "icon": "fa-solid fa-key"}, {"id": "biometrics", "text": "Biometrics", "icon": "fa-solid fa-fingerprint"}, {"id": "lighting", "text": "Lighting", "icon": "fa-solid fa-lightbulb"}], "targets": [{"id": "outside", "text": "Outside Building", "description": "Parking and Visitor drop-off", "icon": "fa-regular fa-building"}, {"id": "reception", "text": "Reception", "description": "Building lobby", "icon": "fa-solid fa-users"}, {"id": "data_center", "text": "Data Center Door", "description": "Entrance from inside building", "icon": "fa-solid fa-door-closed"}, {"id": "server_admin", "text": "Server Administration", "description": "Authentication to server console in the data center", "icon": "fa-solid fa-desktop"}], "correctMatches": {"outside": ["fencing", "lighting"], "reception": ["guard", "vestibule"], "data_center": ["access_badge", "biometrics"], "server_admin": ["token"]}, "allowMultiple": true, "requireAll": true}, "created": "2023-07-15T12:00:00Z", "lastModified": "2023-07-15T12:00:00Z", "version": 1}, {"id": "match_security_categories", "type": "matching_categories", "difficulty": "easy", "category": "A", "tags": ["security controls", "operational security", "managerial security", "physical security", "technical security"], "questionText": "Select the most appropriate security category. Some categories may be used more than once.", "timeLimit": 120, "points": 8, "explanation": "Operational controls are often implemented by people instead of systems. Security guards and awareness programs are examples of an operational control. Managerial controls are administrative controls associated with security design and implementation. A set of policies and procedures would be an example of a managerial control. Physical controls are used to limit physical access. Badge readers, fences, and guard shacks are categorized as physical controls. Technical controls are implemented using systems. Operating system controls, firewalls, and automated processes are considered technical controls.", "hint": "Consider whether the control involves people, policies, physical barriers, or technology.", "tracking": {"completionStatus": "incomplete", "attempts": 0, "lastAttempt": null, "bestScore": 0}, "matchingCategoriesData": {"categories": [{"id": "operational", "text": "Operational"}, {"id": "managerial", "text": "Managerial"}, {"id": "physical", "text": "Physical"}, {"id": "technical", "text": "Technical"}], "items": [{"id": "guard_check", "text": "A guard checks the identification of all visitors", "icon": "fa-solid fa-id-badge"}, {"id": "vp_approval", "text": "All returns must be approved by a Vice President", "icon": "fa-solid fa-stamp"}, {"id": "generator_power", "text": "A generator is used during a power outage", "icon": "fa-solid fa-plug"}, {"id": "door_access_card", "text": "Building doors can be unlocked with an access card", "icon": "fa-solid fa-door-open"}, {"id": "log_transfer", "text": "System logs are transferred automatically to a SIEM", "icon": "fa-solid fa-file-export"}], "correctMatches": {"guard_check": "operational", "vp_approval": "managerial", "generator_power": "physical", "door_access_card": "physical", "log_transfer": "technical"}}, "created": "2023-07-15T12:00:00Z", "lastModified": "2023-07-15T12:00:00Z", "version": 1}, {"id": "match_attack_types", "type": "matching", "difficulty": "medium", "category": "A", "tags": ["attack types", "security threats", "cybersecurity"], "questionText": "Match the description with the most accurate attack type. Not all attack types will be used.", "timeLimit": 150, "points": 10, "explanation": "Understanding different attack types is crucial for implementing appropriate security controls. Vishing involves using voice communication (phone calls) to trick victims into revealing sensitive information. Injection attacks involve inserting malicious code into data inputs (like web forms) to manipulate a database or application. An On-path attack (formerly Man-in-the-Middle) intercepts communication between two parties without their knowledge. A Distributed Denial-of-Service (DDoS) attack overwhelms a target system with traffic from multiple sources, making it unavailable. A keylogger records keystrokes on a compromised computer, often to steal login credentials or other sensitive data.", "hint": "Focus on the primary method or technique used in each attack scenario.", "tracking": {"completionStatus": "incomplete", "attempts": 0, "lastAttempt": null, "bestScore": 0}, "matchingData": {"items": [{"id": "onpath", "text": "On-path", "icon": "fa-solid fa-route"}, {"id": "keylogger", "text": "Keylogger", "icon": "fa-solid fa-keyboard"}, {"id": "rootkit", "text": "Rootkit", "icon": "fa-solid fa-bug"}, {"id": "injection", "text": "Injection", "icon": "fa-solid fa-syringe"}, {"id": "rfid", "text": "RFID cloning", "icon": "fa-solid fa-clone"}, {"id": "vishing", "text": "Vishing", "icon": "fa-solid fa-phone"}, {"id": "ddos", "text": "DDoS", "icon": "fa-solid fa-network-wired"}, {"id": "supply", "text": "Supply chain", "icon": "fa-solid fa-truck"}], "targets": [{"id": "vishing_scenario", "text": "Attacker obtains bank account number and birth date by calling the victim", "icon": "fa-solid fa-phone-volume"}, {"id": "injection_scenario", "text": "Attacker accesses a database directly from a web browser", "icon": "fa-solid fa-laptop-code"}, {"id": "onpath_scenario", "text": "Attacker intercepts all communication between a client and a web server", "icon": "fa-solid fa-arrows-left-right-to-line"}, {"id": "ddos_scenario", "text": "Multiple attackers overwhelm a web server", "icon": "fa-solid fa-server"}, {"id": "keylogger_scenario", "text": "Attacker obtains a list of all login credentials used over the last 24 hours", "icon": "fa-solid fa-keyboard"}], "correctMatches": {"vishing_scenario": ["vishing"], "injection_scenario": ["injection"], "onpath_scenario": ["onpath"], "ddos_scenario": ["ddos"], "keylogger_scenario": ["keylogger"]}, "allowMultiple": false, "requireAll": true}, "created": "2023-07-15T12:00:00Z", "lastModified": "2023-07-15T12:00:00Z", "version": 1}, {"id": "match_auth_factors", "type": "matching", "difficulty": "easy", "category": "A", "tags": ["authentication", "security factors", "access control"], "questionText": "Match the appropriate authentication factor to each description. Each authentication factor will be used once.", "timeLimit": 120, "points": 8, "explanation": "Authentication factors are important to consider when developing applications or designing network infrastructures. It's useful to know each authentication factor and some examples of how that factor can be applied during the authentication process. 'Something you know' refers to information only the user should know, like passwords or PINs. 'Something you have' refers to physical items the user possesses, like smartphones receiving OTP codes. 'Something you are' refers to biometric characteristics unique to the user, like fingerprints. 'Somewhere you are' refers to the user's location, such as being on a specific network.", "hint": "Consider what type of verification is being used in each scenario.", "tracking": {"completionStatus": "incomplete", "attempts": 0, "lastAttempt": null, "bestScore": 0}, "matchingData": {"items": [{"id": "know", "text": "Something you know", "icon": "fa-solid fa-brain"}, {"id": "have", "text": "Something you have", "icon": "fa-solid fa-mobile-screen"}, {"id": "are", "text": "Something you are", "icon": "fa-solid fa-fingerprint"}, {"id": "where", "text": "Somewhere you are", "icon": "fa-solid fa-location-dot"}], "targets": [{"id": "otp_phone", "text": "During the login process, your phone receives a text message with a one-time passcode", "icon": "fa-solid fa-message"}, {"id": "pin_atm", "text": "You enter your PIN to make a deposit into an ATM", "icon": "fa-solid fa-money-bill"}, {"id": "fingerprint_dc", "text": "You can use your fingerprint to unlock the door to the data center", "icon": "fa-solid fa-hand"}, {"id": "vpn_login", "text": "Your login will not work unless you are connected to the VPN", "icon": "fa-solid fa-shield"}], "correctMatches": {"otp_phone": ["have"], "pin_atm": ["know"], "fingerprint_dc": ["are"], "vpn_login": ["where"]}, "allowMultiple": false, "requireAll": true}, "created": "2023-07-15T12:00:00Z", "lastModified": "2023-07-15T12:00:00Z", "version": 1}, {"id": "order_incident_response", "type": "ordering", "difficulty": "medium", "category": "B", "tags": ["incident response", "process", "security operations"], "questionText": "Place the incident response activities in the correct order:", "timeLimit": 90, "points": 10, "explanation": "The standard incident response process follows these phases: Preparation (planning and setup), Detection (identifying an incident), Analysis (understanding the scope and impact), Containment (preventing spread), Eradication (removing the cause), Recovery (restoring systems), and Lessons Learned (improving future responses).", "hint": "Consider the logical flow of activities from before an incident occurs to after it's resolved.", "tracking": {"completionStatus": "incomplete", "attempts": 0, "lastAttempt": null, "bestScore": 0}, "orderingData": {"items": [{"id": "preparation", "text": "Preparation", "icon": "fa-solid fa-clipboard-list"}, {"id": "detection", "text": "Detection", "icon": "fa-solid fa-magnifying-glass"}, {"id": "analysis", "text": "Analysis", "icon": "fa-solid fa-microscope"}, {"id": "containment", "text": "Containment", "icon": "fa-solid fa-shield-virus"}, {"id": "eradication", "text": "Eradication", "icon": "fa-solid fa-virus-slash"}, {"id": "recovery", "text": "Recovery", "icon": "fa-solid fa-arrow-rotate-right"}, {"id": "lessons", "text": "Lessons learned", "icon": "fa-solid fa-book"}], "correctOrder": ["preparation", "detection", "analysis", "containment", "eradication", "recovery", "lessons"], "allowPartialCredit": true}, "created": "2023-07-15T12:00:00Z", "lastModified": "2023-07-15T12:00:00Z", "version": 1}]