/**
 * performance-ui.js
 * UI components for the performance-based question system
 */

const PerformanceUI = (function() {
  // Private variables
  let _container = null;
  let _questionText = null;
  let _questionContent = null;
  let _feedbackContainer = null;
  let _actionButtons = null;
  let _timerDisplay = null;
  let _progressBar = null;
  let _hintContainer = null;
  let _initialized = false;

  /**
   * Initialize the UI
   * @param {string} containerId - ID of the container element
   * @returns {Object} - The PerformanceUI instance
   */
  function init(containerId) {
    if (_initialized) {
      console.warn('PerformanceUI already initialized');
      return this;
    }

    // Get container element
    _container = document.getElementById(containerId);
    if (!_container) {
      console.error(`Container element with ID "${containerId}" not found`);
      return this;
    }

    // Create UI structure
    _createUIStructure();

    // Subscribe to state changes
    PerformanceState.subscribe('question:change', _handleQuestionChange);
    PerformanceState.subscribe('answer:submit', _handleAnswerSubmit);
    PerformanceState.subscribe('timer:update', _handleTimerUpdate);
    PerformanceState.subscribe('progress:update', _handleProgressUpdate);
    PerformanceState.subscribe('session:start', _handleSessionStart);
    PerformanceState.subscribe('session:progress', _handleSessionProgress);
    PerformanceState.subscribe('session:end', _handleSessionEnd);
    PerformanceState.subscribe('error', _handleError);

    // Set initialized flag
    _initialized = true;
    console.log('PerformanceUI initialized');

    return this;
  }

  /**
   * Create the UI structure
   * @private
   */
  function _createUIStructure() {
    _container.innerHTML = `
      <div class="performance-question">
        <div class="performance-progress">
          <div id="performanceProgressBar" class="performance-progress-bar" style="width: 0%"></div>
        </div>

        <div class="performance-timer hidden">
          <i class="fa-solid fa-clock performance-timer-icon"></i>
          <span id="performanceTimerDisplay" class="performance-timer-display">00:00</span>
        </div>

        <div class="question-header">
          <div class="question-meta">
            <span id="performanceQuestionCategory" class="question-category">Category</span>
            <span id="performanceQuestionDifficulty" class="question-difficulty">Medium</span>
          </div>
          <div id="performanceQuestionText" class="question-text">Loading question...</div>
        </div>

        <div id="performanceHintContainer" class="hint-container hidden">
          <button id="performanceHintToggle" class="hint-toggle">Show Hint</button>
          <div id="performanceHintContent" class="hint-content hidden"></div>
        </div>

        <div id="performanceQuestionContent" class="question-content"></div>

        <div id="performanceFeedbackContainer" class="question-feedback"></div>

        <div class="question-actions">
          <button id="performanceCheckAnswerBtn" class="check-answer-btn">Check Answer</button>
          <button id="performanceNextQuestionBtn" class="next-question-btn" disabled>Next Question</button>
          <button id="performanceHomeBtn" class="home-btn">Return Home</button>
        </div>
      </div>
    `;

    // Cache elements
    _questionText = document.getElementById('performanceQuestionText');
    _questionContent = document.getElementById('performanceQuestionContent');
    _feedbackContainer = document.getElementById('performanceFeedbackContainer');
    _timerDisplay = document.getElementById('performanceTimerDisplay');
    _progressBar = document.getElementById('performanceProgressBar');
    _hintContainer = document.getElementById('performanceHintContainer');
    _hintToggle = document.getElementById('performanceHintToggle');
    _hintContent = document.getElementById('performanceHintContent');

    // Add event listeners
    document.getElementById('performanceCheckAnswerBtn').addEventListener('click', _handleCheckAnswer);
    document.getElementById('performanceNextQuestionBtn').addEventListener('click', _handleNextQuestion);
    _hintToggle.addEventListener('click', _toggleHint);

    // Add home button event listener
    const homeBtn = document.getElementById('performanceHomeBtn');
    if (homeBtn) {
      homeBtn.addEventListener('click', function() {
        // Use the global showView function if available
        if (typeof window.showView === 'function') {
          window.showView('home');
          // Update active tab if needed
          const homeButton = document.getElementById('homeButton');
          const statsButton = document.getElementById('statsButton');
          if (homeButton) homeButton.classList.add('active');
          if (statsButton) statsButton.classList.remove('active');
        } else {
          // Fallback to direct DOM manipulation
          const performanceContainer = document.getElementById('performanceQuestionContainer');
          if (performanceContainer) {
            performanceContainer.classList.add('hidden');
          }
          const homeContainer = document.querySelector('.home-container');
          if (homeContainer) {
            homeContainer.classList.remove('hidden');
          }
        }
      });
    }
  }

  /**
   * Handle question change event
   * @param {Object} data - Event data
   * @private
   */
  function _handleQuestionChange(data) {
    const { question } = data;

    if (!question) {
      _container.classList.add('hidden');
      return;
    }

    // Update question text
    _questionText.textContent = question.questionText;

    // Update meta information
    document.getElementById('performanceQuestionCategory').textContent = question.category || 'General';

    const difficultyElement = document.getElementById('performanceQuestionDifficulty');
    difficultyElement.textContent = question.difficulty || 'Medium';
    difficultyElement.className = `question-difficulty difficulty-${(question.difficulty || 'medium').toLowerCase()}`;

    // Clear previous content and feedback
    _questionContent.innerHTML = '';
    _feedbackContainer.innerHTML = '';

    // Reset buttons
    document.getElementById('performanceCheckAnswerBtn').disabled = false;
    document.getElementById('performanceNextQuestionBtn').disabled = true;

    // Update hint
    if (question.hint) {
      _hintContent.textContent = question.hint;
      _hintContainer.classList.remove('hidden');
      _hintContent.classList.add('hidden');
      _hintToggle.textContent = 'Show Hint';
    } else {
      _hintContainer.classList.add('hidden');
    }

    // Update timer
    const timerContainer = document.querySelector('.performance-timer');
    if (question.timeLimit) {
      timerContainer.classList.remove('hidden');
      _timerDisplay.textContent = PerformanceUtils.formatTime(question.timeLimit);
    } else {
      timerContainer.classList.add('hidden');
    }

    // Render question based on type
    const questionType = question.type;
    if (window.PerformanceTypes && window.PerformanceTypes[questionType]) {
      window.PerformanceTypes[questionType].render(question, _questionContent);
    } else {
      console.error(`Unknown question type: ${questionType}`);
      _questionContent.innerHTML = '<p>Error: Unknown question type</p>';
    }

    // Show the container
    _container.classList.remove('hidden');
  }

  /**
   * Handle check answer button click
   * @private
   */
  function _handleCheckAnswer() {
    const state = PerformanceState.getState();
    const currentQuestion = state.currentQuestion;

    if (!currentQuestion) return;

    // Get current answer based on question type
    let currentAnswer = null;
    const questionType = currentQuestion.type;

    if (window.PerformanceTypes && window.PerformanceTypes[questionType]) {
      currentAnswer = window.PerformanceTypes[questionType].getCurrentAnswer(_questionContent);
    } else {
      console.error(`No answer collector found for question type: ${questionType}`);
      return;
    }

    // Submit the answer
    const result = PerformanceCore.submitAnswer(currentAnswer);

    // Show feedback
    _showFeedback(currentQuestion, currentAnswer, result);

    // Update buttons
    document.getElementById('performanceCheckAnswerBtn').disabled = true;
    document.getElementById('performanceNextQuestionBtn').disabled = false;
  }

  /**
   * Handle next question button click
   * @private
   */
  function _handleNextQuestion() {
    // Use the new session-based next question functionality
    if (window.PerformanceCore) {
      const hasNext = PerformanceCore.nextQuestion();
      if (!hasNext) {
        console.log('Session completed - no more questions');
      }
    } else {
      // Fallback to custom event
      const event = new CustomEvent('performance:nextQuestion');
      document.dispatchEvent(event);
    }
  }

  /**
   * Handle session start event
   * @param {Object} data - Event data
   * @private
   */
  function _handleSessionStart(data) {
    console.log('Session started:', data);

    // Update progress bar
    if (_progressBar) {
      _progressBar.style.width = '0%';
      _progressBar.style.backgroundColor = 'var(--warning-color)';
    }

    // Show session info if available
    if (typeof window.showFeedback === 'function') {
      window.showFeedback(`Performance session started with ${data.questionsCount} questions`);
    }
  }

  /**
   * Handle session progress event
   * @param {Object} data - Event data
   * @private
   */
  function _handleSessionProgress(data) {
    console.log('Session progress:', data);

    // Update progress bar
    if (_progressBar) {
      _progressBar.style.width = `${data.progress}%`;

      // Change color based on progress
      if (data.progress < 30) {
        _progressBar.style.backgroundColor = 'var(--warning-color)';
      } else if (data.progress < 70) {
        _progressBar.style.backgroundColor = 'var(--primary-color)';
      } else {
        _progressBar.style.backgroundColor = 'var(--success-color)';
      }
    }
  }

  /**
   * Handle session end event
   * @param {Object} data - Event data
   * @private
   */
  function _handleSessionEnd(data) {
    console.log('Session ended:', data);

    // Show completion feedback
    if (_feedbackContainer) {
      _feedbackContainer.innerHTML = `
        <div class="feedback session-complete">
          <h3>Session Complete!</h3>
          <p>Questions: ${data.questionsCompleted}/${data.totalQuestions}</p>
          <p>Correct: ${data.correctAnswers}</p>
          <p>Incorrect: ${data.incorrectAnswers}</p>
          <p>Partially Correct: ${data.partiallyCorrect}</p>
        </div>
      `;
    }

    // Disable buttons
    const checkBtn = document.getElementById('performanceCheckAnswerBtn');
    const nextBtn = document.getElementById('performanceNextQuestionBtn');
    if (checkBtn) checkBtn.disabled = true;
    if (nextBtn) nextBtn.disabled = true;
  }

  /**
   * Handle answer submission event
   * @param {Object} data - Event data
   * @private
   */
  function _handleAnswerSubmit(data) {
    const { questionId, answer, result } = data;

    // If result is provided (automatic validation), show feedback
    if (result) {
      const question = PerformanceCore.getQuestion(questionId);
      if (question) {
        _showFeedback(question, answer, result);
      }
    }
  }

  /**
   * Handle timer update event
   * @param {Object} data - Event data
   * @private
   */
  function _handleTimerUpdate(data) {
    const { timer } = data;

    if (!_timerDisplay) return;

    // Update timer display
    _timerDisplay.textContent = PerformanceUtils.formatTime(timer.remaining);

    // Update timer classes
    const timerContainer = document.querySelector('.performance-timer');
    timerContainer.classList.remove('warning', 'danger');

    if (timer.total > 0) {
      const percentRemaining = (timer.remaining / timer.total) * 100;
      if (percentRemaining <= 25) {
        timerContainer.classList.add('danger');
      } else if (percentRemaining <= 50) {
        timerContainer.classList.add('warning');
      }
    }
  }

  /**
   * Handle progress update event
   * @param {Object} data - Event data
   * @private
   */
  function _handleProgressUpdate(data) {
    const { progress } = data;
    const state = PerformanceState.getState();

    if (!_progressBar) return;

    // Use session progress if available, otherwise fall back to overall progress
    let progressPercentage = 0;

    if (state.session && state.session.isActive) {
      // Use session-based progress
      progressPercentage = ((state.session.currentQuestionIndex + 1) / state.session.questionsCount) * 100;
    } else {
      // Fall back to overall progress
      const totalQuestions = state.questions.length;
      const completedQuestions = progress.completed.length;
      progressPercentage = totalQuestions > 0 ? (completedQuestions / totalQuestions) * 100 : 0;
    }

    // Update progress bar
    _progressBar.style.width = `${progressPercentage}%`;

    // Change color based on progress
    if (progressPercentage < 30) {
      _progressBar.style.backgroundColor = 'var(--warning-color)';
    } else if (progressPercentage < 70) {
      _progressBar.style.backgroundColor = 'var(--primary-color)';
    } else {
      _progressBar.style.backgroundColor = 'var(--success-color)';
    }
  }

  /**
   * Handle error event
   * @param {Object} data - Event data
   * @private
   */
  function _handleError(data) {
    const { message } = data;
    console.error('Performance system error:', message);

    // Show error message
    if (_feedbackContainer) {
      _feedbackContainer.innerHTML = `
        <div class="feedback incorrect">
          <h3>Error</h3>
          <p>${message}</p>
        </div>
      `;
    }
  }

  /**
   * Show feedback for an answer
   * @param {Object} question - Question object
   * @param {any} answer - User's answer
   * @param {Object} result - Validation result
   * @private
   */
  function _showFeedback(question, answer, result) {
    if (!_feedbackContainer) return;

    _feedbackContainer.innerHTML = '';

    // Create feedback element
    const feedbackElement = document.createElement('div');
    feedbackElement.className = `feedback ${result.correct ? 'correct' : 'incorrect'}`;

    // Add feedback header
    const headerElement = document.createElement('h3');
    headerElement.textContent = result.correct ? 'Correct!' : 'Incorrect';
    feedbackElement.appendChild(headerElement);

    // Add score
    const scoreElement = document.createElement('p');
    scoreElement.textContent = `Score: ${Math.round(result.score)}%`;
    feedbackElement.appendChild(scoreElement);

    // Add explanation if available
    if (question.explanation) {
      const explanationElement = document.createElement('div');
      explanationElement.className = 'explanation';
      explanationElement.innerHTML = `<h4>Explanation:</h4><p>${question.explanation}</p>`;
      feedbackElement.appendChild(explanationElement);
    }

    // Add to container
    _feedbackContainer.appendChild(feedbackElement);

    // Show correct answers if incorrect
    if (!result.correct && window.PerformanceTypes && window.PerformanceTypes[question.type]) {
      window.PerformanceTypes[question.type].showCorrectAnswer(question, _questionContent);
    }
  }

  /**
   * Toggle hint visibility
   * @private
   */
  function _toggleHint() {
    if (_hintContent.classList.contains('hidden')) {
      _hintContent.classList.remove('hidden');
      _hintToggle.textContent = 'Hide Hint';
    } else {
      _hintContent.classList.add('hidden');
      _hintToggle.textContent = 'Show Hint';
    }
  }

  // Public API
  return {
    init
  };
})();

// Export for use in other modules
window.PerformanceUI = PerformanceUI;
