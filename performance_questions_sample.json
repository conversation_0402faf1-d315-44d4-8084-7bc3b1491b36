[{"id": "match_security_controls", "type": "matching", "difficulty": "medium", "category": "A", "tags": ["security controls", "physical security", "access control"], "questionText": "The security team at a manufacturing company is creating a set of security standards for employees and visitors. Select the BEST security control for each location. All of the available security controls will be used once.", "timeLimit": 180, "points": 10, "explanation": "Each security control is designed for specific security needs. Access badges are appropriate for reception areas to identify authorized personnel. Fencing and lighting secure outdoor perimeters. Access control vestibules (mantraps) and biometrics protect high-security areas like data centers. Authentication tokens provide multi-factor authentication for administrative access. Security guards provide human verification at entry points.", "hint": "Consider the security requirements and sensitivity of each location.", "matchingData": {"items": [{"id": "access_badge", "text": "Access badge", "icon": "fa-solid fa-id-card"}, {"id": "fencing", "text": "Fencing", "icon": "fa-solid fa-fence"}, {"id": "vestibule", "text": "Access control vestibule", "icon": "fa-solid fa-door-closed"}, {"id": "guard", "text": "Security guard", "icon": "fa-solid fa-user-shield"}, {"id": "token", "text": "Authentication token", "icon": "fa-solid fa-key"}, {"id": "biometrics", "text": "Biometrics", "icon": "fa-solid fa-fingerprint"}, {"id": "lighting", "text": "Lighting", "icon": "fa-solid fa-lightbulb"}], "targets": [{"id": "outside", "text": "Outside Building", "description": "Parking and Visitor drop-off", "icon": "fa-solid fa-building"}, {"id": "reception", "text": "Reception", "description": "Building lobby", "icon": "fa-solid fa-door-open"}, {"id": "data_center", "text": "Data Center Door", "description": "Entrance from inside building", "icon": "fa-solid fa-server"}, {"id": "server_admin", "text": "Server Administration", "description": "Authentication to server console in the data center", "icon": "fa-solid fa-terminal"}], "correctMatches": {"outside": ["fencing", "lighting"], "reception": ["guard", "access_badge"], "data_center": ["vestibule", "biometrics"], "server_admin": ["token"]}, "allowMultiple": true, "requireAll": true}, "created": "2023-06-15T12:00:00Z", "lastModified": "2023-06-15T12:00:00Z", "version": 1}, {"id": "match_security_categories", "type": "matching_categories", "difficulty": "medium", "category": "A", "tags": ["security controls", "categories", "management"], "questionText": "Select the most appropriate security category. Some categories may be used more than once.", "timeLimit": 120, "points": 10, "explanation": "Security controls are categorized based on their implementation and purpose. Operational controls involve people and procedures (like guard checks). Managerial controls involve policies and approvals (like VP approval). Physical controls protect physical assets (like generators). Technical controls use technology to enforce security (like access cards and SIEM systems).", "hint": "Consider how each control is implemented and what resources it protects.", "matchingData": {"categories": [{"id": "operational", "text": "Operational", "icon": "fa-solid fa-gears"}, {"id": "managerial", "text": "Managerial", "icon": "fa-solid fa-user-tie"}, {"id": "physical", "text": "Physical", "icon": "fa-solid fa-lock"}, {"id": "technical", "text": "Technical", "icon": "fa-solid fa-microchip"}], "items": [{"id": "guard_check", "text": "A guard checks the identification of all visitors", "icon": "fa-solid fa-id-card-clip"}, {"id": "vp_approval", "text": "All returns must be approved by a Vice President", "icon": "fa-solid fa-stamp"}, {"id": "generator_power", "text": "A generator is used during a power outage", "icon": "fa-solid fa-plug"}, {"id": "door_access_card", "text": "Building doors can be unlocked with an access card", "icon": "fa-solid fa-door-open"}, {"id": "log_transfer", "text": "System logs are transferred automatically to a SIEM", "icon": "fa-solid fa-file-lines"}], "correctMatches": {"guard_check": ["operational"], "vp_approval": ["managerial"], "generator_power": ["physical"], "door_access_card": ["technical", "physical"], "log_transfer": ["technical"]}, "allowMultiple": true, "requireAll": true}, "created": "2023-06-15T12:00:00Z", "lastModified": "2023-06-15T12:00:00Z", "version": 1}, {"id": "order_incident_response", "type": "ordering", "difficulty": "medium", "category": "B", "tags": ["incident response", "process", "security operations"], "questionText": "Place the incident response activities in the correct order:", "timeLimit": 90, "points": 10, "explanation": "The standard incident response process follows these phases: Preparation (planning and setup), Detection (identifying an incident), Analysis (understanding the scope and impact), Containment (preventing spread), Eradication (removing the cause), Recovery (restoring systems), and Lessons Learned (improving future responses).", "hint": "Consider the logical flow of activities from before an incident occurs to after it's resolved.", "orderingData": {"items": [{"id": "preparation", "text": "Preparation", "icon": "fa-solid fa-clipboard-list"}, {"id": "detection", "text": "Detection", "icon": "fa-solid fa-magnifying-glass"}, {"id": "analysis", "text": "Analysis", "icon": "fa-solid fa-microscope"}, {"id": "containment", "text": "Containment", "icon": "fa-solid fa-shield-virus"}, {"id": "eradication", "text": "Eradication", "icon": "fa-solid fa-virus-slash"}, {"id": "recovery", "text": "Recovery", "icon": "fa-solid fa-arrow-rotate-right"}, {"id": "lessons", "text": "Lessons learned", "icon": "fa-solid fa-book"}], "correctOrder": ["preparation", "detection", "analysis", "containment", "eradication", "recovery", "lessons"], "allowPartialCredit": true}, "created": "2023-06-15T12:00:00Z", "lastModified": "2023-06-15T12:00:00Z", "version": 1}, {"id": "firewall_simulation", "type": "simulation", "difficulty": "hard", "category": "P", "tags": ["firewall", "network security", "configuration"], "questionText": "Configure the firewall rules to allow only the specified traffic while blocking all other connections.", "timeLimit": 300, "points": 20, "explanation": "Firewall rules should follow the principle of least privilege, allowing only necessary traffic while denying everything else by default. The order of rules matters, with more specific rules placed before general ones.", "hint": "Start by identifying the required services and their port numbers.", "simulationData": {"scenario": "You are a network administrator for a company that needs to secure its internal network while allowing specific services. Configure the firewall to allow only the required traffic based on the security requirements.", "environment": {"type": "network", "initialState": {"networks": [{"id": "internal", "name": "Internal Network", "subnet": "*********/24"}, {"id": "external", "name": "Internet", "subnet": "0.0.0.0/0"}], "services": [{"id": "ssh", "name": "SSH", "port": 22, "protocol": "TCP"}, {"id": "rdp", "name": "RDP", "port": 3389, "protocol": "TCP"}, {"id": "dns", "name": "DNS", "port": 53, "protocol": "UDP"}, {"id": "web", "name": "HTTP/HTTPS", "ports": [80, 443], "protocol": "TCP"}, {"id": "ldap", "name": "LDAP", "port": 389, "protocol": "TCP"}, {"id": "ntp", "name": "NTP", "port": 123, "protocol": "UDP"}], "hosts": [{"id": "workstation", "name": "Workstation", "ip": "**********"}, {"id": "webserver", "name": "Web Server", "ip": "***********"}, {"id": "ldapserver", "name": "LDAP Server", "ip": "**********"}, {"id": "ntpserver", "name": "NTP Server", "ip": "**********"}, {"id": "dnsserver", "name": "Public DNS", "ip": "*******"}]}, "tools": ["firewall-config", "network-test"]}, "tasks": [{"id": "allow_ssh", "description": "Allow SSH connections to the workstation (**********)", "points": 4}, {"id": "block_rdp", "description": "Block all RDP connections", "points": 4}, {"id": "allow_dns", "description": "Allow DNS queries from the workstation to the public DNS server", "points": 4}, {"id": "allow_web", "description": "Allow HTTP and HTTPS access to the web server", "points": 4}, {"id": "block_ldap", "description": "Block all LDAP connections", "points": 4}], "successCriteria": {"requiredRules": [{"action": "ALLOW", "source": "ANY", "destination": "**********", "service": "SSH"}, {"action": "DENY", "source": "ANY", "destination": "ANY", "service": "RDP"}, {"action": "ALLOW", "source": "**********", "destination": "*******", "service": "DNS"}, {"action": "ALLOW", "source": "ANY", "destination": "***********", "service": "HTTP"}, {"action": "ALLOW", "source": "ANY", "destination": "***********", "service": "HTTPS"}, {"action": "DENY", "source": "ANY", "destination": "ANY", "service": "LDAP"}, {"action": "ALLOW", "source": "ANY", "destination": "**********", "service": "NTP"}], "testCases": [{"source": "EXTERNAL", "destination": "**********", "service": "SSH", "expected": "ALLOW"}, {"source": "EXTERNAL", "destination": "***********", "service": "RDP", "expected": "DENY"}, {"source": "**********", "destination": "*******", "service": "DNS", "expected": "ALLOW"}, {"source": "EXTERNAL", "destination": "***********", "service": "HTTP", "expected": "ALLOW"}, {"source": "EXTERNAL", "destination": "**********", "service": "LDAP", "expected": "DENY"}, {"source": "INTERNAL", "destination": "**********", "service": "NTP", "expected": "ALLOW"}]}}, "created": "2023-06-15T12:00:00Z", "lastModified": "2023-06-15T12:00:00Z", "version": 1}]