# Performance-Based Question System Integration Guide

This guide provides step-by-step instructions for integrating the new performance-based question system into the existing quiz application.

## Table of Contents

1. [File Structure Setup](#1-file-structure-setup)
2. [HTML Integration](#2-html-integration)
3. [CSS Integration](#3-css-integration)
4. [JavaScript Integration](#4-javascript-integration)
5. [Data Migration](#5-data-migration)
6. [Testing](#6-testing)
7. [Troubleshooting](#7-troubleshooting)

## 1. File Structure Setup

Create the following directory structure in your project:

```
/performance/
  ├── core/
  │   ├── performance-core.js
  │   ├── performance-state.js
  │   └── performance-utils.js
  ├── ui/
  │   ├── performance-ui.js
  │   ├── performance-renderer.js
  │   └── performance-animations.js
  ├── types/
  │   ├── matching.js
  │   ├── ordering.js
  │   └── simulation.js
  ├── analytics/
  │   ├── performance-tracking.js
  │   └── performance-metrics.js
  └── data/
      ├── question-schema.json
      └── performance_questions.json
```

Copy the provided implementation files to their respective locations.

## 2. HTML Integration

### 2.1 Add Container to index.html

Add the performance question container to your `index.html` file:

```html
<!-- Add this inside the quiz-container or at an appropriate location -->
<div id="performanceQuestionContainer" class="performance-container hidden"></div>
```

### 2.2 Add Script References

Add the following script references to your `index.html` file, before the closing `</body>` tag:

```html
<!-- Performance System Scripts -->
<script src="performance/core/performance-utils.js"></script>
<script src="performance/core/performance-state.js"></script>
<script src="performance/core/performance-core.js"></script>
<script src="performance/ui/performance-animations.js"></script>
<script src="performance/ui/performance-renderer.js"></script>
<script src="performance/ui/performance-ui.js"></script>
<script src="performance/types/matching.js"></script>
<script src="performance/types/ordering.js"></script>
<script src="performance/types/simulation.js"></script>
<script src="performance/analytics/performance-tracking.js"></script>
<script src="performance/analytics/performance-metrics.js"></script>
```

### 2.3 Add Progress Bar

Add the progress bar to your quiz interface:

```html
<!-- Add this near the top of your quiz container -->
<div class="performance-progress">
  <div id="performanceProgressBar" class="performance-progress-bar" style="width: 0%"></div>
</div>
```

## 3. CSS Integration

### 3.1 Add CSS Reference

Add the following link to your `index.html` file in the `<head>` section:

```html
<link rel="stylesheet" href="performance_styles.css">
```

### 3.2 Ensure Font Awesome Integration

Make sure Font Awesome is included for icons:

```html
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
```

## 4. JavaScript Integration

### 4.1 Initialize the Performance System

Add the following code to your main JavaScript file (e.g., `quiz_script.js`):

```javascript
// Initialize performance system when document is ready
document.addEventListener('DOMContentLoaded', function() {
  // Initialize performance system
  if (window.PerformanceCore) {
    PerformanceCore.init({
      showHints: true,
      allowPartialCredit: true,
      adaptiveDifficulty: false
    });
    
    PerformanceUI.init('performanceQuestionContainer');
    
    if (window.PerformanceAnalytics) {
      PerformanceAnalytics.init();
    }
  }
});
```

### 4.2 Integrate with Quiz Mode Selection

Modify your quiz mode selection handler to support performance questions:

```javascript
// In your existing quiz mode selection handler
function selectQuizMode(mode) {
  // ... existing code ...
  
  if (mode === 'performance') {
    // Hide regular quiz container
    document.querySelector('.quiz-container').classList.add('hidden');
    
    // Show performance container
    document.getElementById('performanceQuestionContainer').classList.remove('hidden');
    
    // Load a performance question
    const performanceQuestions = PerformanceCore.getQuestions();
    if (performanceQuestions.length > 0) {
      // Select a random question or first question
      const randomIndex = Math.floor(Math.random() * performanceQuestions.length);
      PerformanceCore.setCurrentQuestion(performanceQuestions[randomIndex].id);
    }
  } else {
    // Hide performance container
    document.getElementById('performanceQuestionContainer').classList.add('hidden');
    
    // Show regular quiz container
    document.querySelector('.quiz-container').classList.remove('hidden');
    
    // ... existing code for other modes ...
  }
}
```

### 4.3 Integrate with User Data System

Add performance data to your user data structure:

```javascript
// In your createDefaultUserData function or equivalent
function createDefaultUserData() {
  return {
    // ... existing user data structure ...
    
    // Add performance data
    performance: {
      completedQuestions: [],
      correctQuestions: [],
      incorrectQuestions: [],
      partiallyCorrectQuestions: [],
      questionHistory: {},
      skillMastery: {
        matching: 0,
        ordering: 0,
        simulation: 0
      }
    }
  };
}
```

### 4.4 Update Progress Bar

Add code to update the progress bar:

```javascript
// Add this function to your code
function updatePerformanceProgressBar() {
  const progressBar = document.getElementById('performanceProgressBar');
  if (!progressBar) return;
  
  const state = PerformanceState.getState();
  const totalQuestions = state.questions.length;
  const completedQuestions = state.userProgress.completed.length;
  
  if (totalQuestions > 0) {
    const progressPercentage = (completedQuestions / totalQuestions) * 100;
    progressBar.style.width = `${progressPercentage}%`;
  } else {
    progressBar.style.width = '0%';
  }
}

// Subscribe to state changes
PerformanceState.subscribe('answer:submit', updatePerformanceProgressBar);
PerformanceState.subscribe('state:change', updatePerformanceProgressBar);
```

## 5. Data Migration

### 5.1 Convert Existing Questions

Create a script to convert your existing performance questions to the new format:

```javascript
// Example conversion function
function convertLegacyQuestions() {
  // Fetch legacy questions
  fetch('performance_questions.json')
    .then(response => response.json())
    .then(legacyQuestions => {
      const convertedQuestions = legacyQuestions.map((q, index) => {
        // Base structure for all questions
        const baseQuestion = {
          id: q.id || `perf_${q.type}_${index}`,
          type: q.type,
          difficulty: q.difficulty || 'medium',
          category: q.category || 'P',
          tags: [],
          questionText: q.questionText,
          timeLimit: 180,
          points: 10,
          explanation: q.explanation || '',
          hint: '',
          created: new Date().toISOString(),
          lastModified: new Date().toISOString(),
          version: 1
        };
        
        // Type-specific conversion
        if (q.type === 'matching') {
          return {
            ...baseQuestion,
            matchingData: {
              items: q.availableControls.map(control => ({
                id: control.toLowerCase().replace(/[^a-z0-9]/g, '_'),
                text: control,
                icon: 'fa-solid fa-puzzle-piece'
              })),
              targets: q.locations.map(loc => ({
                id: loc.id,
                text: loc.name,
                description: loc.description || '',
                icon: loc.icon || 'fa-solid fa-location-dot'
              })),
              correctMatches: q.correctMatches,
              allowMultiple: true,
              requireAll: true
            }
          };
        } else if (q.type === 'ordering') {
          return {
            ...baseQuestion,
            orderingData: {
              items: q.items.map(item => ({
                id: item.toLowerCase().replace(/[^a-z0-9]/g, '_'),
                text: item,
                icon: 'fa-solid fa-arrow-down-short-wide'
              })),
              correctOrder: q.correctOrder.map(item => 
                item.toLowerCase().replace(/[^a-z0-9]/g, '_')
              ),
              allowPartialCredit: true
            }
          };
        }
        // Add other type conversions as needed
        
        return baseQuestion;
      });
      
      // Save converted questions
      console.log('Converted questions:', convertedQuestions);
      // You would typically save this to a file or localStorage
    });
}
```

### 5.2 Migrate User Performance Data

Add code to migrate existing user performance data:

```javascript
function migrateUserPerformanceData() {
  // Get all users data
  const allUsersData = JSON.parse(localStorage.getItem('quizAppUsers') || '{}');
  
  // Process each user
  Object.keys(allUsersData).forEach(nickname => {
    const userData = allUsersData[nickname];
    
    // Initialize performance data if not exists
    if (!userData.performance) {
      userData.performance = {
        completedQuestions: [],
        correctQuestions: [],
        incorrectQuestions: [],
        partiallyCorrectQuestions: [],
        questionHistory: {},
        skillMastery: {
          matching: 0,
          ordering: 0,
          simulation: 0
        }
      };
    }
    
    // Migrate question history
    if (userData.questionHistory) {
      Object.keys(userData.questionHistory).forEach(questionId => {
        const historyEntry = userData.questionHistory[questionId];
        
        // Check if it's a performance question
        const question = allQuestions.find(q => q.id === questionId);
        if (question && ['matching', 'matching_categories', 'ordering'].includes(question.type)) {
          // Add to performance history
          userData.performance.questionHistory[questionId] = {
            timestamp: historyEntry.timestamp || Date.now(),
            correct: historyEntry.correct,
            answer: historyEntry.answer,
            timeOrReason: historyEntry.timeOrReason
          };
          
          // Update lists
          if (!userData.performance.completedQuestions.includes(questionId)) {
            userData.performance.completedQuestions.push(questionId);
          }
          
          if (historyEntry.correct) {
            if (!userData.performance.correctQuestions.includes(questionId)) {
              userData.performance.correctQuestions.push(questionId);
            }
          } else {
            if (!userData.performance.incorrectQuestions.includes(questionId)) {
              userData.performance.incorrectQuestions.push(questionId);
            }
          }
        }
      });
    }
    
    // Update user data
    allUsersData[nickname] = userData;
  });
  
  // Save updated data
  localStorage.setItem('quizAppUsers', JSON.stringify(allUsersData));
}
```

## 6. Testing

### 6.1 Test Individual Components

Test each component separately:

1. Test question loading
2. Test UI rendering
3. Test drag-and-drop functionality
4. Test answer validation
5. Test progress tracking

### 6.2 Integration Testing

Test the complete flow:

1. Select performance mode
2. Load and display a question
3. Interact with the question
4. Submit an answer
5. View feedback
6. Navigate to the next question

### 6.3 Cross-Browser Testing

Test in multiple browsers:

1. Chrome
2. Firefox
3. Safari
4. Edge

### 6.4 Mobile Testing

Test on mobile devices:

1. iOS (iPhone/iPad)
2. Android phones and tablets

## 7. Troubleshooting

### 7.1 Common Issues

- **Questions not loading**: Check network requests and JSON format
- **Drag-and-drop not working**: Verify event listeners and CSS
- **Progress not updating**: Check state management and event subscriptions
- **Styling issues**: Inspect CSS conflicts with existing styles

### 7.2 Debugging Tips

- Use browser developer tools to inspect elements and state
- Add console.log statements to track data flow
- Check for JavaScript errors in the console
- Verify localStorage data structure

### 7.3 Performance Optimization

- Lazy load question types
- Optimize drag-and-drop operations
- Minimize DOM updates
- Use requestAnimationFrame for animations
