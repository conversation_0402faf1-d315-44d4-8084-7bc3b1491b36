/**
 * performance-state.js
 * State management for the performance-based question system
 */

const PerformanceState = (function() {
  // Private state object
  const state = {
    questions: [],
    currentQuestion: null,
    userAnswers: {},
    userProgress: {
      completed: [],
      correct: [],
      incorrect: [],
      partiallyCorrect: []
    },
    session: {
      questionsCount: 10, // Total questions for current session
      currentQuestionIndex: 0, // Current question index (0-based)
      sessionQuestions: [], // Questions selected for this session
      isActive: false, // Whether a session is currently active
      startTime: null, // Session start timestamp
      endTime: null // Session end timestamp
    },
    settings: {
      showHints: true,
      allowPartialCredit: true,
      adaptiveDifficulty: false,
      showExplanations: true,
      timeLimit: 0 // 0 means no time limit
    },
    ui: {
      activeView: null,
      isLoading: false,
      error: null
    },
    timer: {
      active: false,
      remaining: 0,
      total: 0
    }
  };

  // Event listeners
  const listeners = {
    'state:change': [],
    'question:change': [],
    'answer:submit': [],
    'progress:update': [],
    'timer:update': [],
    'tracking:update': [],
    'session:start': [],
    'session:progress': [],
    'session:end': [],
    'error': []
  };

  /**
   * Get the current state (immutable copy)
   * @returns {Object} - The current state
   */
  function getState() {
    return PerformanceUtils.deepClone(state);
  }

  /**
   * Set questions in the state
   * @param {Array} questions - Array of question objects
   */
  function setQuestions(questions) {
    if (!Array.isArray(questions)) {
      _notifyError('Questions must be an array');
      return;
    }

    state.questions = PerformanceUtils.deepClone(questions);
    _notifyListeners('state:change', { questions: state.questions });
  }

  /**
   * Set the current question
   * @param {string|Object} questionIdOrObject - Question ID or question object
   */
  function setCurrentQuestion(questionIdOrObject) {
    let question = null;

    if (typeof questionIdOrObject === 'string') {
      // Find question by ID
      question = state.questions.find(q => q.id === questionIdOrObject);
      if (!question) {
        _notifyError(`Question with ID ${questionIdOrObject} not found`);
        return;
      }
    } else if (typeof questionIdOrObject === 'object' && questionIdOrObject !== null) {
      // Use provided question object
      question = PerformanceUtils.deepClone(questionIdOrObject);

      // Add to questions array if not already present
      if (!state.questions.some(q => q.id === question.id)) {
        state.questions.push(question);
      }
    } else {
      _notifyError('Invalid question ID or object');
      return;
    }

    state.currentQuestion = question;

    // Reset timer if question has a time limit
    if (question.timeLimit) {
      state.timer = {
        active: true,
        remaining: question.timeLimit,
        total: question.timeLimit
      };
    } else {
      state.timer = {
        active: false,
        remaining: 0,
        total: 0
      };
    }

    _notifyListeners('question:change', { question: state.currentQuestion });
  }

  /**
   * Set user answer for a question
   * @param {string} questionId - Question ID
   * @param {any} answer - User's answer
   * @param {Object} result - Validation result (optional)
   */
  function setUserAnswer(questionId, answer, result = null) {
    // Store the answer
    state.userAnswers[questionId] = {
      answer: PerformanceUtils.deepClone(answer),
      result: result ? PerformanceUtils.deepClone(result) : null,
      timestamp: new Date().toISOString()
    };

    // Update progress
    if (!state.userProgress.completed.includes(questionId)) {
      state.userProgress.completed.push(questionId);
    }

    // Update correctness lists if result is provided
    if (result) {
      // Remove from all lists first to avoid duplicates
      state.userProgress.correct = state.userProgress.correct.filter(id => id !== questionId);
      state.userProgress.incorrect = state.userProgress.incorrect.filter(id => id !== questionId);
      state.userProgress.partiallyCorrect = state.userProgress.partiallyCorrect.filter(id => id !== questionId);

      // Add to appropriate list
      if (result.correct === true) {
        state.userProgress.correct.push(questionId);
      } else if (result.score > 0) {
        state.userProgress.partiallyCorrect.push(questionId);
      } else {
        state.userProgress.incorrect.push(questionId);
      }

      _notifyListeners('progress:update', {
        questionId,
        progress: PerformanceUtils.deepClone(state.userProgress)
      });
    }

    _notifyListeners('answer:submit', {
      questionId,
      answer: PerformanceUtils.deepClone(answer),
      result: result ? PerformanceUtils.deepClone(result) : null
    });
  }

  /**
   * Update settings
   * @param {Object} newSettings - New settings to apply
   */
  function updateSettings(newSettings) {
    if (typeof newSettings !== 'object' || newSettings === null) {
      _notifyError('Settings must be an object');
      return;
    }

    state.settings = {
      ...state.settings,
      ...newSettings
    };

    _notifyListeners('state:change', { settings: state.settings });
  }

  /**
   * Update timer state
   * @param {number} remaining - Remaining time in seconds
   */
  function updateTimer(remaining) {
    state.timer.remaining = remaining;

    if (remaining <= 0 && state.timer.active) {
      state.timer.active = false;
    }

    _notifyListeners('timer:update', { timer: { ...state.timer } });
  }

  /**
   * Set UI state
   * @param {Object} uiState - New UI state
   */
  function setUIState(uiState) {
    state.ui = {
      ...state.ui,
      ...uiState
    };

    _notifyListeners('state:change', { ui: state.ui });
  }

  /**
   * Start a new session with specified question count
   * @param {number} questionsCount - Number of questions for the session
   */
  function startSession(questionsCount = 10) {
    if (!Array.isArray(state.questions) || state.questions.length === 0) {
      _notifyError('No questions available to start session');
      return false;
    }

    // Validate question count
    const maxQuestions = state.questions.length;
    const sessionCount = Math.min(questionsCount, maxQuestions);

    // Select random questions for this session
    const shuffledQuestions = [...state.questions].sort(() => Math.random() - 0.5);
    const sessionQuestions = shuffledQuestions.slice(0, sessionCount);

    // Initialize session state
    state.session = {
      questionsCount: sessionCount,
      currentQuestionIndex: 0,
      sessionQuestions: sessionQuestions,
      isActive: true,
      startTime: Date.now(),
      endTime: null
    };

    // Reset progress for new session
    state.userProgress = {
      completed: [],
      correct: [],
      incorrect: [],
      partiallyCorrect: []
    };

    // Set first question as current
    if (sessionQuestions.length > 0) {
      setCurrentQuestion(sessionQuestions[0]);
    }

    _notifyListeners('session:start', {
      questionsCount: sessionCount,
      totalAvailable: maxQuestions
    });

    console.log(`Performance session started: ${sessionCount} questions selected`);
    return true;
  }

  /**
   * Move to the next question in the session
   * @returns {boolean} - True if moved to next question, false if session ended
   */
  function nextQuestion() {
    if (!state.session.isActive) {
      console.warn('No active session');
      return false;
    }

    const nextIndex = state.session.currentQuestionIndex + 1;

    if (nextIndex >= state.session.sessionQuestions.length) {
      // Session completed
      endSession();
      return false;
    }

    // Move to next question
    state.session.currentQuestionIndex = nextIndex;
    const nextQuestion = state.session.sessionQuestions[nextIndex];
    setCurrentQuestion(nextQuestion);

    _notifyListeners('session:progress', {
      currentIndex: nextIndex,
      totalQuestions: state.session.questionsCount,
      progress: ((nextIndex + 1) / state.session.questionsCount) * 100
    });

    return true;
  }

  /**
   * End the current session
   */
  function endSession() {
    if (!state.session.isActive) {
      console.warn('No active session to end');
      return;
    }

    state.session.isActive = false;
    state.session.endTime = Date.now();

    const sessionStats = {
      questionsCompleted: state.session.currentQuestionIndex + 1,
      totalQuestions: state.session.questionsCount,
      correctAnswers: state.userProgress.correct.length,
      incorrectAnswers: state.userProgress.incorrect.length,
      partiallyCorrect: state.userProgress.partiallyCorrect.length,
      duration: state.session.endTime - state.session.startTime
    };

    _notifyListeners('session:end', sessionStats);
    console.log('Performance session ended:', sessionStats);
  }

  /**
   * Get current session progress
   * @returns {Object} - Session progress information
   */
  function getSessionProgress() {
    if (!state.session.isActive) {
      return {
        isActive: false,
        currentIndex: 0,
        totalQuestions: 0,
        progress: 0
      };
    }

    return {
      isActive: state.session.isActive,
      currentIndex: state.session.currentQuestionIndex,
      totalQuestions: state.session.questionsCount,
      progress: ((state.session.currentQuestionIndex + 1) / state.session.questionsCount) * 100,
      questionsRemaining: state.session.questionsCount - (state.session.currentQuestionIndex + 1)
    };
  }

  /**
   * Reset the state
   */
  function resetState() {
    state.currentQuestion = null;
    state.userAnswers = {};
    state.userProgress = {
      completed: [],
      correct: [],
      incorrect: [],
      partiallyCorrect: []
    };
    state.session = {
      questionsCount: 10,
      currentQuestionIndex: 0,
      sessionQuestions: [],
      isActive: false,
      startTime: null,
      endTime: null
    };
    state.timer = {
      active: false,
      remaining: 0,
      total: 0
    };
    state.ui = {
      activeView: null,
      isLoading: false,
      error: null
    };

    _notifyListeners('state:change', { state: getState() });
  }

  /**
   * Subscribe to state changes
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   * @returns {Function} - Unsubscribe function
   */
  function subscribe(event, callback) {
    if (!listeners[event]) {
      listeners[event] = [];
    }

    listeners[event].push(callback);

    // Return unsubscribe function
    return function unsubscribe() {
      listeners[event] = listeners[event].filter(cb => cb !== callback);
    };
  }

  /**
   * Notify listeners of an event
   * @param {string} event - Event name
   * @param {Object} data - Event data
   * @private
   */
  function _notifyListeners(event, data) {
    if (listeners[event]) {
      listeners[event].forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in ${event} listener:`, error);
        }
      });
    }
  }

  /**
   * Notify error
   * @param {string} message - Error message
   * @private
   */
  function _notifyError(message) {
    state.ui.error = message;
    _notifyListeners('error', { message });
  }

  // Public API
  return {
    getState,
    setQuestions,
    setCurrentQuestion,
    setUserAnswer,
    updateSettings,
    updateTimer,
    setUIState,
    resetState,
    startSession,
    nextQuestion,
    endSession,
    getSessionProgress,
    subscribe,
    _notifyListeners // Expose for tracking updates
  };
})();

// Export for use in other modules
window.PerformanceState = PerformanceState;
