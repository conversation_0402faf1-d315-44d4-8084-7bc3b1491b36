// Gamification System for Quiz Application
// Handles achievements, XP, levels, and streaks

// Constants
const ACHIEVEMENT_STORAGE_KEY = 'quizAppAchievements';
const XP_LEVELS = [
    { level: 1, xpRequired: 0, title: "<PERSON><PERSON>" },
    { level: 2, xpRequired: 100, title: "Apprentice" },
    { level: 3, xpRequired: 250, title: "Student" },
    { level: 4, xpRequired: 500, title: "Scholar" },
    { level: 5, xpRequired: 1000, title: "Expert" },
    { level: 6, xpRequired: 2000, title: "Master" },
    { level: 7, xpRequired: 3500, title: "Grandma<PERSON>" },
    { level: 8, xpRequired: 5000, title: "<PERSON>" },
    { level: 9, xpRequired: 7500, title: "<PERSON>" },
    { level: 10, xpRequired: 10000, title: "Legendary" }
];

// XP rewards for different actions
const XP_REWARDS = {
    CORRECT_ANSWER: 10,
    QUIZ_COMPLETION: 25,
    PERFECT_SCORE: 50,
    DAILY_STREAK: 15,
    ACHIEVEMENT_UNLOCK: 20
};

// Achievement definitions
const ACHIEVEMENTS = [
    {
        id: 'first_quiz',
        name: 'First Steps',
        description: 'Complete your first quiz',
        icon: 'fa-solid fa-shoe-prints',
        xpReward: XP_REWARDS.ACHIEVEMENT_UNLOCK,
        condition: (userData) => userData.stats && userData.stats.overall && userData.stats.overall.quizzesTaken >= 1
    },
    {
        id: 'perfect_score',
        name: 'Perfect Score',
        description: 'Get 100% on a quiz with at least 5 questions',
        icon: 'fa-solid fa-star',
        xpReward: XP_REWARDS.ACHIEVEMENT_UNLOCK,
        condition: (userData) => {
            return userData.quizHistory && Array.isArray(userData.quizHistory) && userData.quizHistory.some(quiz =>
                quiz.questionsCount >= 5 && quiz.correct === quiz.questionsCount);
        }
    },
    {
        id: 'quiz_master',
        name: 'Quiz Master',
        description: 'Complete 10 quizzes',
        icon: 'fa-solid fa-crown',
        xpReward: XP_REWARDS.ACHIEVEMENT_UNLOCK,
        condition: (userData) => userData.stats && userData.stats.overall && userData.stats.overall.quizzesTaken >= 10
    },
    {
        id: 'speed_demon',
        name: 'Speed Demon',
        description: 'Complete a quiz with at least 5 questions in under 2 minutes',
        icon: 'fa-solid fa-bolt',
        xpReward: XP_REWARDS.ACHIEVEMENT_UNLOCK,
        condition: (userData) => {
            return userData.quizHistory && Array.isArray(userData.quizHistory) && userData.quizHistory.some(quiz =>
                quiz.questionsCount >= 5 && quiz.time < 120);
        }
    },
    {
        id: 'consistent_learner',
        name: 'Consistent Learner',
        description: 'Achieve a 3-day streak',
        icon: 'fa-solid fa-calendar-check',
        xpReward: XP_REWARDS.ACHIEVEMENT_UNLOCK,
        condition: (userData) => userData.gamification && userData.gamification.streak && userData.gamification.streak.current >= 3
    },
    {
        id: 'section_master_a',
        name: 'Section A Master',
        description: 'Answer at least 20 Section A questions correctly',
        icon: 'fa-solid fa-a',
        xpReward: XP_REWARDS.ACHIEVEMENT_UNLOCK,
        condition: (userData) => userData.stats && userData.stats.A && userData.stats.A.correctAnswers >= 20
    },
    {
        id: 'section_master_b',
        name: 'Section B Master',
        description: 'Answer at least 20 Section B questions correctly',
        icon: 'fa-solid fa-b',
        xpReward: XP_REWARDS.ACHIEVEMENT_UNLOCK,
        condition: (userData) => userData.stats && userData.stats.B && userData.stats.B.correctAnswers >= 20
    },
    {
        id: 'section_master_c',
        name: 'Section C Master',
        description: 'Answer at least 20 Section C questions correctly',
        icon: 'fa-solid fa-c',
        xpReward: XP_REWARDS.ACHIEVEMENT_UNLOCK,
        condition: (userData) => userData.stats && userData.stats.C && userData.stats.C.correctAnswers >= 20
    }
];

// Initialize gamification data for a user
function initializeGamificationData() {
    return {
        xp: 0,
        level: 1,
        achievements: {},
        streak: {
            current: 0,
            lastQuizDate: null
        }
    };
}

// Update user's gamification data in userData
function ensureGamificationData(userData) {
    if (!userData.gamification) {
        userData.gamification = initializeGamificationData();
    }
    return userData;
}

// Award XP to the user
function awardXP(amount, userData) {
    if (!userData) return;

    // Ensure gamification data exists
    userData = ensureGamificationData(userData);

    // Add XP
    userData.gamification.xp += amount;

    // Check for level up
    const newLevel = calculateLevel(userData.gamification.xp);
    const didLevelUp = newLevel > userData.gamification.level;
    userData.gamification.level = newLevel;

    return {
        newXP: userData.gamification.xp,
        newLevel: userData.gamification.level,
        didLevelUp
    };
}

// Calculate level based on XP
function calculateLevel(xp) {
    for (let i = XP_LEVELS.length - 1; i >= 0; i--) {
        if (xp >= XP_LEVELS[i].xpRequired) {
            return XP_LEVELS[i].level;
        }
    }
    return 1; // Default to level 1
}

// Get level title
function getLevelTitle(level) {
    const levelData = XP_LEVELS.find(l => l.level === level);
    return levelData ? levelData.title : "Unknown";
}

// Check and update achievements
function checkAchievements(userData) {
    if (!userData) return { newAchievements: [] };

    // Ensure gamification data exists
    userData = ensureGamificationData(userData);

    const newAchievements = [];

    // Check each achievement
    ACHIEVEMENTS.forEach(achievement => {
        // Skip if already achieved
        if (userData.gamification.achievements[achievement.id]) return;

        // Check if condition is met
        if (achievement.condition(userData)) {
            // Award achievement
            userData.gamification.achievements[achievement.id] = {
                dateUnlocked: new Date().toISOString(),
                name: achievement.name
            };

            // Award XP for achievement
            awardXP(achievement.xpReward, userData);

            // Add to new achievements list
            newAchievements.push(achievement);
        }
    });

    return { newAchievements };
}

// Update streak
function updateStreak(userData) {
    if (!userData) return { streakUpdated: false };

    // Ensure gamification data exists
    userData = ensureGamificationData(userData);

    const today = new Date().toDateString();
    const lastQuizDate = userData.gamification.streak.lastQuizDate;

    // If this is the first quiz ever
    if (!lastQuizDate) {
        userData.gamification.streak.current = 1;
        userData.gamification.streak.lastQuizDate = today;
        return {
            streakUpdated: true,
            newStreak: 1,
            streakMaintained: true
        };
    }

    // If already did a quiz today
    if (today === new Date(lastQuizDate).toDateString()) {
        return {
            streakUpdated: false,
            newStreak: userData.gamification.streak.current,
            streakMaintained: true
        };
    }

    // Check if last quiz was yesterday
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const wasYesterday = yesterday.toDateString() === new Date(lastQuizDate).toDateString();

    if (wasYesterday) {
        // Increment streak
        userData.gamification.streak.current += 1;
        userData.gamification.streak.lastQuizDate = today;

        // Award XP for maintaining streak
        awardXP(XP_REWARDS.DAILY_STREAK, userData);

        return {
            streakUpdated: true,
            newStreak: userData.gamification.streak.current,
            streakMaintained: true
        };
    } else {
        // Reset streak
        userData.gamification.streak.current = 1;
        userData.gamification.streak.lastQuizDate = today;

        return {
            streakUpdated: true,
            newStreak: 1,
            streakMaintained: false
        };
    }
}

// Get all achievements (for display)
function getAllAchievements() {
    return ACHIEVEMENTS;
}

// Get user's unlocked achievements
function getUnlockedAchievements(userData) {
    if (!userData || !userData.gamification || !userData.gamification.achievements) {
        return [];
    }

    return ACHIEVEMENTS.filter(achievement =>
        userData.gamification.achievements[achievement.id]);
}

// Update UI elements with gamification data
function updateUI() {
    // Call the updateXpDisplay function defined in quiz_script.js
    if (typeof window.updateXpDisplay === 'function') {
        window.updateXpDisplay();
    } else {
        console.warn('updateXpDisplay function not found. UI elements will not be updated.');
    }
}

// Export functions
window.gamification = {
    awardXP,
    checkAchievements,
    updateStreak,
    getAllAchievements,
    getUnlockedAchievements,
    getLevelTitle,
    calculateLevel,
    updateUI,
    XP_REWARDS,
    XP_LEVELS
};
