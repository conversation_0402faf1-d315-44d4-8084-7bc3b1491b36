// Text Similarity Scoring System for Flashcard Mode
// Provides fuzzy matching and similarity scoring for user text input

// Configuration for similarity scoring
const SIMILARITY_CONFIG = {
    // Minimum similarity threshold to consider an answer correct (0-1)
    CORRECT_THRESHOLD: 0.75,
    
    // Threshold for "close enough" feedback (0-1)
    CLOSE_THRESHOLD: 0.60,
    
    // Weight for different similarity algorithms
    WEIGHTS: {
        levenshtein: 0.4,
        jaro: 0.3,
        token: 0.3
    }
};

/**
 * Normalizes text for comparison by:
 * - Converting to lowercase
 * - Removing extra whitespace
 * - Removing common punctuation
 * - Handling common abbreviations
 */
function normalizeText(text) {
    if (!text || typeof text !== 'string') return '';
    
    return text
        .toLowerCase()
        .trim()
        // Remove common punctuation but keep hyphens and apostrophes in words
        .replace(/[^\w\s'-]/g, ' ')
        // Normalize whitespace
        .replace(/\s+/g, ' ')
        // Handle common abbreviations and variations
        .replace(/\b(and|&)\b/g, 'and')
        .replace(/\b(you|u)\b/g, 'you')
        .replace(/\b(to|2)\b/g, 'to')
        .replace(/\b(for|4)\b/g, 'for')
        .trim();
}

/**
 * Calculates Levenshtein distance between two strings
 * Returns a similarity score between 0 and 1 (1 = identical)
 */
function levenshteinSimilarity(str1, str2) {
    const s1 = normalizeText(str1);
    const s2 = normalizeText(str2);
    
    if (s1 === s2) return 1;
    if (s1.length === 0 || s2.length === 0) return 0;
    
    const matrix = Array(s2.length + 1).fill(null).map(() => Array(s1.length + 1).fill(null));
    
    for (let i = 0; i <= s1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= s2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= s2.length; j++) {
        for (let i = 1; i <= s1.length; i++) {
            const cost = s1[i - 1] === s2[j - 1] ? 0 : 1;
            matrix[j][i] = Math.min(
                matrix[j][i - 1] + 1,     // deletion
                matrix[j - 1][i] + 1,     // insertion
                matrix[j - 1][i - 1] + cost // substitution
            );
        }
    }
    
    const maxLength = Math.max(s1.length, s2.length);
    return 1 - (matrix[s2.length][s1.length] / maxLength);
}

/**
 * Calculates Jaro similarity between two strings
 * Returns a similarity score between 0 and 1
 */
function jaroSimilarity(str1, str2) {
    const s1 = normalizeText(str1);
    const s2 = normalizeText(str2);
    
    if (s1 === s2) return 1;
    if (s1.length === 0 || s2.length === 0) return 0;
    
    const matchWindow = Math.floor(Math.max(s1.length, s2.length) / 2) - 1;
    if (matchWindow < 0) return 0;
    
    const s1Matches = new Array(s1.length).fill(false);
    const s2Matches = new Array(s2.length).fill(false);
    
    let matches = 0;
    let transpositions = 0;
    
    // Find matches
    for (let i = 0; i < s1.length; i++) {
        const start = Math.max(0, i - matchWindow);
        const end = Math.min(i + matchWindow + 1, s2.length);
        
        for (let j = start; j < end; j++) {
            if (s2Matches[j] || s1[i] !== s2[j]) continue;
            s1Matches[i] = s2Matches[j] = true;
            matches++;
            break;
        }
    }
    
    if (matches === 0) return 0;
    
    // Find transpositions
    let k = 0;
    for (let i = 0; i < s1.length; i++) {
        if (!s1Matches[i]) continue;
        while (!s2Matches[k]) k++;
        if (s1[i] !== s2[k]) transpositions++;
        k++;
    }
    
    return (matches / s1.length + matches / s2.length + (matches - transpositions / 2) / matches) / 3;
}

/**
 * Calculates token-based similarity (Jaccard similarity of word sets)
 * Returns a similarity score between 0 and 1
 */
function tokenSimilarity(str1, str2) {
    const tokens1 = new Set(normalizeText(str1).split(/\s+/).filter(token => token.length > 0));
    const tokens2 = new Set(normalizeText(str2).split(/\s+/).filter(token => token.length > 0));
    
    if (tokens1.size === 0 && tokens2.size === 0) return 1;
    if (tokens1.size === 0 || tokens2.size === 0) return 0;
    
    const intersection = new Set([...tokens1].filter(token => tokens2.has(token)));
    const union = new Set([...tokens1, ...tokens2]);
    
    return intersection.size / union.size;
}

/**
 * Calculates overall similarity score using weighted combination of algorithms
 * Returns an object with individual scores and overall similarity
 */
function calculateSimilarity(userAnswer, correctAnswer) {
    const levenshtein = levenshteinSimilarity(userAnswer, correctAnswer);
    const jaro = jaroSimilarity(userAnswer, correctAnswer);
    const token = tokenSimilarity(userAnswer, correctAnswer);
    
    const overall = (
        levenshtein * SIMILARITY_CONFIG.WEIGHTS.levenshtein +
        jaro * SIMILARITY_CONFIG.WEIGHTS.jaro +
        token * SIMILARITY_CONFIG.WEIGHTS.token
    );
    
    return {
        levenshtein: Math.round(levenshtein * 100) / 100,
        jaro: Math.round(jaro * 100) / 100,
        token: Math.round(token * 100) / 100,
        overall: Math.round(overall * 100) / 100
    };
}

/**
 * Evaluates if an answer is correct based on similarity thresholds
 * Supports multiple correct answers (array)
 * Returns detailed evaluation result
 */
function evaluateAnswer(userAnswer, correctAnswers) {
    if (!userAnswer || typeof userAnswer !== 'string') {
        return {
            isCorrect: false,
            isClose: false,
            similarity: 0,
            bestMatch: null,
            feedback: 'Please provide an answer.'
        };
    }
    
    // Ensure correctAnswers is an array
    const answers = Array.isArray(correctAnswers) ? correctAnswers : [correctAnswers];
    
    let bestSimilarity = 0;
    let bestMatch = null;
    let bestScores = null;
    
    // Check against all possible correct answers
    for (const answer of answers) {
        if (!answer || typeof answer !== 'string') continue;
        
        const scores = calculateSimilarity(userAnswer, answer);
        if (scores.overall > bestSimilarity) {
            bestSimilarity = scores.overall;
            bestMatch = answer;
            bestScores = scores;
        }
    }
    
    const isCorrect = bestSimilarity >= SIMILARITY_CONFIG.CORRECT_THRESHOLD;
    const isClose = bestSimilarity >= SIMILARITY_CONFIG.CLOSE_THRESHOLD;
    
    let feedback = '';
    if (isCorrect) {
        if (bestSimilarity >= 0.95) {
            feedback = 'Excellent! Perfect match!';
        } else {
            feedback = `Correct! (${Math.round(bestSimilarity * 100)}% similarity)`;
        }
    } else if (isClose) {
        feedback = `Close, but not quite right. (${Math.round(bestSimilarity * 100)}% similarity)`;
    } else {
        feedback = `Not correct. (${Math.round(bestSimilarity * 100)}% similarity)`;
    }
    
    return {
        isCorrect,
        isClose,
        similarity: bestSimilarity,
        bestMatch,
        scores: bestScores,
        feedback,
        userAnswer: userAnswer.trim(),
        normalizedUser: normalizeText(userAnswer),
        normalizedCorrect: normalizeText(bestMatch || '')
    };
}

/**
 * Gets the user's text input from the flashcard input field
 */
function getFlashcardTextAnswer() {
    const textInput = document.getElementById('flashcardTextInput');
    return textInput ? textInput.value.trim() : '';
}

// Export functions for use in main quiz script
window.SimilarityScoring = {
    evaluateAnswer,
    calculateSimilarity,
    getFlashcardTextAnswer,
    normalizeText,
    SIMILARITY_CONFIG
};
