/**
 * matching.js
 * Matching question type implementation
 */

const MatchingType = (function() {
  /**
   * Render a matching question
   * @param {Object} question - Question object
   * @param {HTMLElement} container - Container element
   */
  function render(question, container) {
    if (!question || !question.matchingData || !container) {
      console.error('Invalid question or container');
      return;
    }
    
    const { matchingData } = question;
    
    // Create container elements
    const matchingContainer = document.createElement('div');
    matchingContainer.className = 'performance-matching';
    
    // Create items container
    const itemsContainer = document.createElement('div');
    itemsContainer.className = 'matching-items';
    
    // Create targets container
    const targetsContainer = document.createElement('div');
    targetsContainer.className = 'matching-targets';
    
    // Render items (shuffled)
    const shuffledItems = PerformanceUtils.shuffleArray([...matchingData.items]);
    shuffledItems.forEach(item => {
      const itemElement = document.createElement('div');
      itemElement.className = 'matching-item';
      itemElement.dataset.itemId = item.id;
      itemElement.draggable = true;
      
      // Add icon if available
      if (item.icon) {
        const iconElement = document.createElement('i');
        iconElement.className = item.icon;
        itemElement.appendChild(iconElement);
      }
      
      // Add text
      const textElement = document.createElement('span');
      textElement.textContent = item.text;
      itemElement.appendChild(textElement);
      
      // Add drag events
      itemElement.addEventListener('dragstart', handleDragStart);
      itemElement.addEventListener('dragend', handleDragEnd);
      
      itemsContainer.appendChild(itemElement);
    });
    
    // Render targets
    matchingData.targets.forEach(target => {
      const targetElement = document.createElement('div');
      targetElement.className = 'matching-target';
      targetElement.dataset.targetId = target.id;
      
      // Add header
      const headerElement = document.createElement('div');
      headerElement.className = 'matching-target-header';
      
      // Add icon if available
      if (target.icon) {
        const iconElement = document.createElement('i');
        iconElement.className = target.icon;
        headerElement.appendChild(iconElement);
      }
      
      // Add text
      const textElement = document.createElement('span');
      textElement.textContent = target.text;
      headerElement.appendChild(textElement);
      
      // Add description if available
      if (target.description) {
        const descElement = document.createElement('div');
        descElement.className = 'matching-target-description';
        descElement.textContent = target.description;
        headerElement.appendChild(descElement);
      }
      
      targetElement.appendChild(headerElement);
      
      // Add drop zone
      const dropZone = document.createElement('div');
      dropZone.className = 'matching-drop-zone';
      dropZone.dataset.targetId = target.id;
      dropZone.addEventListener('dragover', handleDragOver);
      dropZone.addEventListener('dragleave', handleDragLeave);
      dropZone.addEventListener('drop', handleDrop);
      targetElement.appendChild(dropZone);
      
      targetsContainer.appendChild(targetElement);
    });
    
    // Assemble the question
    matchingContainer.appendChild(itemsContainer);
    matchingContainer.appendChild(targetsContainer);
    container.appendChild(matchingContainer);
  }
  
  /**
   * Validate a matching answer
   * @param {Object} question - Question object
   * @param {Object} answer - User's answer
   * @returns {Object} - Validation result
   */
  function validateAnswer(question, answer) {
    if (!question || !question.matchingData || !question.matchingData.correctMatches) {
      return { correct: false, score: 0, details: { error: 'Invalid question' } };
    }
    
    if (!answer || typeof answer !== 'object') {
      return { correct: false, score: 0, details: { error: 'Invalid answer' } };
    }
    
    const { correctMatches } = question.matchingData;
    const targetIds = Object.keys(correctMatches);
    
    let totalTargets = targetIds.length;
    let correctTargets = 0;
    let partiallyCorrectTargets = 0;
    
    // Check each target
    const details = {};
    
    targetIds.forEach(targetId => {
      const correctItems = Array.isArray(correctMatches[targetId]) 
        ? [...correctMatches[targetId]].sort() 
        : [correctMatches[targetId]];
      
      const userItems = Array.isArray(answer[targetId]) 
        ? [...answer[targetId]].sort() 
        : (answer[targetId] ? [answer[targetId]] : []);
      
      // Check if arrays match exactly
      const isExactMatch = correctItems.length === userItems.length && 
                          correctItems.every((item, index) => item === userItems[index]);
      
      // Check for partial match (at least one correct item)
      const correctItemCount = userItems.filter(item => correctItems.includes(item)).length;
      const isPartialMatch = correctItemCount > 0 && !isExactMatch;
      
      if (isExactMatch) {
        correctTargets++;
      } else if (isPartialMatch) {
        partiallyCorrectTargets++;
      }
      
      // Store details for this target
      details[targetId] = {
        correctItems,
        userItems,
        isExactMatch,
        isPartialMatch,
        correctItemCount
      };
    });
    
    // Calculate score
    const exactMatchScore = (correctTargets / totalTargets) * 100;
    const partialMatchScore = (partiallyCorrectTargets / totalTargets) * 20; // 20% credit for partial matches
    const totalScore = Math.min(100, exactMatchScore + partialMatchScore);
    
    // Determine if answer is fully correct
    const isFullyCorrect = correctTargets === totalTargets;
    
    return {
      correct: isFullyCorrect,
      score: totalScore,
      details: {
        totalTargets,
        correctTargets,
        partiallyCorrectTargets,
        targetDetails: details
      }
    };
  }
  
  /**
   * Get the current answer from the UI
   * @param {HTMLElement} container - Container element
   * @returns {Object} - Current answer
   */
  function getCurrentAnswer(container) {
    if (!container) return {};
    
    const answer = {};
    const dropZones = container.querySelectorAll('.matching-drop-zone');
    
    dropZones.forEach(dropZone => {
      const targetId = dropZone.dataset.targetId;
      if (!targetId) return;
      
      const items = Array.from(dropZone.querySelectorAll('.matching-item'))
        .map(item => item.dataset.itemId);
      
      if (items.length > 0) {
        answer[targetId] = items;
      }
    });
    
    return answer;
  }
  
  /**
   * Show the correct answer in the UI
   * @param {Object} question - Question object
   * @param {HTMLElement} container - Container element
   */
  function showCorrectAnswer(question, container) {
    if (!question || !question.matchingData || !container) return;
    
    const { correctMatches } = question.matchingData;
    
    // Get all drop zones
    const dropZones = container.querySelectorAll('.matching-drop-zone');
    
    // Check each drop zone
    dropZones.forEach(dropZone => {
      const targetId = dropZone.dataset.targetId;
      if (!targetId || !correctMatches[targetId]) return;
      
      // Get correct items for this target
      const correctItems = Array.isArray(correctMatches[targetId]) 
        ? correctMatches[targetId] 
        : [correctMatches[targetId]];
      
      // Get current items in the drop zone
      const currentItems = Array.from(dropZone.querySelectorAll('.matching-item'))
        .map(item => item.dataset.itemId);
      
      // Check if all items are correct
      const allCorrect = currentItems.length === correctItems.length && 
                         currentItems.every(item => correctItems.includes(item));
      
      // Mark the drop zone as correct or incorrect
      if (allCorrect) {
        dropZone.classList.add('correct-answer');
      } else {
        dropZone.classList.add('incorrect-answer');
        
        // Show correct items
        const correctItemsHtml = correctItems.map(itemId => {
          const item = question.matchingData.items.find(i => i.id === itemId);
          return item ? item.text : itemId;
        }).join(', ');
        
        const correctLabel = document.createElement('div');
        correctLabel.className = 'correct-answer-label';
        correctLabel.innerHTML = `<strong>Correct:</strong> ${correctItemsHtml}`;
        dropZone.appendChild(correctLabel);
      }
    });
  }
  
  /**
   * Handle drag start event
   * @param {Event} event - Drag event
   */
  function handleDragStart(event) {
    event.dataTransfer.setData('text/plain', event.target.dataset.itemId);
    event.target.classList.add('dragging');
  }
  
  /**
   * Handle drag end event
   * @param {Event} event - Drag event
   */
  function handleDragEnd(event) {
    event.target.classList.remove('dragging');
    
    // Remove drag-over class from all drop zones
    document.querySelectorAll('.drag-over').forEach(el => {
      el.classList.remove('drag-over');
    });
  }
  
  /**
   * Handle drag over event
   * @param {Event} event - Drag event
   */
  function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('drag-over');
  }
  
  /**
   * Handle drag leave event
   * @param {Event} event - Drag event
   */
  function handleDragLeave(event) {
    event.currentTarget.classList.remove('drag-over');
  }
  
  /**
   * Handle drop event
   * @param {Event} event - Drag event
   */
  function handleDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('drag-over');
    
    const itemId = event.dataTransfer.getData('text/plain');
    const item = document.querySelector(`.matching-item[data-item-id="${itemId}"]`);
    
    if (item) {
      // Move the item to the drop zone
      event.currentTarget.appendChild(item);
    }
  }
  
  // Public API
  return {
    render,
    validateAnswer,
    getCurrentAnswer,
    showCorrectAnswer
  };
})();

// Register the type
if (!window.PerformanceTypes) window.PerformanceTypes = {};
window.PerformanceTypes.matching = MatchingType;
