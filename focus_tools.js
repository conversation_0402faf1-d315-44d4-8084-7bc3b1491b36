// Focus Tools for Quiz Application
// Includes Pomodoro Timer and Focus Mode

// Constants
const POMODORO_SETTINGS_KEY = 'quizAppPomodoroSettings';
const AMBIENT_SOUNDS_KEY = 'quizAppAmbientSounds';

// Default Pomodoro settings
const DEFAULT_POMODORO_SETTINGS = {
    focusDuration: 25, // minutes
    shortBreakDuration: 5, // minutes
    longBreakDuration: 15, // minutes
    sessionsBeforeLongBreak: 4,
    autoStartBreaks: true,
    autoStartPomodoros: false
};

// Ambient sound options
const AMBIENT_SOUNDS = [
    { id: 'none', name: 'No Sound', file: null },
    { id: 'rain', name: 'Rainfall', file: 'sounds/rain.mp3' },
    { id: 'cafe', name: 'Coffee Shop', file: 'sounds/cafe.mp3' },
    { id: 'nature', name: 'Nature', file: 'sounds/nature.mp3' },
    { id: 'lofi', name: 'Lo-Fi Music', file: 'sounds/lofi.mp3' },
    { id: 'whitenoise', name: 'White Noise', file: 'sounds/whitenoise.mp3' }
];

// Pomodoro timer state
let pomodoroState = {
    isActive: false,
    isBreak: false,
    isLongBreak: false,
    currentSession: 1,
    timeRemaining: 0, // in seconds
    timerInterval: null,
    settings: { ...DEFAULT_POMODORO_SETTINGS }
};

// Ambient sound state
let ambientSoundState = {
    currentSound: 'none',
    volume: 0.5,
    isPlaying: false,
    audioElement: null
};

// DOM Elements (to be set when initializing)
let pomodoroTimerElement;
let pomodoroStatusElement;
let pomodoroControlsElement;
let focusModeToggleElement;
let ambientSoundControlsElement;

// Initialize Pomodoro timer
function initPomodoro() {
    // Cache DOM elements
    pomodoroTimerElement = document.getElementById('pomodoroTimer');
    pomodoroStatusElement = document.getElementById('pomodoroStatus');
    pomodoroControlsElement = document.getElementById('pomodoroControls');
    focusModeToggleElement = document.getElementById('focusModeToggle');
    ambientSoundControlsElement = document.getElementById('ambientSoundOptions');

    // Load saved settings if available
    const savedSettings = localStorage.getItem(POMODORO_SETTINGS_KEY);
    if (savedSettings) {
        try {
            const parsedSettings = JSON.parse(savedSettings);
            pomodoroState.settings = { ...DEFAULT_POMODORO_SETTINGS, ...parsedSettings };
        } catch (e) {
            console.error('Error parsing saved Pomodoro settings:', e);
            pomodoroState.settings = { ...DEFAULT_POMODORO_SETTINGS };
        }
    }

    // Initialize timer with focus duration
    resetPomodoroTimer();

    // Set up initial controls
    updatePomodoroControls();
}

// Reset Pomodoro timer based on current state
function resetPomodoroTimer() {
    clearInterval(pomodoroState.timerInterval);

    if (pomodoroState.isBreak) {
        if (pomodoroState.isLongBreak) {
            pomodoroState.timeRemaining = pomodoroState.settings.longBreakDuration * 60;
        } else {
            pomodoroState.timeRemaining = pomodoroState.settings.shortBreakDuration * 60;
        }
    } else {
        pomodoroState.timeRemaining = pomodoroState.settings.focusDuration * 60;
    }

    updatePomodoroDisplay();
}

// Start Pomodoro timer
function startPomodoroTimer() {
    if (pomodoroState.isActive) return;

    pomodoroState.isActive = true;
    pomodoroState.timerInterval = setInterval(updatePomodoroTime, 1000);

    updatePomodoroControls();
    updatePomodoroDisplay();
}

// Pause Pomodoro timer
function pausePomodoroTimer() {
    if (!pomodoroState.isActive) return;

    pomodoroState.isActive = false;
    clearInterval(pomodoroState.timerInterval);

    updatePomodoroControls();
}

// Update Pomodoro time (called every second when active)
function updatePomodoroTime() {
    if (pomodoroState.timeRemaining <= 0) {
        handlePomodoroComplete();
        return;
    }

    pomodoroState.timeRemaining--;
    updatePomodoroDisplay();
}

// Handle Pomodoro session completion
function handlePomodoroComplete() {
    pausePomodoroTimer();

    // Play notification sound
    playNotificationSound();

    if (pomodoroState.isBreak) {
        // Break is over, start a new focus session
        pomodoroState.isBreak = false;

        // Auto-start next pomodoro if enabled
        if (pomodoroState.settings.autoStartPomodoros) {
            resetPomodoroTimer();
            startPomodoroTimer();
        } else {
            resetPomodoroTimer();
        }
    } else {
        // Focus session is over, start a break
        pomodoroState.isBreak = true;

        // Check if it should be a long break
        if (pomodoroState.currentSession >= pomodoroState.settings.sessionsBeforeLongBreak) {
            pomodoroState.isLongBreak = true;
            pomodoroState.currentSession = 1; // Reset session count
        } else {
            pomodoroState.isLongBreak = false;
            pomodoroState.currentSession++;
        }

        // Auto-start break if enabled
        if (pomodoroState.settings.autoStartBreaks) {
            resetPomodoroTimer();
            startPomodoroTimer();
        } else {
            resetPomodoroTimer();
        }
    }

    // Show notification
    showPomodoroNotification();
}

// Format time for display (mm:ss)
function formatTime(seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

// Update Pomodoro display
function updatePomodoroDisplay() {
    if (!pomodoroTimerElement || !pomodoroStatusElement) return;

    pomodoroTimerElement.textContent = formatTime(pomodoroState.timeRemaining);

    let statusText = pomodoroState.isBreak
        ? (pomodoroState.isLongBreak ? 'Long Break' : 'Short Break')
        : 'Focus Time';

    pomodoroStatusElement.textContent = statusText;

    // Update document title to show timer
    document.title = `${formatTime(pomodoroState.timeRemaining)} - ${statusText} | Quiz App`;
}

// Update Pomodoro controls
function updatePomodoroControls() {
    if (!pomodoroControlsElement) return;

    // Clear existing controls
    pomodoroControlsElement.innerHTML = '';

    // Create appropriate buttons based on state
    if (pomodoroState.isActive) {
        const pauseButton = document.createElement('button');
        pauseButton.className = 'pomodoro-button';
        pauseButton.innerHTML = '<i class="fa-solid fa-pause"></i> Pause';
        pauseButton.addEventListener('click', pausePomodoroTimer);
        pomodoroControlsElement.appendChild(pauseButton);
    } else {
        const startButton = document.createElement('button');
        startButton.className = 'pomodoro-button';
        startButton.innerHTML = '<i class="fa-solid fa-play"></i> Start';
        startButton.addEventListener('click', startPomodoroTimer);
        pomodoroControlsElement.appendChild(startButton);
    }

    // Add reset button
    const resetButton = document.createElement('button');
    resetButton.className = 'pomodoro-button';
    resetButton.innerHTML = '<i class="fa-solid fa-rotate"></i> Reset';
    resetButton.addEventListener('click', resetPomodoroTimer);
    pomodoroControlsElement.appendChild(resetButton);

    // Add skip button
    const skipButton = document.createElement('button');
    skipButton.className = 'pomodoro-button';
    skipButton.innerHTML = '<i class="fa-solid fa-forward"></i> Skip';
    skipButton.addEventListener('click', handlePomodoroComplete);
    pomodoroControlsElement.appendChild(skipButton);
}

// Show Pomodoro notification
function showPomodoroNotification() {
    const message = pomodoroState.isBreak
        ? `Time for a ${pomodoroState.isLongBreak ? 'long' : 'short'} break!`
        : 'Break over! Time to focus!';

    // Use the app's feedback system if available
    if (typeof showFeedback === 'function') {
        showFeedback(message);
    } else {
        alert(message);
    }
}

// Play notification sound
function playNotificationSound() {
    const audio = new Audio('sounds/notification.mp3');
    audio.play().catch(e => console.log('Error playing notification sound:', e));
}

// Toggle focus mode
function toggleFocusMode() {
    const body = document.body;
    const isFocusModeActive = body.classList.contains('focus-mode');

    // Toggle focus mode class
    if (isFocusModeActive) {
        body.classList.remove('focus-mode');
        // Show all containers that might have been hidden
        const containers = document.querySelectorAll('.home-container, .stats-container, .select-question-container, .acronyms-container, .user-container');
        containers.forEach(container => {
            container.style.display = '';
        });
    } else {
        body.classList.add('focus-mode');
        // Only hide containers if we're in quiz view
        if (document.querySelector('.quiz-container:not(.hidden)')) {
            const containers = document.querySelectorAll('.home-container, .stats-container, .select-question-container, .acronyms-container, .user-container');
            containers.forEach(container => {
                container.style.display = 'none';
            });
        }
    }

    // Update toggle button if it exists
    if (focusModeToggleElement) {
        if (body.classList.contains('focus-mode')) {
            focusModeToggleElement.innerHTML = '<i class="fa-solid fa-eye"></i> Exit Focus Mode';
        } else {
            focusModeToggleElement.innerHTML = '<i class="fa-solid fa-eye-slash"></i> Enter Focus Mode';
        }
    }

    // Show feedback to user
    if (typeof showFeedback === 'function') {
        showFeedback(body.classList.contains('focus-mode') ?
            'Focus mode enabled. Press ESC or click the button again to exit.' :
            'Focus mode disabled.');
    }
}

// Initialize ambient sounds
function initAmbientSounds() {
    // Load saved sound preference if available
    const savedSound = localStorage.getItem(AMBIENT_SOUNDS_KEY);
    if (savedSound) {
        try {
            const soundData = JSON.parse(savedSound);
            ambientSoundState.currentSound = soundData.id || 'none';
            ambientSoundState.volume = soundData.volume || 0.5;
        } catch (e) {
            console.error('Error parsing saved ambient sound settings:', e);
        }
    }
}

// Play ambient sound
function playAmbientSound(soundId, volume = 0.5) {
    // Stop current sound if playing
    stopAmbientSound();

    if (soundId === 'none') return;

    // Find sound data
    const soundData = AMBIENT_SOUNDS.find(sound => sound.id === soundId);
    if (!soundData || !soundData.file) return;

    try {
        // Create audio element
        ambientSoundState.audioElement = new Audio(soundData.file);
        ambientSoundState.audioElement.loop = true;
        ambientSoundState.audioElement.volume = volume;
        ambientSoundState.currentSound = soundId;
        ambientSoundState.volume = volume;
        ambientSoundState.isPlaying = true;

        // Save preference
        localStorage.setItem(AMBIENT_SOUNDS_KEY, JSON.stringify({
            id: soundId,
            volume: volume
        }));

        // Play the sound
        ambientSoundState.audioElement.play().catch(e => {
            console.error('Error playing ambient sound:', e);
            ambientSoundState.isPlaying = false;

            // Show feedback to user
            if (typeof showFeedback === 'function') {
                showFeedback(`Sound files not available. In a real implementation, you would need actual MP3 files.`);
            } else {
                alert('Sound files not available. In a real implementation, you would need actual MP3 files.');
            }
        });

        // Update controls if they exist
        updateAmbientSoundControls();
    } catch (e) {
        console.error('Error setting up ambient sound:', e);
        ambientSoundState.isPlaying = false;

        // Show feedback to user
        if (typeof showFeedback === 'function') {
            showFeedback(`Sound files not available. In a real implementation, you would need actual MP3 files.`);
        } else {
            alert('Sound files not available. In a real implementation, you would need actual MP3 files.');
        }
    }
}

// Stop ambient sound
function stopAmbientSound() {
    if (ambientSoundState.audioElement && ambientSoundState.isPlaying) {
        ambientSoundState.audioElement.pause();
        ambientSoundState.audioElement = null;
        ambientSoundState.isPlaying = false;

        // Update controls if they exist
        updateAmbientSoundControls();
    }
}

// Update ambient sound controls
function updateAmbientSoundControls() {
    if (!ambientSoundControlsElement) return;

    // Implementation will depend on the HTML structure
    // This is a placeholder for the actual implementation
}

// Export functions
window.focusTools = {
    initPomodoro,
    startPomodoroTimer,
    pausePomodoroTimer,
    resetPomodoroTimer,
    toggleFocusMode,
    initAmbientSounds,
    playAmbientSound,
    stopAmbientSound,
    AMBIENT_SOUNDS
};
