{"version": "1.0", "lastUpdated": "2023-07-15", "questionSets": [{"id": "security_fundamentals", "name": "Security Fundamentals", "description": "Basic security concepts and principles", "questions": ["match_security_locations", "match_security_categories", "match_attack_types", "match_auth_factors"]}, {"id": "security_implementation", "name": "Security Implementation", "description": "Implementing security controls and technologies", "questions": ["match_mobile_security", "match_certificate_concepts", "order_incident_response", "match_security_tech", "match_data_states"]}, {"id": "security_operations", "name": "Security Operations", "description": "Operational aspects of security", "questions": ["match_firewall_rules", "match_network_devices", "match_attack_characteristics", "match_crypto_tech", "match_security_scenarios"]}], "categories": [{"id": "A", "name": "Security Fundamentals", "description": "Basic security concepts and principles"}, {"id": "B", "name": "Security Implementation", "description": "Implementing security controls and technologies"}, {"id": "C", "name": "Security Operations", "description": "Operational aspects of security"}], "difficultyLevels": [{"id": "easy", "name": "Easy", "description": "Basic concepts and straightforward applications"}, {"id": "medium", "name": "Medium", "description": "More complex concepts requiring deeper understanding"}, {"id": "hard", "name": "Hard", "description": "Advanced concepts requiring comprehensive knowledge"}], "tags": ["security controls", "physical security", "access control", "operational security", "managerial security", "technical security", "attack types", "security threats", "cybersecurity", "authentication", "security factors", "incident response", "process", "security operations", "mobile security", "device security", "cryptography", "certificates", "PKI", "data security", "data states", "data protection", "network security", "firewall", "traffic filtering", "network devices", "security appliances", "security technologies"]}