/* Performance-Based Question System Styles */

:root {
  /* Light Mode Variables */
  --performance-bg: #f8f9fa;
  --performance-text: #212529;
  --performance-border: #dee2e6;
  --performance-primary: #0d6efd;
  --performance-secondary: #6c757d;
  --performance-success: #198754;
  --performance-danger: #dc3545;
  --performance-warning: #ffc107;
  --performance-info: #0dcaf0;
  --performance-light: #f8f9fa;
  --performance-dark: #212529;
  --performance-shadow: rgba(0, 0, 0, 0.1);
  --performance-card-bg: #ffffff;
  --performance-item-bg: #e9ecef;
  --performance-item-hover: #ced4da;
  --performance-target-bg: #f1f3f5;
  --performance-target-border: #adb5bd;
  --performance-drop-zone-bg: #e9ecef;
  --performance-drop-zone-hover: #ced4da;
  --performance-drop-zone-active: #adb5bd;
  --performance-correct: #d1e7dd;
  --performance-incorrect: #f8d7da;
  --performance-hint: #cff4fc;
  --performance-explanation: #e2e3e5;
}

/* Dark Mode Variables */
.dark-mode {
  --performance-bg: #212529;
  --performance-text: #f8f9fa;
  --performance-border: #495057;
  --performance-primary: #0d6efd;
  --performance-secondary: #6c757d;
  --performance-success: #198754;
  --performance-danger: #dc3545;
  --performance-warning: #ffc107;
  --performance-info: #0dcaf0;
  --performance-light: #343a40;
  --performance-dark: #f8f9fa;
  --performance-shadow: rgba(0, 0, 0, 0.5);
  --performance-card-bg: #343a40;
  --performance-item-bg: #495057;
  --performance-item-hover: #6c757d;
  --performance-target-bg: #343a40;
  --performance-target-border: #6c757d;
  --performance-drop-zone-bg: #495057;
  --performance-drop-zone-hover: #6c757d;
  --performance-drop-zone-active: #adb5bd;
  --performance-correct: #0f5132;
  --performance-incorrect: #842029;
  --performance-hint: #055160;
  --performance-explanation: #41464b;
}

/* Container Styles */
.performance-container {
  background-color: var(--performance-bg);
  color: var(--performance-text);
  border-radius: 0.5rem;
  box-shadow: 0 0.25rem 0.5rem var(--performance-shadow);
  padding: 1.5rem;
  margin: 1.5rem 0;
  max-width: 900px;
  width: 100%;
  transition: all 0.3s ease;
}

.performance-tracking-container {
  background-color: var(--performance-bg);
  color: var(--performance-text);
  border-radius: 0.5rem;
  box-shadow: 0 0.25rem 0.5rem var(--performance-shadow);
  padding: 1rem;
  margin: 1.5rem 0;
  max-width: 900px;
  width: 100%;
  transition: all 0.3s ease;
}

/* Question Header */
.performance-question .question-header {
  margin-bottom: 1.5rem;
}

.performance-question .question-meta {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.performance-question .question-category,
.performance-question .question-difficulty {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.performance-question .question-category {
  background-color: var(--performance-primary);
  color: white;
}

.performance-question .question-difficulty {
  background-color: var(--performance-secondary);
  color: white;
}

.performance-question .question-difficulty.easy {
  background-color: var(--performance-success);
}

.performance-question .question-difficulty.medium {
  background-color: var(--performance-warning);
  color: var(--performance-dark);
}

.performance-question .question-difficulty.hard {
  background-color: var(--performance-danger);
}

.performance-question .question-text {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 1rem;
}

/* Question Content */
.performance-question .question-content {
  background-color: var(--performance-card-bg);
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 0.125rem 0.25rem var(--performance-shadow);
}

/* Matching Question Styles */
.performance-matching {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

@media (max-width: 768px) {
  .performance-matching {
    grid-template-columns: 1fr;
  }
}

.matching-items,
.matching-targets {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.matching-item {
  background-color: var(--performance-item-bg);
  border-radius: 0.25rem;
  padding: 0.75rem 1rem;
  cursor: grab;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  user-select: none;
}

.matching-item:hover {
  background-color: var(--performance-item-hover);
}

.matching-item.dragging {
  opacity: 0.5;
}

.matching-item i {
  font-size: 1.25rem;
  color: var(--performance-primary);
}

.matching-target {
  border: 1px solid var(--performance-target-border);
  border-radius: 0.25rem;
  overflow: hidden;
}

.matching-target-header {
  background-color: var(--performance-target-bg);
  padding: 0.75rem 1rem;
  font-weight: 600;
  border-bottom: 1px solid var(--performance-target-border);
}

.matching-drop-zone {
  min-height: 5rem;
  padding: 0.75rem 1rem;
  background-color: var(--performance-drop-zone-bg);
  transition: all 0.2s ease;
}

.matching-drop-zone.drag-over {
  background-color: var(--performance-drop-zone-hover);
}

/* Ordering Question Styles */
.ordering-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

@media (max-width: 768px) {
  .ordering-container {
    grid-template-columns: 1fr;
  }
}

.ordering-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.ordering-item {
  background-color: var(--performance-item-bg);
  border-radius: 0.25rem;
  padding: 0.75rem 1rem;
  cursor: grab;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  user-select: none;
}

.ordering-item:hover {
  background-color: var(--performance-item-hover);
}

.ordering-target-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.ordering-drop-zone {
  background-color: var(--performance-drop-zone-bg);
  border: 1px dashed var(--performance-target-border);
  border-radius: 0.25rem;
  min-height: 3rem;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.ordering-drop-zone.drag-over {
  background-color: var(--performance-drop-zone-hover);
}

.ordering-drop-zone.filled {
  border-style: solid;
}

.ordering-position {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.ordering-position-number {
  background-color: var(--performance-primary);
  color: white;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

/* Simulation Question Styles */
.simulation-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.simulation-scenario {
  background-color: var(--performance-light);
  border-radius: 0.25rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

.simulation-workspace {
  display: grid;
  grid-template-columns: 1fr 3fr;
  gap: 1rem;
  height: 400px;
}

@media (max-width: 768px) {
  .simulation-workspace {
    grid-template-columns: 1fr;
    height: auto;
  }
}

.simulation-tools {
  background-color: var(--performance-light);
  border-radius: 0.25rem;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  overflow-y: auto;
}

.simulation-tool {
  background-color: var(--performance-item-bg);
  border-radius: 0.25rem;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.simulation-tool:hover {
  background-color: var(--performance-item-hover);
}

.simulation-canvas {
  background-color: var(--performance-card-bg);
  border: 1px solid var(--performance-border);
  border-radius: 0.25rem;
  overflow: auto;
  position: relative;
}

/* Feedback Styles */
.question-feedback {
  margin-bottom: 1.5rem;
}

.feedback {
  border-radius: 0.25rem;
  padding: 1rem;
  animation: fadeIn 0.3s ease;
}

.feedback.correct {
  background-color: var(--performance-correct);
  color: var(--performance-dark);
}

.feedback.incorrect {
  background-color: var(--performance-incorrect);
  color: var(--performance-dark);
}

.feedback h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
}

.explanation {
  background-color: var(--performance-explanation);
  border-radius: 0.25rem;
  padding: 1rem;
  margin-top: 1rem;
}

.explanation h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

/* Hint Styles */
.hint-container {
  background-color: var(--performance-hint);
  border-radius: 0.25rem;
  padding: 1rem;
  margin-bottom: 1rem;
  color: var(--performance-dark);
}

.hint-toggle {
  background: none;
  border: none;
  color: var(--performance-primary);
  cursor: pointer;
  padding: 0;
  font-size: 0.875rem;
  text-decoration: underline;
}

.hint-content {
  margin-top: 0.5rem;
}

/* Action Buttons */
.question-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.question-actions button {
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.check-answer-btn {
  background-color: var(--performance-primary);
  color: white;
}

.check-answer-btn:hover {
  background-color: #0b5ed7;
}

.next-question-btn {
  background-color: var(--performance-secondary);
  color: white;
}

.next-question-btn:hover {
  background-color: #5c636a;
}

.question-actions button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Progress Bar */
.performance-progress {
  height: 0.5rem;
  background-color: var(--performance-light);
  border-radius: 0.25rem;
  margin-bottom: 1rem;
  overflow: hidden;
}

.performance-progress-bar {
  height: 100%;
  background-color: var(--performance-primary);
  transition: width 0.3s ease;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.dragging {
  opacity: 0.5;
}

.drag-over {
  background-color: var(--performance-drop-zone-hover) !important;
}

.correct-answer {
  border-color: var(--performance-success) !important;
  background-color: var(--performance-correct) !important;
}

.incorrect-answer {
  border-color: var(--performance-danger) !important;
  background-color: var(--performance-incorrect) !important;
}
