/* Theme Customization for Quiz Application */

/* Base Theme Variables (Default/Standard) */
.theme-standard {
    --primary-color: #007BFF;
    --primary-hover-color: #0056b3;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    
    --card-hover-bg: #10ebe7;
    --card-hover-text: #000;
    
    --gradient-start: #4158D0;
    --gradient-middle: #C850C0;
    --gradient-end: #FFCC70;
    
    --achievement-bg: rgba(255, 255, 255, 0.1);
    --achievement-border: rgba(255, 255, 255, 0.2);
}

/* Cyberpunk Theme */
.theme-cyberpunk {
    --primary-color: #00f3ff;
    --primary-hover-color: #00c8d4;
    --secondary-color: #ff00a0;
    --success-color: #00ff9d;
    --danger-color: #ff3e3e;
    --warning-color: #ffcc00;
    --info-color: #00b8ff;
    
    --card-hover-bg: #ff00a0;
    --card-hover-text: #000;
    
    --gradient-start: #000428;
    --gradient-middle: #004e92;
    --gradient-end: #00c8ff;
    
    --achievement-bg: rgba(0, 243, 255, 0.1);
    --achievement-border: rgba(0, 243, 255, 0.3);
}

/* Nature Theme */
.theme-nature {
    --primary-color: #4CAF50;
    --primary-hover-color: #388E3C;
    --secondary-color: #8D6E63;
    --success-color: #66BB6A;
    --danger-color: #e57373;
    --warning-color: #FFA726;
    --info-color: #4FC3F7;
    
    --card-hover-bg: #81C784;
    --card-hover-text: #1B5E20;
    
    --gradient-start: #134E5E;
    --gradient-middle: #71B280;
    --gradient-end: #A8E063;
    
    --achievement-bg: rgba(76, 175, 80, 0.1);
    --achievement-border: rgba(76, 175, 80, 0.3);
}

/* Space Theme */
.theme-space {
    --primary-color: #7E57C2;
    --primary-hover-color: #5E35B1;
    --secondary-color: #5C6BC0;
    --success-color: #26A69A;
    --danger-color: #EF5350;
    --warning-color: #FFCA28;
    --info-color: #29B6F6;
    
    --card-hover-bg: #B39DDB;
    --card-hover-text: #311B92;
    
    --gradient-start: #000000;
    --gradient-middle: #434343;
    --gradient-end: #7E57C2;
    
    --achievement-bg: rgba(126, 87, 194, 0.1);
    --achievement-border: rgba(126, 87, 194, 0.3);
}

/* Retro Theme */
.theme-retro {
    --primary-color: #FF5722;
    --primary-hover-color: #E64A19;
    --secondary-color: #607D8B;
    --success-color: #8BC34A;
    --danger-color: #F44336;
    --warning-color: #FFC107;
    --info-color: #03A9F4;
    
    --card-hover-bg: #FFAB91;
    --card-hover-text: #BF360C;
    
    --gradient-start: #FF8008;
    --gradient-middle: #FFC837;
    --gradient-end: #FF5722;
    
    --achievement-bg: rgba(255, 87, 34, 0.1);
    --achievement-border: rgba(255, 87, 34, 0.3);
}

/* Apply theme-specific background gradients */
.theme-standard body {
    background: linear-gradient(135deg, var(--gradient-start), var(--gradient-middle), var(--gradient-end)) fixed;
}

.theme-cyberpunk body {
    background: linear-gradient(135deg, var(--gradient-start), var(--gradient-middle), var(--gradient-end)) fixed;
}

.theme-nature body {
    background: linear-gradient(135deg, var(--gradient-start), var(--gradient-middle), var(--gradient-end)) fixed;
}

.theme-space body {
    background: linear-gradient(135deg, var(--gradient-start), var(--gradient-middle), var(--gradient-end)) fixed;
}

.theme-retro body {
    background: linear-gradient(135deg, var(--gradient-start), var(--gradient-middle), var(--gradient-end)) fixed;
}

/* Theme-specific card styles */
.theme-cyberpunk .quiz-mode-card {
    border: 1px solid var(--primary-color);
    box-shadow: 0 0 10px rgba(0, 243, 255, 0.3);
}

.theme-nature .quiz-mode-card {
    border: 1px solid var(--primary-color);
    box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

.theme-space .quiz-mode-card {
    border: 1px solid var(--primary-color);
    box-shadow: 0 0 10px rgba(126, 87, 194, 0.3);
}

.theme-retro .quiz-mode-card {
    border: 1px solid var(--primary-color);
    box-shadow: 0 0 10px rgba(255, 87, 34, 0.3);
}

/* Theme-specific button styles */
.theme-cyberpunk .button {
    text-shadow: 0 0 5px var(--primary-color);
}

.theme-space .button {
    background: rgba(126, 87, 194, 0.2);
    backdrop-filter: blur(5px);
}

/* Achievement styles for different themes */
.achievement-card {
    background: var(--achievement-bg);
    border: 1px solid var(--achievement-border);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    transition: transform 0.3s, box-shadow 0.3s;
}

.achievement-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.achievement-icon {
    font-size: 24px;
    margin-right: 15px;
    color: var(--primary-color);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.achievement-info {
    flex: 1;
}

.achievement-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.achievement-description {
    font-size: 0.9em;
    opacity: 0.8;
}

.achievement-locked {
    opacity: 0.5;
    filter: grayscale(1);
}

/* Focus mode styles */
.focus-mode .sidebar {
    transform: translateX(-100%);
}

.focus-mode .main-content {
    margin-left: 0;
    width: 100%;
}

.focus-mode .quiz-container {
    max-width: 800px;
    margin: 2rem auto;
}

.focus-mode .home-container,
.focus-mode .stats-container,
.focus-mode .select-question-container,
.focus-mode .acronyms-container,
.focus-mode .user-container {
    display: none;
}

.focus-mode #quizHeader {
    opacity: 0.3;
    transition: opacity 0.3s;
}

.focus-mode #quizHeader:hover {
    opacity: 1;
}

/* Pomodoro timer styles */
.pomodoro-container {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    text-align: center;
}

.pomodoro-timer {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 10px 0;
    color: var(--primary-color);
}

.pomodoro-status {
    font-size: 1.2rem;
    margin-bottom: 15px;
}

.pomodoro-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.pomodoro-button {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 15px;
    cursor: pointer;
    transition: background 0.3s;
}

.pomodoro-button:hover {
    background: var(--primary-hover-color);
}

/* XP and level display */
.xp-container {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.xp-level {
    background: var(--primary-color);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
}

.xp-bar-container {
    flex: 1;
    height: 10px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    overflow: hidden;
}

.xp-bar {
    height: 100%;
    background: var(--primary-color);
    width: 0%; /* Will be set via JS */
}

.xp-text {
    margin-left: 10px;
    font-size: 0.9em;
}

/* Streak display */
.streak-container {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.streak-flame {
    color: #FF9800;
    font-size: 24px;
    margin-right: 10px;
}

.streak-count {
    font-weight: bold;
    font-size: 1.2em;
}

.streak-label {
    margin-left: 5px;
    opacity: 0.8;
}

/* Ambient sound controls */
.ambient-sound-container {
    margin-top: 20px;
}

.ambient-sound-title {
    font-size: 1.1em;
    margin-bottom: 10px;
}

.ambient-sound-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.ambient-sound-option {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    padding: 8px 15px;
    cursor: pointer;
    transition: background 0.3s;
}

.ambient-sound-option.active {
    background: var(--primary-color);
    color: white;
}

.ambient-sound-volume {
    width: 100%;
    margin-top: 10px;
}
