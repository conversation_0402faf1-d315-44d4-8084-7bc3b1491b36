/**
 * performance-renderer.js
 * Rendering engine for the performance-based question system
 */

const PerformanceRenderer = (function() {
  /**
   * Create an element with attributes and children
   * @param {string} tag - HTML tag name
   * @param {Object} attributes - Element attributes
   * @param {Array|string|HTMLElement} children - Child elements or text content
   * @returns {HTMLElement} - The created element
   */
  function createElement(tag, attributes = {}, children = []) {
    const element = document.createElement(tag);
    
    // Set attributes
    for (const [key, value] of Object.entries(attributes)) {
      if (key === 'className') {
        element.className = value;
      } else if (key === 'style' && typeof value === 'object') {
        Object.assign(element.style, value);
      } else if (key.startsWith('on') && typeof value === 'function') {
        const eventName = key.substring(2).toLowerCase();
        element.addEventListener(eventName, value);
      } else if (key === 'dataset' && typeof value === 'object') {
        Object.assign(element.dataset, value);
      } else {
        element.setAttribute(key, value);
      }
    }
    
    // Add children
    if (Array.isArray(children)) {
      children.forEach(child => {
        if (child instanceof HTMLElement) {
          element.appendChild(child);
        } else if (child !== null && child !== undefined) {
          element.appendChild(document.createTextNode(String(child)));
        }
      });
    } else if (children instanceof HTMLElement) {
      element.appendChild(children);
    } else if (children !== null && children !== undefined) {
      element.textContent = String(children);
    }
    
    return element;
  }

  /**
   * Create a button element
   * @param {string} text - Button text
   * @param {Function} onClick - Click handler
   * @param {Object} attributes - Additional attributes
   * @returns {HTMLElement} - The button element
   */
  function createButton(text, onClick, attributes = {}) {
    return createElement('button', {
      ...attributes,
      onClick
    }, text);
  }

  /**
   * Create an icon element
   * @param {string} iconClass - Icon class (Font Awesome)
   * @param {Object} attributes - Additional attributes
   * @returns {HTMLElement} - The icon element
   */
  function createIcon(iconClass, attributes = {}) {
    return createElement('i', {
      className: iconClass,
      ...attributes
    });
  }

  /**
   * Create a draggable item
   * @param {Object} item - Item data
   * @param {Object} attributes - Additional attributes
   * @returns {HTMLElement} - The draggable item element
   */
  function createDraggableItem(item, attributes = {}) {
    const itemElement = createElement('div', {
      className: 'draggable-item',
      draggable: true,
      dataset: { itemId: item.id },
      ...attributes
    });
    
    // Add drag events
    itemElement.addEventListener('dragstart', _handleDragStart);
    itemElement.addEventListener('dragend', _handleDragEnd);
    
    // Add icon if available
    if (item.icon) {
      itemElement.appendChild(createIcon(item.icon));
    }
    
    // Add text
    itemElement.appendChild(document.createTextNode(item.text));
    
    return itemElement;
  }

  /**
   * Create a drop zone
   * @param {Object} target - Target data
   * @param {Object} attributes - Additional attributes
   * @returns {HTMLElement} - The drop zone element
   */
  function createDropZone(target, attributes = {}) {
    const dropZone = createElement('div', {
      className: 'drop-zone',
      dataset: { targetId: target.id },
      ...attributes
    });
    
    // Add drop events
    dropZone.addEventListener('dragover', _handleDragOver);
    dropZone.addEventListener('dragleave', _handleDragLeave);
    dropZone.addEventListener('drop', _handleDrop);
    
    return dropZone;
  }

  /**
   * Create a matching question UI
   * @param {Object} question - Question data
   * @returns {HTMLElement} - The matching question element
   */
  function createMatchingQuestion(question) {
    const { matchingData } = question;
    
    // Create container
    const container = createElement('div', {
      className: 'performance-matching'
    });
    
    // Create items container
    const itemsContainer = createElement('div', {
      className: 'matching-items'
    });
    
    // Create targets container
    const targetsContainer = createElement('div', {
      className: 'matching-targets'
    });
    
    // Add items (shuffled)
    const shuffledItems = PerformanceUtils.shuffleArray([...matchingData.items]);
    shuffledItems.forEach(item => {
      const itemElement = createElement('div', {
        className: 'matching-item',
        draggable: true,
        dataset: { itemId: item.id }
      });
      
      // Add drag events
      itemElement.addEventListener('dragstart', _handleDragStart);
      itemElement.addEventListener('dragend', _handleDragEnd);
      
      // Add icon if available
      if (item.icon) {
        itemElement.appendChild(createIcon(item.icon));
      }
      
      // Add text
      itemElement.appendChild(document.createTextNode(item.text));
      
      itemsContainer.appendChild(itemElement);
    });
    
    // Add targets
    matchingData.targets.forEach(target => {
      const targetElement = createElement('div', {
        className: 'matching-target'
      });
      
      // Add header
      const headerElement = createElement('div', {
        className: 'matching-target-header'
      }, target.text);
      
      // Add description if available
      if (target.description) {
        headerElement.appendChild(createElement('div', {
          className: 'matching-target-description'
        }, target.description));
      }
      
      // Add drop zone
      const dropZone = createElement('div', {
        className: 'matching-drop-zone',
        dataset: { targetId: target.id }
      });
      
      // Add drop events
      dropZone.addEventListener('dragover', _handleDragOver);
      dropZone.addEventListener('dragleave', _handleDragLeave);
      dropZone.addEventListener('drop', _handleDrop);
      
      targetElement.appendChild(headerElement);
      targetElement.appendChild(dropZone);
      
      targetsContainer.appendChild(targetElement);
    });
    
    // Assemble the question
    container.appendChild(itemsContainer);
    container.appendChild(targetsContainer);
    
    return container;
  }

  /**
   * Create an ordering question UI
   * @param {Object} question - Question data
   * @returns {HTMLElement} - The ordering question element
   */
  function createOrderingQuestion(question) {
    const { orderingData } = question;
    
    // Create container
    const container = createElement('div', {
      className: 'ordering-container'
    });
    
    // Create items container
    const itemsContainer = createElement('div', {
      className: 'ordering-items'
    });
    
    // Create target list container
    const targetListContainer = createElement('div', {
      className: 'ordering-target-list'
    });
    
    // Add title to target list
    targetListContainer.appendChild(createElement('h4', {}, 'Correct Order:'));
    
    // Add items (shuffled)
    const shuffledItems = PerformanceUtils.shuffleArray([...orderingData.items]);
    shuffledItems.forEach(item => {
      const itemElement = createElement('div', {
        className: 'ordering-item',
        draggable: true,
        dataset: { itemId: item.id }
      });
      
      // Add drag events
      itemElement.addEventListener('dragstart', _handleDragStart);
      itemElement.addEventListener('dragend', _handleDragEnd);
      
      // Add icon if available
      if (item.icon) {
        itemElement.appendChild(createIcon(item.icon));
      }
      
      // Add text
      itemElement.appendChild(document.createTextNode(item.text));
      
      itemsContainer.appendChild(itemElement);
    });
    
    // Add drop zones for ordering
    for (let i = 0; i < orderingData.items.length; i++) {
      const positionElement = createElement('div', {
        className: 'ordering-position'
      });
      
      // Add position number
      positionElement.appendChild(createElement('div', {
        className: 'ordering-position-number'
      }, i + 1));
      
      // Add drop zone
      const dropZone = createElement('div', {
        className: 'ordering-drop-zone',
        dataset: { position: i }
      });
      
      // Add drop events
      dropZone.addEventListener('dragover', _handleDragOver);
      dropZone.addEventListener('dragleave', _handleDragLeave);
      dropZone.addEventListener('drop', _handleDrop);
      
      positionElement.appendChild(dropZone);
      targetListContainer.appendChild(positionElement);
    }
    
    // Assemble the question
    container.appendChild(itemsContainer);
    container.appendChild(targetListContainer);
    
    return container;
  }

  /**
   * Handle drag start event
   * @param {Event} event - Drag event
   * @private
   */
  function _handleDragStart(event) {
    event.dataTransfer.setData('text/plain', event.target.dataset.itemId);
    event.target.classList.add('dragging');
  }

  /**
   * Handle drag end event
   * @param {Event} event - Drag event
   * @private
   */
  function _handleDragEnd(event) {
    event.target.classList.remove('dragging');
  }

  /**
   * Handle drag over event
   * @param {Event} event - Drag event
   * @private
   */
  function _handleDragOver(event) {
    event.preventDefault();
    event.target.classList.add('drag-over');
  }

  /**
   * Handle drag leave event
   * @param {Event} event - Drag event
   * @private
   */
  function _handleDragLeave(event) {
    event.target.classList.remove('drag-over');
  }

  /**
   * Handle drop event
   * @param {Event} event - Drag event
   * @private
   */
  function _handleDrop(event) {
    event.preventDefault();
    event.target.classList.remove('drag-over');
    
    const itemId = event.dataTransfer.getData('text/plain');
    const itemElement = document.querySelector(`[data-item-id="${itemId}"]`);
    
    if (itemElement) {
      // Move the item to the drop zone
      event.target.appendChild(itemElement);
      
      // Dispatch custom event
      const dropEvent = new CustomEvent('performance:itemDropped', {
        detail: {
          itemId,
          targetId: event.target.dataset.targetId,
          position: event.target.dataset.position
        }
      });
      document.dispatchEvent(dropEvent);
    }
  }

  // Public API
  return {
    createElement,
    createButton,
    createIcon,
    createDraggableItem,
    createDropZone,
    createMatchingQuestion,
    createOrderingQuestion
  };
})();

// Export for use in other modules
window.PerformanceRenderer = PerformanceRenderer;
