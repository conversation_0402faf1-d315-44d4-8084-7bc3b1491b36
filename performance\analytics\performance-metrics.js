/**
 * performance-metrics.js
 * Performance metrics and visualization for the performance-based question system
 */

const PerformanceMetrics = (function() {
  // Private variables
  let _initialized = false;
  
  /**
   * Initialize the metrics module
   * @param {Object} options - Configuration options
   * @returns {Object} - The PerformanceMetrics instance
   */
  function init(options = {}) {
    if (_initialized) {
      console.warn('PerformanceMetrics already initialized');
      return this;
    }
    
    // Set initialized flag
    _initialized = true;
    console.log('PerformanceMetrics initialized');
    
    return this;
  }
  
  /**
   * Calculate user performance metrics
   * @returns {Object} - Performance metrics
   */
  function calculateMetrics() {
    // Check if userData is available in the global scope
    if (typeof userData === 'undefined' || !userData || !userData.performance) {
      console.warn('userData not available or missing performance data');
      return {};
    }
    
    const performance = userData.performance;
    
    // Calculate overall metrics
    const totalQuestions = performance.completedQuestions.length;
    const correctQuestions = performance.correctQuestions.length;
    const incorrectQuestions = performance.incorrectQuestions.length;
    const partiallyCorrectQuestions = performance.partiallyCorrectQuestions.length;
    
    const metrics = {
      overall: {
        totalQuestions,
        correctQuestions,
        incorrectQuestions,
        partiallyCorrectQuestions,
        accuracy: totalQuestions > 0 ? (correctQuestions / totalQuestions) * 100 : 0,
        partialAccuracy: totalQuestions > 0 ? ((correctQuestions + partiallyCorrectQuestions) / totalQuestions) * 100 : 0
      },
      skillMastery: { ...performance.skillMastery },
      byType: {},
      byCategory: {},
      byDifficulty: {},
      timeAnalysis: {
        averageTime: 0,
        fastestTime: Infinity,
        slowestTime: 0
      }
    };
    
    // Skip further calculations if no questions completed
    if (totalQuestions === 0) {
      return metrics;
    }
    
    // Group questions by type, category, and difficulty
    const questionsByType = {};
    const questionsByCategory = {};
    const questionsByDifficulty = {};
    let totalTime = 0;
    let questionCount = 0;
    
    // Process question history
    Object.entries(performance.questionHistory).forEach(([questionId, data]) => {
      // Skip if not a performance question
      if (!data.type) return;
      
      // Group by type
      if (!questionsByType[data.type]) {
        questionsByType[data.type] = {
          total: 0,
          correct: 0,
          incorrect: 0,
          partiallyCorrect: 0,
          totalTime: 0,
          totalScore: 0
        };
      }
      
      questionsByType[data.type].total++;
      if (performance.correctQuestions.includes(questionId)) {
        questionsByType[data.type].correct++;
      } else if (performance.partiallyCorrectQuestions.includes(questionId)) {
        questionsByType[data.type].partiallyCorrect++;
      } else {
        questionsByType[data.type].incorrect++;
      }
      
      // Group by category
      if (data.category) {
        if (!questionsByCategory[data.category]) {
          questionsByCategory[data.category] = {
            total: 0,
            correct: 0,
            incorrect: 0,
            partiallyCorrect: 0,
            totalTime: 0,
            totalScore: 0
          };
        }
        
        questionsByCategory[data.category].total++;
        if (performance.correctQuestions.includes(questionId)) {
          questionsByCategory[data.category].correct++;
        } else if (performance.partiallyCorrectQuestions.includes(questionId)) {
          questionsByCategory[data.category].partiallyCorrect++;
        } else {
          questionsByCategory[data.category].incorrect++;
        }
      }
      
      // Group by difficulty
      if (data.difficulty) {
        if (!questionsByDifficulty[data.difficulty]) {
          questionsByDifficulty[data.difficulty] = {
            total: 0,
            correct: 0,
            incorrect: 0,
            partiallyCorrect: 0,
            totalTime: 0,
            totalScore: 0
          };
        }
        
        questionsByDifficulty[data.difficulty].total++;
        if (performance.correctQuestions.includes(questionId)) {
          questionsByDifficulty[data.difficulty].correct++;
        } else if (performance.partiallyCorrectQuestions.includes(questionId)) {
          questionsByDifficulty[data.difficulty].partiallyCorrect++;
        } else {
          questionsByDifficulty[data.difficulty].incorrect++;
        }
      }
      
      // Time analysis
      if (data.lastResult && data.lastResult.timeSpent) {
        const timeSpent = data.lastResult.timeSpent;
        
        // Update type time
        questionsByType[data.type].totalTime += timeSpent;
        questionsByType[data.type].totalScore += data.lastResult.score || 0;
        
        // Update category time
        if (data.category) {
          questionsByCategory[data.category].totalTime += timeSpent;
          questionsByCategory[data.category].totalScore += data.lastResult.score || 0;
        }
        
        // Update difficulty time
        if (data.difficulty) {
          questionsByDifficulty[data.difficulty].totalTime += timeSpent;
          questionsByDifficulty[data.difficulty].totalScore += data.lastResult.score || 0;
        }
        
        // Update overall time stats
        totalTime += timeSpent;
        questionCount++;
        
        if (timeSpent < metrics.timeAnalysis.fastestTime) {
          metrics.timeAnalysis.fastestTime = timeSpent;
        }
        
        if (timeSpent > metrics.timeAnalysis.slowestTime) {
          metrics.timeAnalysis.slowestTime = timeSpent;
        }
      }
    });
    
    // Calculate metrics by type
    Object.entries(questionsByType).forEach(([type, data]) => {
      metrics.byType[type] = {
        totalQuestions: data.total,
        correctQuestions: data.correct,
        incorrectQuestions: data.incorrect,
        partiallyCorrectQuestions: data.partiallyCorrect,
        accuracy: data.total > 0 ? (data.correct / data.total) * 100 : 0,
        partialAccuracy: data.total > 0 ? ((data.correct + data.partiallyCorrect) / data.total) * 100 : 0,
        averageTime: data.total > 0 ? data.totalTime / data.total : 0,
        averageScore: data.total > 0 ? data.totalScore / data.total : 0
      };
    });
    
    // Calculate metrics by category
    Object.entries(questionsByCategory).forEach(([category, data]) => {
      metrics.byCategory[category] = {
        totalQuestions: data.total,
        correctQuestions: data.correct,
        incorrectQuestions: data.incorrect,
        partiallyCorrectQuestions: data.partiallyCorrect,
        accuracy: data.total > 0 ? (data.correct / data.total) * 100 : 0,
        partialAccuracy: data.total > 0 ? ((data.correct + data.partiallyCorrect) / data.total) * 100 : 0,
        averageTime: data.total > 0 ? data.totalTime / data.total : 0,
        averageScore: data.total > 0 ? data.totalScore / data.total : 0
      };
    });
    
    // Calculate metrics by difficulty
    Object.entries(questionsByDifficulty).forEach(([difficulty, data]) => {
      metrics.byDifficulty[difficulty] = {
        totalQuestions: data.total,
        correctQuestions: data.correct,
        incorrectQuestions: data.incorrect,
        partiallyCorrectQuestions: data.partiallyCorrect,
        accuracy: data.total > 0 ? (data.correct / data.total) * 100 : 0,
        partialAccuracy: data.total > 0 ? ((data.correct + data.partiallyCorrect) / data.total) * 100 : 0,
        averageTime: data.total > 0 ? data.totalTime / data.total : 0,
        averageScore: data.total > 0 ? data.totalScore / data.total : 0
      };
    });
    
    // Calculate overall time metrics
    metrics.timeAnalysis.averageTime = questionCount > 0 ? totalTime / questionCount : 0;
    
    // Handle case where no questions have time data
    if (metrics.timeAnalysis.fastestTime === Infinity) {
      metrics.timeAnalysis.fastestTime = 0;
    }
    
    return metrics;
  }
  
  /**
   * Generate a performance report
   * @param {string} containerId - ID of the container element
   * @returns {HTMLElement} - The report element
   */
  function generateReport(containerId) {
    const container = document.getElementById(containerId);
    if (!container) {
      console.error(`Container element with ID "${containerId}" not found`);
      return null;
    }
    
    // Calculate metrics
    const metrics = calculateMetrics();
    
    // Create report element
    const reportElement = document.createElement('div');
    reportElement.className = 'performance-report';
    
    // Add overall metrics
    const overallSection = document.createElement('div');
    overallSection.className = 'performance-report-section';
    overallSection.innerHTML = `
      <h3>Overall Performance</h3>
      <div class="performance-stats">
        <div class="performance-stat">
          <div class="performance-stat-value">${metrics.overall.totalQuestions}</div>
          <div class="performance-stat-label">Total Questions</div>
        </div>
        <div class="performance-stat">
          <div class="performance-stat-value">${metrics.overall.correctQuestions}</div>
          <div class="performance-stat-label">Correct</div>
        </div>
        <div class="performance-stat">
          <div class="performance-stat-value">${metrics.overall.partiallyCorrectQuestions}</div>
          <div class="performance-stat-label">Partially Correct</div>
        </div>
        <div class="performance-stat">
          <div class="performance-stat-value">${metrics.overall.incorrectQuestions}</div>
          <div class="performance-stat-label">Incorrect</div>
        </div>
        <div class="performance-stat">
          <div class="performance-stat-value">${Math.round(metrics.overall.accuracy)}%</div>
          <div class="performance-stat-label">Accuracy</div>
        </div>
      </div>
    `;
    reportElement.appendChild(overallSection);
    
    // Add skill mastery section
    const skillSection = document.createElement('div');
    skillSection.className = 'performance-report-section';
    skillSection.innerHTML = `
      <h3>Skill Mastery</h3>
      <div class="performance-skill-mastery">
        ${Object.entries(metrics.skillMastery).map(([skill, mastery]) => `
          <div class="performance-skill">
            <div class="performance-skill-label">${skill.charAt(0).toUpperCase() + skill.slice(1)}</div>
            <div class="performance-skill-bar">
              <div class="performance-skill-progress" style="width: ${Math.round(mastery * 100)}%"></div>
            </div>
            <div class="performance-skill-value">${Math.round(mastery * 100)}%</div>
          </div>
        `).join('')}
      </div>
    `;
    reportElement.appendChild(skillSection);
    
    // Add type breakdown
    if (Object.keys(metrics.byType).length > 0) {
      const typeSection = document.createElement('div');
      typeSection.className = 'performance-report-section';
      typeSection.innerHTML = `
        <h3>Performance by Question Type</h3>
        <table class="performance-table">
          <thead>
            <tr>
              <th>Type</th>
              <th>Questions</th>
              <th>Accuracy</th>
              <th>Avg. Score</th>
              <th>Avg. Time</th>
            </tr>
          </thead>
          <tbody>
            ${Object.entries(metrics.byType).map(([type, data]) => `
              <tr>
                <td>${type.charAt(0).toUpperCase() + type.slice(1)}</td>
                <td>${data.totalQuestions}</td>
                <td>${Math.round(data.accuracy)}%</td>
                <td>${Math.round(data.averageScore)}%</td>
                <td>${Math.round(data.averageTime / 1000)} sec</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      `;
      reportElement.appendChild(typeSection);
    }
    
    // Add to container
    container.innerHTML = '';
    container.appendChild(reportElement);
    
    return reportElement;
  }
  
  /**
   * Create a chart for performance data
   * @param {string} canvasId - ID of the canvas element
   * @param {string} chartType - Type of chart to create
   * @returns {Object|null} - Chart object or null if failed
   */
  function createChart(canvasId, chartType = 'category') {
    // Check if Chart.js is available
    if (typeof Chart === 'undefined') {
      console.error('Chart.js is not available');
      return null;
    }
    
    const canvas = document.getElementById(canvasId);
    if (!canvas) {
      console.error(`Canvas element with ID "${canvasId}" not found`);
      return null;
    }
    
    // Calculate metrics
    const metrics = calculateMetrics();
    
    // Create chart based on type
    let chartData = {};
    let chartOptions = {};
    
    if (chartType === 'category') {
      // Category performance chart
      const categories = Object.keys(metrics.byCategory);
      const accuracyData = categories.map(cat => Math.round(metrics.byCategory[cat].accuracy));
      
      chartData = {
        labels: categories,
        datasets: [{
          label: 'Accuracy (%)',
          data: accuracyData,
          backgroundColor: 'rgba(54, 162, 235, 0.5)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        }]
      };
      
      chartOptions = {
        scales: {
          y: {
            beginAtZero: true,
            max: 100
          }
        }
      };
    } else if (chartType === 'type') {
      // Question type performance chart
      const types = Object.keys(metrics.byType);
      const accuracyData = types.map(type => Math.round(metrics.byType[type].accuracy));
      
      chartData = {
        labels: types.map(type => type.charAt(0).toUpperCase() + type.slice(1)),
        datasets: [{
          label: 'Accuracy (%)',
          data: accuracyData,
          backgroundColor: 'rgba(75, 192, 192, 0.5)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1
        }]
      };
      
      chartOptions = {
        scales: {
          y: {
            beginAtZero: true,
            max: 100
          }
        }
      };
    } else if (chartType === 'radar') {
      // Skill mastery radar chart
      const skills = Object.keys(metrics.skillMastery);
      const masteryData = skills.map(skill => Math.round(metrics.skillMastery[skill] * 100));
      
      chartData = {
        labels: skills.map(skill => skill.charAt(0).toUpperCase() + skill.slice(1)),
        datasets: [{
          label: 'Skill Mastery (%)',
          data: masteryData,
          backgroundColor: 'rgba(255, 99, 132, 0.2)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1
        }]
      };
      
      chartOptions = {
        scales: {
          r: {
            angleLines: {
              display: true
            },
            suggestedMin: 0,
            suggestedMax: 100
          }
        }
      };
    }
    
    // Create chart
    return new Chart(canvas, {
      type: chartType === 'radar' ? 'radar' : 'bar',
      data: chartData,
      options: chartOptions
    });
  }
  
  // Public API
  return {
    init,
    calculateMetrics,
    generateReport,
    createChart
  };
})();

// Export for use in other modules
window.PerformanceMetrics = PerformanceMetrics;
