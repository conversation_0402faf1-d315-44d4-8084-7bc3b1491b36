# Quiz Application Enhancements

This document outlines the new gamification and focus-enhancing features added to the quiz application.

## Gamification Features

### Achievement System
- Tracks and rewards user accomplishments
- Unlockable badges for completing specific milestones
- Achievement modal to view progress and locked achievements

### Experience Points (XP) and Levels
- Earn XP for correct answers, completing quizzes, and other activities
- Level up as you accumulate XP
- Visual XP bar shows progress to next level

### Streak Counter
- Tracks consecutive days of quiz completion
- Encourages regular practice and consistency
- Rewards for maintaining streaks

## Focus-Enhancing Features

### Pomodoro Timer
- Built-in study timer with focused work periods and short breaks
- Customizable work and break durations
- Notifications when periods end

### Focus Mode
- Distraction-free interface during quiz sessions
- Hides unnecessary UI elements to improve concentration
- Toggle on/off as needed

### Ambient Sounds
- Background audio options to help with focus
- Includes rain, coffee shop, nature, and lo-fi music options
- Adjustable volume control

## Visual Enhancements

### Theme Customization
- Multiple visual themes to personalize the experience
- Themes include: Standard, Cyberpunk, Nature, Space, and Retro
- Each theme has unique colors and visual styles

### Weekly Progress Bar
- Visual representation of weekly progress
- Resets with the 7-day timer for quiz questions
- Shows percentage of questions completed

## Interactive Question Types

### Drag and Drop Questions
- Interactive questions with draggable elements
- Match items to their correct locations
- Visual feedback for correct and incorrect answers

### Matching Questions
- Connect related items with visual lines
- Test knowledge of relationships between concepts
- Supports multiple correct matches per item

## How to Use

### Accessing New Features
1. **Theme Selection**: Click the "Change Theme" button on the home screen
2. **Focus Tools**: Click the "Focus Tools" button to access Pomodoro timer and ambient sounds
3. **Achievements**: Click the "Achievements" button to view your progress
4. **Interactive Questions**: Select the "Interactive Challenges" quiz mode

### Gamification Elements
- XP and level are displayed at the top of the quiz interface
- Streak counter shows your current daily streak
- Achievements are unlocked automatically as you meet requirements

### Focus Tools
- **Pomodoro Timer**: Set work/break durations and start the timer
- **Focus Mode**: Toggle to hide distractions during study sessions
- **Ambient Sounds**: Select a background sound and adjust volume

## Technical Implementation

The enhancements are implemented across several files:
- `gamification.js`: Handles achievements, XP, and streaks
- `focus_tools.js`: Implements Pomodoro timer and ambient sounds
- `interactive_questions.js`: Provides drag-and-drop and matching question functionality
- `themes.css`: Contains theme styles and variations

User data is saved to localStorage, including:
- Achievement progress
- XP and level information
- Streak data
- Theme preferences

## Future Enhancements

Potential future improvements:
- Leaderboards for competitive motivation
- More interactive question types
- Additional themes and customization options
- Social sharing of achievements
- Expanded achievement system with more badges
