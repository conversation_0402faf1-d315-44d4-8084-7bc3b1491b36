/**
 * performance-integration.js
 * Integration of the performance-based question system with the main quiz application
 */

document.addEventListener('DOMContentLoaded', function() {
  // Initialize performance system when document is ready
  if (window.PerformanceCore) {
    console.log('Initializing Performance System...');

    // Initialize core modules
    PerformanceCore.init({
      showHints: true,
      allowPartialCredit: true,
      adaptiveDifficulty: false,
      showExplanations: true
    });

    // Initialize UI
    if (window.PerformanceUI) {
      PerformanceUI.init('performanceQuestionContainer');
    }

    // Initialize analytics
    if (window.PerformanceAnalytics) {
      PerformanceAnalytics.init();
    }

    // Initialize metrics
    if (window.PerformanceMetrics) {
      PerformanceMetrics.init();
    }

    // Initialize tracking UI
    if (window.PerformanceTrackingUI) {
      PerformanceTrackingUI.init('performanceTrackingContainer');
    }

    // Load questions
    PerformanceCore.loadQuestions('performance/data/performance_questions.json')
      .then(questions => {
        console.log(`Loaded ${questions.length} performance questions`);
      })
      .catch(error => {
        console.error('Error loading performance questions:', error);
      });

    // Add performance mode to quiz modes
    addPerformanceMode();

    // Listen for next question events
    document.addEventListener('performance:nextQuestion', handleNextPerformanceQuestion);
  }
});

/**
 * Add performance mode to quiz modes
 */
function addPerformanceMode() {
  // Check if quiz modes container exists
  const quizModesContainer = document.querySelector('.quiz-modes');
  if (!quizModesContainer) {
    console.warn('Quiz modes container not found');
    return;
  }

  // Create performance mode card
  const performanceModeCard = document.createElement('div');
  performanceModeCard.className = 'quiz-mode-card';
  performanceModeCard.dataset.mode = 'performance';
  performanceModeCard.innerHTML = `
    <h3>Performance Based</h3>
    <p>Test your skills with interactive drag-and-drop and ordering exercises.</p>
    <div class="category-badge">Interactive</div>
  `;

  // Add click event
  performanceModeCard.addEventListener('click', function() {
    selectQuizMode('performance');
  });

  // Add to container
  quizModesContainer.appendChild(performanceModeCard);

  // Add performance container to the DOM if it doesn't exist
  if (!document.getElementById('performanceQuestionContainer')) {
    const performanceContainer = document.createElement('div');
    performanceContainer.id = 'performanceQuestionContainer';
    performanceContainer.className = 'performance-container hidden';

    // Add after quiz container
    const quizContainer = document.querySelector('.quiz-container');
    if (quizContainer && quizContainer.parentNode) {
      quizContainer.parentNode.insertBefore(performanceContainer, quizContainer.nextSibling);
    } else {
      // Fallback to body
      document.body.appendChild(performanceContainer);
    }
  }

  console.log('Performance mode added to quiz modes');
}

/**
 * Select quiz mode
 * @param {string} mode - Quiz mode
 */
function selectQuizMode(mode) {
  // Check if this is the original function
  if (typeof window.originalSelectQuizMode === 'function') {
    // Call original function for non-performance modes
    if (mode !== 'performance') {
      window.originalSelectQuizMode(mode);
      return;
    }
  } else if (typeof window.selectQuizMode === 'function' && window.selectQuizMode !== selectQuizMode) {
    // Save original function
    window.originalSelectQuizMode = window.selectQuizMode;

    // Override global function
    window.selectQuizMode = selectQuizMode;

    // Call original function for non-performance modes
    if (mode !== 'performance') {
      window.originalSelectQuizMode(mode);
      return;
    }
  }

  // Handle performance mode
  if (mode === 'performance') {
    console.log('Selecting performance mode');

    // Update current mode
    if (typeof window.currentMode !== 'undefined') {
      window.currentMode = 'performance';
    }

    // Get question count from main quiz settings
    let questionsCount = 10; // Default
    if (typeof window.quizSettings !== 'undefined' && window.quizSettings.questionsCount) {
      questionsCount = window.quizSettings.questionsCount;
    } else {
      // Try to get from question count input
      const questionCountInput = document.getElementById('questionCount');
      if (questionCountInput && questionCountInput.value) {
        questionsCount = parseInt(questionCountInput.value) || 10;
      }
    }

    console.log(`Starting performance session with ${questionsCount} questions`);

    // Use the showView function to handle container visibility
    if (typeof window.showView === 'function') {
      window.showView('performance');
    } else {
      // Fallback to direct DOM manipulation if showView is not available
      document.querySelectorAll('.home-container, .quiz-container, .stats-container, .select-question-container, .acronyms-container, .user-container').forEach(container => {
        container.classList.add('hidden');
      });

      // Show performance container
      const performanceContainer = document.getElementById('performanceQuestionContainer');
      const trackingContainer = document.getElementById('performanceTrackingContainer');

      if (performanceContainer) {
        performanceContainer.classList.remove('hidden');
      }

      if (trackingContainer) {
        trackingContainer.classList.remove('hidden');
      }
    }

    // Start a new performance session with the specified question count
    startPerformanceSession(questionsCount);
  }
}

/**
 * Start a new performance session
 * @param {number} questionsCount - Number of questions for the session
 */
function startPerformanceSession(questionsCount = 10) {
  if (!window.PerformanceCore) {
    console.error('PerformanceCore not available');
    return;
  }

  // Start the session
  const success = PerformanceCore.startSession(questionsCount);

  if (success) {
    // Set up event listeners for session events
    setupSessionEventListeners();

    // Update progress bar
    updatePerformanceProgressBar();

    // Update tracking display
    if (window.PerformanceTrackingUI) {
      PerformanceTrackingUI.updateTrackingDisplay();
    }

    console.log(`Performance session started with ${questionsCount} questions`);
  } else {
    console.error('Failed to start performance session');
  }
}

/**
 * Set up event listeners for session events
 */
function setupSessionEventListeners() {
  if (!window.PerformanceState) {
    return;
  }

  // Listen for session progress updates
  PerformanceState.subscribe('session:progress', (data) => {
    console.log('Session progress:', data);
    updatePerformanceProgressBar();

    // Update question number display if available
    const questionNumber = document.getElementById('questionNumber');
    if (questionNumber) {
      questionNumber.textContent = `Question ${data.currentIndex + 1}/${data.totalQuestions}`;
    }
  });

  // Listen for session end
  PerformanceState.subscribe('session:end', (data) => {
    console.log('Session ended:', data);
    handleSessionEnd(data);
  });

  // Listen for answer submissions to advance to next question
  PerformanceState.subscribe('answer:submit', (data) => {
    console.log('Answer submitted:', data);

    // Wait a moment for feedback, then move to next question
    setTimeout(() => {
      const hasNext = PerformanceCore.nextQuestion();
      if (!hasNext) {
        console.log('Session completed - no more questions');
      }
    }, 2000); // 2 second delay for user to see feedback
  });
}

/**
 * Handle session end
 * @param {Object} sessionStats - Session statistics
 */
function handleSessionEnd(sessionStats) {
  console.log('Performance session completed:', sessionStats);

  // Show completion message
  const message = `Session Complete!\n\nQuestions: ${sessionStats.questionsCompleted}/${sessionStats.totalQuestions}\nCorrect: ${sessionStats.correctAnswers}\nIncorrect: ${sessionStats.incorrectAnswers}\nPartially Correct: ${sessionStats.partiallyCorrect}`;

  // Use the main quiz system's feedback mechanism if available
  if (typeof window.showFeedback === 'function') {
    window.showFeedback(message);
  } else {
    alert(message);
  }

  // Update progress tracking
  updatePerformanceProgressBar();

  // Integrate with main quiz statistics
  integratePerformanceStats();

  // Return to home after a delay
  setTimeout(() => {
    if (typeof window.showView === 'function') {
      window.showView('home');
    }
  }, 3000);
}

/**
 * Load a random performance question (legacy function - now handled by session)
 */
function loadRandomPerformanceQuestion() {
  console.warn('loadRandomPerformanceQuestion is deprecated - use startPerformanceSession instead');
  startPerformanceSession(1);
}

/**
 * Handle next performance question event (legacy function)
 */
function handleNextPerformanceQuestion() {
  console.warn('handleNextPerformanceQuestion is deprecated - session handles progression automatically');
  PerformanceCore.nextQuestion();
}

/**
 * Update progress bar for performance questions
 */
function updatePerformanceProgressBar() {
  // Get session progress
  const sessionProgress = PerformanceCore.getSessionProgress();

  // Update main progress bar if available
  const progressBar = document.getElementById('progressBar');
  if (progressBar && sessionProgress.isActive) {
    progressBar.style.width = `${sessionProgress.progress}%`;

    // Change color based on progress
    if (sessionProgress.progress < 30) {
      progressBar.style.backgroundColor = 'var(--warning-color)';
    } else if (sessionProgress.progress < 70) {
      progressBar.style.backgroundColor = 'var(--primary-color)';
    } else {
      progressBar.style.backgroundColor = 'var(--success-color)';
    }
  }

  // Update question number display
  const questionNumber = document.getElementById('questionNumber');
  if (questionNumber && sessionProgress.isActive) {
    questionNumber.textContent = `Question ${sessionProgress.currentIndex + 1}/${sessionProgress.totalQuestions}`;
  }

  // Also update weekly progress bar with overall performance metrics
  const weeklyProgressBar = document.getElementById('weeklyProgressBar');
  if (weeklyProgressBar && window.PerformanceMetrics) {
    try {
      const metrics = PerformanceMetrics.calculateMetrics();
      const totalQuestions = metrics.overall.totalQuestions;
      const completedQuestions = metrics.overall.correctQuestions + metrics.overall.partiallyCorrectQuestions;
      const progressPercentage = totalQuestions > 0 ? (completedQuestions / totalQuestions) * 100 : 0;
      weeklyProgressBar.style.width = `${progressPercentage}%`;
    } catch (error) {
      console.warn('Error updating weekly progress bar:', error);
    }
  }
}

/**
 * Integrate performance data with user statistics
 */
function integratePerformanceStats() {
  // Check if userData is available
  if (typeof userData === 'undefined' || !userData) {
    console.warn('userData not available');
    return;
  }

  // Ensure stats structure exists
  if (!userData.stats) {
    userData.stats = {
      overall: { totalQuestions: 0, correctAnswers: 0, totalTime: 0, quizzesTaken: 0 },
      A: { totalQuestions: 0, correctAnswers: 0, totalTime: 0 },
      B: { totalQuestions: 0, correctAnswers: 0, totalTime: 0 },
      C: { totalQuestions: 0, correctAnswers: 0, totalTime: 0 },
      P: { totalQuestions: 0, correctAnswers: 0, totalTime: 0 } // Performance category
    };
  }

  // Ensure P category exists
  if (!userData.stats.P) {
    userData.stats.P = { totalQuestions: 0, correctAnswers: 0, totalTime: 0 };
  }

  // Get performance metrics
  const metrics = PerformanceMetrics.calculateMetrics();

  // Update P category stats
  userData.stats.P.totalQuestions = metrics.overall.totalQuestions;
  userData.stats.P.correctAnswers = metrics.overall.correctQuestions;

  // Calculate total time from question history
  let totalTime = 0;
  if (userData.performance && userData.performance.questionHistory) {
    Object.values(userData.performance.questionHistory).forEach(data => {
      if (data.lastResult && data.lastResult.timeSpent) {
        totalTime += data.lastResult.timeSpent;
      }
    });
  }
  userData.stats.P.totalTime = totalTime;

  // Update overall stats
  userData.stats.overall.totalQuestions += metrics.overall.totalQuestions;
  userData.stats.overall.correctAnswers += metrics.overall.correctQuestions;
  userData.stats.overall.totalTime += totalTime;

  // Save user data if saveUserData function is available
  if (typeof saveUserData === 'function' && typeof getCurrentNickname === 'function') {
    const nickname = getCurrentNickname();
    if (nickname) {
      saveUserData(nickname);
    }
  }
}

/**
 * Show performance report
 */
function showPerformanceReport() {
  // Check if tracking UI is available
  if (!window.PerformanceTrackingUI) {
    console.warn('PerformanceTrackingUI not available');
    return;
  }

  // Get tracking container
  const trackingContainer = document.getElementById('performanceTrackingContainer');
  if (!trackingContainer) {
    console.warn('Performance tracking container not found');
    return;
  }

  // Show tracking container
  trackingContainer.classList.remove('hidden');

  // Update tracking display
  PerformanceTrackingUI.updateTrackingDisplay();

  // Toggle detailed report
  const toggleBtn = document.getElementById('showDetailedReportBtn');
  if (toggleBtn) {
    toggleBtn.click();
  }
}

// Export functions for use in other modules
window.performanceIntegration = {
  selectQuizMode,
  startPerformanceSession,
  setupSessionEventListeners,
  handleSessionEnd,
  loadRandomPerformanceQuestion, // Legacy
  handleNextPerformanceQuestion, // Legacy
  updatePerformanceProgressBar,
  integratePerformanceStats,
  showPerformanceReport
};
